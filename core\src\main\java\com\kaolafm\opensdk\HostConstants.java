package com.kaolafm.opensdk;

import com.kaolafm.core.BuildConfig;
/**
 *
 * <AUTHOR>
 * @date 2020-03-13
 */
public class HostConstants {


//    public static final String OPEN_KAOLA_HOST = "iovopen.radio.cn";
//    public static final String SOKET_HOST = "iovwsopen.radio.cn";
//    public static final String MALL_HOST = "iovmall.radio.cn";
//    public static final String REPORT_HOST = "iovmsg.radio.cn";
//    public static final String SEARCH_HTTPS_HOST = "iovsearch.radio.cn";
//    public static final String SEARCH_HTTP_HOST = "api.search.kaolafm.com";


//    public static final String OPEN_KAOLA_HOST = "iovopen-prealese.radio.cn";
//    public static final String SOKET_HOST = "iovwsopen-prealese.radio.cn";
//    public static final String MALL_HOST = "iovmall-prealese.radio.cn";
//    public static final String REPORT_HOST = "iovmsg.radio.cn";
//    public static final String SEARCH_HTTPS_HOST = "iovsearch.radio.cn";
//    public static final String SEARCH_HTTP_HOST = "api.search.kaolafm.com";

    // attention：这些域名的test接口目前还没有，必须打release包才能正常获取数据
    public static final String OPEN_KAOLA_HOST = BuildConfig.DOMAIN_TYPE.contains("test") ? "iovopen-test.radio.cn" : "open"+BuildConfig.DOMAIN_TYPE+".kaolafm.com";
    public static final String SOCKET_HOST = (BuildConfig.DOMAIN_TYPE.contains("test") ? "iovws-test.radio.cn/" : "ws" +BuildConfig.DOMAIN_TYPE+ ".kaolafm.com/")+ (BuildConfig.API_VERSION.equals("v2") ? "open" : "internal");
    public static final String MALL_HOST = BuildConfig.DOMAIN_TYPE.contains("test") ? "iovmall-test.radio.cn" : "shop"+BuildConfig.DOMAIN_TYPE+".kaolafm.com";
    public static final String REPORT_HOST = "msg.kaolafm.com";
    public static final String SEARCH_HTTPS_HOST = BuildConfig.DOMAIN_TYPE.contains("test") ? "iovsearch-test.radio.cn" : "search"+BuildConfig.DOMAIN_TYPE+".kaolafm.com";
    public static final String EMERGENCY_HTTPS_HOST = BuildConfig.DOMAIN_TYPE.contains("test") ?"iovrec-test.radio.cn"  : "rec"+BuildConfig.DOMAIN_TYPE+".kaolafm.com";
    public static final String SEARCH_HTTP_HOST = "api.search.kaolafm.com";

}
