package com.kaolafm.base.utils;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;

import java.lang.ref.WeakReference;
import java.util.ArrayList;

public class NetworkMonitor {

    public interface OnNetworkStatusChangedListener {
        void onStatusChanged(int newStatus, int oldStatus);
    }

    private WeakReference<Context> mContextWeakReference;

    public static final int STATUS_NO_NETWORK = 0;
    public static final int STATUS_WIFI = 1;
    public static final int STATUS_MOBILE = 2;


    private int mNetStatus = STATUS_NO_NETWORK;

    private static class NETWORK_MONITOR_CLASS {
        private static final NetworkMonitor NETWORK_MONITOR_INSTANCE = new NetworkMonitor();
    }

    public static NetworkMonitor getInstance(Context context) {
        if (NETWORK_MONITOR_CLASS.NETWORK_MONITOR_INSTANCE.mContextWeakReference == null) {
            NETWORK_MONITOR_CLASS.NETWORK_MONITOR_INSTANCE.mContextWeakReference = new WeakReference<>(context instanceof Activity ?
                    context.getApplicationContext() : context);
            NETWORK_MONITOR_CLASS.NETWORK_MONITOR_INSTANCE.init(context);
        }
        return NETWORK_MONITOR_CLASS.NETWORK_MONITOR_INSTANCE;
    }

    private NetworkMonitor() {
    }

    private ArrayList<OnNetworkStatusChangedListener> mListeners = new ArrayList<>();

    public void registerNetworkStatusChangeListener(OnNetworkStatusChangedListener listener) {
        if (listener != null && !mListeners.contains(listener)) {
            if (mListeners.size() == 0) {
                IntentFilter filter = new IntentFilter(ConnectivityManager.CONNECTIVITY_ACTION);
                Context context = mContextWeakReference.get();
                if (context != null) {
                    context.registerReceiver(mReceiver, filter);
                }
            }
            mListeners.add(listener);
        }
    }

    public void removeNetworkStatusChangeListener(OnNetworkStatusChangedListener listener) {
        if (mListeners.contains(listener)) {
            mListeners.remove(listener);
            Context context = mContextWeakReference.get();
            if (mListeners.size() == 0 && context != null) {
                context.unregisterReceiver(mReceiver);
            }
        }
    }

    private void init(Context context) {
        if (mConnectivityManager == null) {
            mConnectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        }
        mNetStatus = populateNetworkStatus();
    }

    private boolean isNetStatusChanged() {
        int status = populateNetworkStatus();
        if (mNetStatus != status) {
            mNetStatus = status;
            return true;
        }
        return false;
    }

    private int populateNetworkStatus() {
        NetworkInfo info = mConnectivityManager.getActiveNetworkInfo();
        if (info != null && info.isConnected()) {
            switch (info.getType()) {
                case ConnectivityManager.TYPE_MOBILE:
                case ConnectivityManager.TYPE_MOBILE_DUN:
                case ConnectivityManager.TYPE_MOBILE_HIPRI:
                case ConnectivityManager.TYPE_MOBILE_MMS:
                case ConnectivityManager.TYPE_MOBILE_SUPL:
                    return STATUS_MOBILE;
                case ConnectivityManager.TYPE_WIFI:
                case ConnectivityManager.TYPE_ETHERNET:
                case ConnectivityManager.TYPE_WIMAX:
                    return STATUS_WIFI;
            }
        }
        return STATUS_NO_NETWORK;
    }

    private ConnectivityManager mConnectivityManager;
    private BroadcastReceiver mReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            int status = mNetStatus;
            if (intent.getAction().equals(ConnectivityManager.CONNECTIVITY_ACTION) && isNetStatusChanged()) {
                if (mListeners.size() > 0) {
                    ArrayList<OnNetworkStatusChangedListener> listeners = (ArrayList<OnNetworkStatusChangedListener>) mListeners.clone();
                    for (int i = 0, size = listeners.size(); i < size; i++) {
                        OnNetworkStatusChangedListener listener = listeners.get(i);
                        listener.onStatusChanged(mNetStatus, status);
                    }
                }
            }
        }
    };
}
