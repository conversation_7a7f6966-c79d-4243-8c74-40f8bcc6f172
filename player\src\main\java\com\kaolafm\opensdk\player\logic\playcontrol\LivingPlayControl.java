package com.kaolafm.opensdk.player.logic.playcontrol;

import android.util.Log;

import com.kaolafm.opensdk.api.media.model.AudioFileInfo;
import com.kaolafm.opensdk.api.media.model.ToneQuality;
import com.kaolafm.opensdk.player.core.ijk.VideoView;
import com.kaolafm.opensdk.player.logic.model.item.LivePlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerPreconditions;

import java.util.List;

/**
 * 直播播放控制
 * do 直播间类型直播 此类没有用 startTimer() 方式刷新进度，在 IJKMediaPlayerAdapter 已经进行了处理。
 * <AUTHOR> shi qian
 */
public class LivingPlayControl extends BasePlayControl {

    @Override
    public void play() {
        if (PlayerPreconditions.checkNull(mPlayerBinder) || mPlayItem == null) {
            return;
        }
        mPlayerBinder.start(mPlayItem.getPlayUrl(), 0, 0, true, null, mPlayItem.getPlayUrlId());
    }

    @Override
    public void pause() {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        mPlayerBinder.pause();
    }

    @Override
    public void start(int type, PlayItem playItem, VideoView videoView, boolean isPlayNow) {
        super.start(type, playItem, videoView, isPlayNow);
//        LivePlayItem livePlayItem = (LivePlayItem) playItem;
//        if(livePlayItem.getStatus()==1){
//            super.start(type, playItem, videoView, isPlayNow);
//        }
    }


    @Override
    ToneQuality getToneQuality(PlayItem playItem, List<AudioFileInfo> playListUrlInfos) {
        return null;
    }

    @Override
    void requestPlayUrl(PlayItem playItem, OnGetPlayUrlData callback) {
        //设置并回调播放地址
        if (playItem instanceof LivePlayItem) {
            LivePlayItem livePlayItem = (LivePlayItem) playItem;
            setPlayUrl(playItem, livePlayItem.getPlayListUrlInfo());
            callback.onDataGet(playItem.getPlayUrl());
        } else {
            Log.w(getClass().getSimpleName(), "target playItem not instance of LivePlayItem!!!!");
        }
    }

    @Override
    String getPlayItemResourceType(PlayItem playItem) {
        return "m3u8";
    }
}
