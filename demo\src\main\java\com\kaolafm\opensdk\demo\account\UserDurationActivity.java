package com.kaolafm.opensdk.demo.account;

import android.annotation.SuppressLint;
import androidx.appcompat.app.AppCompatActivity;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.widget.TextView;

import com.kaolafm.opensdk.account.token.AccessTokenManager;
import com.kaolafm.opensdk.api.login.LoginRequest;
import com.kaolafm.opensdk.api.login.model.CumPlaytimeInfo;
import com.kaolafm.opensdk.demo.BaseActivity;
import com.kaolafm.opensdk.demo.R;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.report.util.ReportParameterManager;

import butterknife.BindView;

/**
 * 用户收听时长
 */
public class UserDurationActivity extends BaseActivity {
    @BindView(R.id.duration_tv)
    TextView durationTv;
    @BindView(R.id.duration_tv2)
    TextView durationTv2;
    @BindView(R.id.duration_tv3)
    TextView durationTv3;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_user_duration;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        setTitle("云听账户收听时长");
    }

    @Override
    public void initData() {
        new LoginRequest().getCumPlaytime( AccessTokenManager.getInstance().getKaolaAccessToken().getUserId()
                , ReportParameterManager.getInstance().getReportParameter().getAppid(), new HttpCallback<CumPlaytimeInfo>() {
                    @SuppressLint("SetTextI18n")
                    @Override
                    public void onSuccess(CumPlaytimeInfo cumPlaytimeInfo) {
                        if (cumPlaytimeInfo.getUid() != null) {


                            durationTv.setText("本月累计收听" + userDurationFormat(cumPlaytimeInfo.getMonthCumulative()) + "小时--" + cumPlaytimeInfo.getMonthCumulative());

                            if (!TextUtils.isEmpty(cumPlaytimeInfo.getListenRank()))
                                durationTv2.setText("你已经超过了 " + cumPlaytimeInfo.getListenRank() + "的人");

                            durationTv3.setText("总收听时长：" + userDurationFormat(cumPlaytimeInfo.getCumulativeDuration()) + "h----" + cumPlaytimeInfo.getCumulativeDuration());
                        }
                    }

                    @Override
                    public void onError(ApiException e) {
                        Log.e("UserDurationActivity", e.getMessage());
                    }
                });
    }

    /**
     * 收听时长格式化
     * 不足0.5小时按0.5小时 超过0.5小时不足1小时按1小时
     *
     * @return
     */
    private String userDurationFormat(long duration) {
        if (duration == 0) {
            return "0";
        }
        String time = "0";
        double cumPlaytime = (duration / 1000d / 60d) / 60;
        int o = ((int) cumPlaytime);//整数部分
        double p = cumPlaytime - o;//小数部分
        if (p <= 0.5) {
            time = (o + 0.5) + "";
        } else {
            time = (o + 1) + "";
        }
        return time;
    }
}