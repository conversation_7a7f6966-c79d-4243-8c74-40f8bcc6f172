package com.kaolafm.opensdk.api.operation.model.column;

import com.kaolafm.opensdk.api.operation.model.ImageFile;
import com.kaolafm.opensdk.api.topic.model.TopicPosts;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public abstract class ColumnContent extends ColumnMember implements Serializable {
    private String id;//资源id
    private int canPlay;//是否可以播放 0-不可播放 1-可以播放
    private int resType;
    private int isCurrentAudio;// 1-当前专辑内容
    private String brandTitle;//品牌电台标题
    private String tag;//标签
    private String tagColor;//标签颜色
    private long updateDate;//更新时间
    private int liveStatus;//直播状态 0-未开始 1-直播中 2-已结束
    private String destUrl;//跳转页面地址
    /**
     * 子内容合集
     */
    private List<ColumnContentChild> columnMemberChildContents;
    /**
     * 参与用户数量
     */
    private long userCount;
    /**
     * 阅读量
     */
    private long readCount;
    /**
     * 帖子信息
     */
    private List<TopicPosts> posts;
    /**
     * 跳转的一级tab
     */
    private String firstCode = "0";
    /**
     * 跳转的二级tab
     */
    private String secondCode = "0";

    public String getBrandTitle() {
        return brandTitle;
    }

    public void setBrandTitle(String brandTitle) {
        this.brandTitle = brandTitle;
    }

    public long getUserCount() {
        return userCount;
    }

    public void setUserCount(long userCount) {
        this.userCount = userCount;
    }

    public long getReadCount() {
        return readCount;
    }

    public void setReadCount(long readCount) {
        this.readCount = readCount;
    }

    public List<TopicPosts> getPosts() {
        return posts;
    }

    public void setPosts(List<TopicPosts> posts) {
        this.posts = posts;
    }



    public String getTagColor() {
        return tagColor;
    }

    public void setTagColor(String tagColor) {
        this.tagColor = tagColor;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }


    public int getLiveStatus() {
        return liveStatus;
    }

    public void setLiveStatus(int liveStatus) {
        this.liveStatus = liveStatus;
    }

    public String getDestUrl() {
        return destUrl;
    }

    public void setDestUrl(String destUrl) {
        this.destUrl = destUrl;
    }

    public String getFirstCode() {
        return firstCode;
    }

    public void setFirstCode(String firstCode) {
        this.firstCode = firstCode;
    }

    public String getSecondCode() {
        return secondCode;
    }

    public void setSecondCode(String secondCode) {
        this.secondCode = secondCode;
    }


    public long getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(long updateDate) {
        this.updateDate = updateDate;
    }

    public List<ColumnContentChild> getColumnMemberChildContents() {
        return columnMemberChildContents;
    }

    public void setColumnMemberChildContents(List<ColumnContentChild> columnMemberChildContents) {
        this.columnMemberChildContents = columnMemberChildContents;
    }

    public int getIsCurrentAudio() {
        return isCurrentAudio;
    }

    public void setIsCurrentAudio(int isCurrentAudio) {
        this.isCurrentAudio = isCurrentAudio;
    }

    public int getResType() {
        return resType;
    }

    public void setResType(int resType) {
        this.resType = resType;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public int getCanPlay() {
        return canPlay;
    }

    public void setCanPlay(int canPlay) {
        this.canPlay = canPlay;
    }

}
