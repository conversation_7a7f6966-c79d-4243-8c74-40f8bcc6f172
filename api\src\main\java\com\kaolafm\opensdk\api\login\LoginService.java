package com.kaolafm.opensdk.api.login;

import com.kaolafm.opensdk.account.token.KaolaAccessToken;
import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.KaolaApiConstant;
import com.kaolafm.opensdk.api.login.internal.AuthorInfo;
import com.kaolafm.opensdk.api.login.model.CumPlaytimeInfo;
import com.kaolafm.opensdk.api.login.model.QRCodeInfo;
import com.kaolafm.opensdk.api.login.model.Success;
import com.kaolafm.opensdk.api.login.model.UserInfo;
import com.kaolafm.opensdk.api.login.model.UserWealth;

import java.util.HashMap;
import java.util.Map;

import io.reactivex.Observable;
import io.reactivex.Single;
import okhttp3.RequestBody;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.POST;
import retrofit2.http.Query;
import retrofit2.http.QueryMap;

/**
 * 登录注册注销等Service
 *
 * <AUTHOR> Yan
 * @date 2018/8/2
 */
interface LoginService {

    String PARAMETER_PHONE_NUMBER = "phoneNumber";
    String PARAMETER_VALIDATE_CODE = "validateCode";

    /**
     * 检测手机号是否已经注册
     */
    @Deprecated
//    @Headers(ApiHostConstants.DOMAIN_HEADER_KRADIO_USER)
//    @GET(KaolaApiConstant.REQUEST_K_RADIO_PHONE_IS_REGISTERED)
    Single<BaseResult<Success>> checkPhoneIsRegistered(@Query(PARAMETER_PHONE_NUMBER) String phoneNumber);

    /**
     * 获取注册验证码
     */
    @Deprecated
//    @Headers(ApiHostConstants.DOMAIN_HEADER_KRADIO_USER)
//    @GET(KaolaApiConstant.REQUEST_K_RADIO_VALIDATE_CODE)
    Single<BaseResult<Success>> getVerificationCode(@Query(PARAMETER_PHONE_NUMBER) String phoneNumber);


    /**
     * 注册
     */
    @Deprecated
//    @Headers(ApiHostConstants.DOMAIN_HEADER_KRADIO_USER)
//    @POST(KaolaApiConstant.REQUEST_K_RADIO_REGISTER)
    Single<BaseResult<AuthorInfo>> register(@QueryMap HashMap<String, String> params);

    /**
     * 手机号验证码登录
     */
    @Deprecated
//    @Headers(ApiHostConstants.DOMAIN_HEADER_KRADIO_USER)
//    @POST(KaolaApiConstant.REQUEST_K_RADIO_LOGIN)
    Single<BaseResult<AuthorInfo>> login(@Query(PARAMETER_PHONE_NUMBER) String phoneNumber,
            @Query(PARAMETER_VALIDATE_CODE) String validateCode);

    /**
     * 退出登录
     */
    @Deprecated
//    @Headers(ApiHostConstants.DOMAIN_HEADER_KRADIO_USER)
//    @POST(KaolaApiConstant.REQUEST_K_RADIO_LOGOUT)
    Single<BaseResult<Success>> logout();

    @Deprecated
//    @Headers(ApiHostConstants.DOMAIN_HEADER_KRADIO_USER)
//    @POST(KaolaApiConstant.BIND_KRADIO)
    Single<BaseResult<UserInfo>> bindKradio(@Query("code") String code);

    @Deprecated
//    @Headers(ApiHostConstants.DOMAIN_HEADER_KRADIO_USER)
//    @POST(KaolaApiConstant.UNBIND_KRADIO)
    Single<BaseResult<UserInfo>> unbindKradio();

    @Deprecated
//    @Headers(ApiHostConstants.DOMAIN_HEADER_KRADIO_USER)
//    @POST(KaolaApiConstant.CHECK_IS_BIND_KRADIO)
    Single<BaseResult<UserInfo>> isBindKradio();

    /**
     * 根据code绑定并获取用户信息
     * @param code
     * @return
     */
    @Deprecated
//    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
//    @POST(KaolaApiConstant.GET_USER_INFO)
    Observable<BaseResult<UserInfo>> authorized(@Query("code") String code);

    @Deprecated
//    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
//    @POST(KaolaApiConstant.KAOLA_LOGOUT)
    Single<BaseResult<UserInfo>> logoutTingban();

    /******************************************************************************/
    /**
     * 获取用户收听时长
     * @return
     */
    @Headers({ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER, ApiHostConstants.HTTPS_PROTOCOL_DOMAIN_HEADER})
    @POST(KaolaApiConstant.GET_PLAY_TIME)
    Single<BaseResult<CumPlaytimeInfo>> getCumPlaytime(@QueryMap Map<String, String> params);
    /**
     * 获取考拉登录二维码
     */
    @Headers({ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER, ApiHostConstants.HTTPS_PROTOCOL_DOMAIN_HEADER})
    @GET(KaolaApiConstant.GET_QR_CODE)
    Single<BaseResult<QRCodeInfo>> getQRCode(@QueryMap Map<String, String> params);

    /**
     * 检查二维码状态
     */
    @Headers({ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER, ApiHostConstants.HTTPS_PROTOCOL_DOMAIN_HEADER})
    @GET(KaolaApiConstant.CHECK_QR_STATUS)
    Observable<BaseResult<QRCodeInfo>> checkQRStatus(@Query("uuid") String uuid);

    /**
     * 根据uuid获取用于绑定K-radio的code
     */
    @Headers({ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER, ApiHostConstants.HTTPS_PROTOCOL_DOMAIN_HEADER})
    @GET(KaolaApiConstant.FETCH_CODE)
    Observable<BaseResult<QRCodeInfo>> fetchCode(@Query("uuid") String uuid);

    /**
     * 获取token并绑定设备
     * @param appKey 开放平台应用秘钥
     * @param code 通过uuid获取的code值
     * @return
     */
    @Headers({ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER, ApiHostConstants.HTTPS_PROTOCOL_DOMAIN_HEADER})
    @POST(KaolaApiConstant.GET_TOKEN_AND_BIND + "?grant_type=authorization_code")
    Observable<BaseResult<KaolaAccessToken>> authorized(@Query("secretkey")String appKey, @Query("code") String code);

    /**
     * 刷新token
     * @param appKey 开放平台应用秘钥
     * @param refreshToken 授权时返回的refreshToken
     * @return
     */
    @Headers({ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER, ApiHostConstants.HTTPS_PROTOCOL_DOMAIN_HEADER})
    @POST(KaolaApiConstant.REFRESH_TOKEN + "?grant_type=refresh_token")
    Single<BaseResult<KaolaAccessToken>> refreshToken(@Query("secretkey")String appKey, @Query("refresh_token")String refreshToken);

    /**
     * 获取用户信息
     * @return
     */
    @Headers({ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER, ApiHostConstants.HTTPS_PROTOCOL_DOMAIN_HEADER})
    @POST(KaolaApiConstant.GET_USER_INFO)
    Observable<BaseResult<UserInfo>> getUserInfo();

    /**
     * 取消授权
     * @param appKey 开放平台应用秘钥
     * @return
     */
    @Headers({ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER, ApiHostConstants.HTTPS_PROTOCOL_DOMAIN_HEADER})
    @POST(KaolaApiConstant.REVOKE_AUTHORIZATION)
    Single<BaseResult<Success>> revokeAuthorization(@Query("secretkey")String appKey);

    /**
     * 根据第三方账号的uid和token打通账号，就是校验第三方用户的合法性。
     */
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @POST(KaolaApiConstant.LINK_ACCOUNT)
    Single<BaseResult<Success>> linkAccount(@Body RequestBody requestBody);

    /**
     * 获取用户财产信息
     */
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_USER_WEALTH)
    Single<BaseResult<UserWealth>> getUserWealth();

}
