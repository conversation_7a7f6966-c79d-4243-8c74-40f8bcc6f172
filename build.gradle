// Top-level build file where you can add configuration options common to all sub-projects/modules.
apply from: "config.gradle"

buildscript {

    repositories {
        //阿里云的maven镜像，提升下载依赖库的速度，理论上只有这个就可以了，下面的都不需要
        maven{url 'http://maven.aliyun.com/nexus/content/groups/public/'}
        google()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:3.6.4'
        classpath 'org.greenrobot:greendao-gradle-plugin:3.3.0'
    }
}

allprojects {
    repositories {
        //阿里云的maven镜像，提升下载依赖库的速度，理论上只有这个就可以了，下面的都不需要
        maven{url 'http://maven.aliyun.com/nexus/content/groups/public/'}
        google()
        maven { url "https://jitpack.io" } 
        maven {
            def mavenUrl = readLocalProperties("local.maven.url")
            if (mavenUrl != null) {
                url mavenUrl
            } else {
            //原先的仓库地址
//                url "http://nexus.kaolafm.com/nexus/content/repositories/releases/"
            //新仓库地址，从2022-12-1谷老师修改sdk仓库地址且12-2更新app仓库地址开始使用
            url "http://pub.nexus.kaolafm.com:8082/repository/maven-releases/"
            }
        }
//        maven {
////            url "https://dl.bintray.com/tingban/maven/"
////            url "file:///Users/<USER>/AndroidStudioProjects/maven-repository"
////            url "http://nexus.kaolafm.com/nexus/content/repositories/releases/"
//            url "http://pub.nexus.kaolafm.com:8082/repository/github-self/"
//        }
        maven{url "http://nexus.kaolafm.com/nexus/content/repositories/releases/"}
    }
}
def readLocalProperties(String name) {
    def properties = new Properties()
    File file = project.rootProject.file('local.properties')
    if (file.exists()) {
        def inputStream = file.newDataInputStream()
        properties.load(inputStream)
        if (properties.containsKey(name)) {
            return properties.getProperty(name)
        }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}

subprojects {
    // 定义检查依赖变化的时间间隔,!!配置为0实时刷新
    configurations.all {
        // check for updates every build
        resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
    }
}