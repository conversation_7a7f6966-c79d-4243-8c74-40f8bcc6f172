package com.kaolafm.report.event;


import com.kaolafm.opensdk.log.Logging;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.util.ReportConstants;
import com.kaolafm.report.util.ReportParameterManager;

/**
 * <AUTHOR> on 2019/1/21.
 */

public class BaseReportEventBean {
    private String dsource = "vehicleeqv2"; //区分接口用,vehicleeqv1=车联网v1版,vehicleeqv2=车联网v2版
    private String applicationcode = "17000"; //非空
    private String sourcetype = "3"; //非空
    private String appid; //应用id,非空, 标识不同车场的不同项目
    private String openid; //旧车载设备唯一识别,非空，考拉生成的唯一id
    private String udid; //设备唯一标识（目前在用）,非空，uv统计用唯一识别
    private String eventcode; //事件代码,非空
    private String car_type; //车型,可为空
    private String page; //事件当前所属页面,可为空，操作页面id
    private String uid; //用户id,可为空
    private String lon; //经度,可为空
    private String lat; //维度,可为空
    private String ip; //用户ip地址,新增字段，非空，可能是别的城市ip，不准
    private String network; //网络,-1：未知，0：无网，1：wifi，2：2g，3：3g，4：4g，非空
    private String operator; //运营商,0、未知，1、移动，2、联通，3、电信，非空
    private String imsi; //可为空，如果无法获取，可以不用赋值（维持现有生成方式）
    private String os; //操作系统,可为空
    private String osversion; //操作系统补充,可为空
    private String app_version; //当前应用版本,可为空
    private String screen_direction; //屏幕状态,可为空，v1 app_type 清除,v2版本，0：横屏；1：竖屏
    private String timestamp; //事件发生时间,时间戳，非空

    private String app_version2;
    private String lib_version;
    private String market_type;//渠道类型 0：后装，1：前装
    private String appid_type;//合作方式 0：APK，1：SDK，2：API，3：控件
    private String oem;//产品直接落地车辆所属的车厂，后装则为方案商名称
    private String car_brand;//品牌
    private String car_id;//车载唯一标识码 	车厂账号
    private String screen_height;
    private String screen_width;
    private String manufacturer;//设备制造商
    private String model;//设备型号
    private String report_timely = ReportConstants.REPORT_EVENT_NORMAL + "";
    private String playid;//播放行为发生时，每播放一个音频时，生成一个新的唯一的id，没有音频播放时，可以赋默认值0000 播放器拉取新的播放资源时更新playid，比如点播、播放开始时，playid均更新；选择搜索结果上报时playid不更新


    /**
     * 应用每启动一次，生成一个新的sessionid
     */
    private String sessionid;

    /**
     * 当前应用每发生一次事件上报，action_id自增1
     */
    private String action_id;
    /**
     * 0：否；1：真
     */
    private String wifi;


    /**
     * 0、未知，1、移动，2、联通，3、电信
     */
    private String carrier;

    /**
     * 鉴权时使用的包名
     */
    private String channel;

    /**
     * 该应用所属的开发者id
     */
    private String developer;
    /**
     * 应用表的产品id：1-车载综合版；6-车载在线电台版
     */
    private String product_id;

    /**
     * 应用模式：0-综合版；1-在线电台版
     */
    private String app_mode;

    public BaseReportEventBean() {
        ReportParameterManager reportParameterManager = ReportParameterManager.getInstance();
        appid = reportParameterManager.getReportParameter().getAppid();
        uid = reportParameterManager.getReportParameter().getUid();
        openid = reportParameterManager.getReportParameter().getOpenid();
        lib_version = reportParameterManager.getReportParameter().getLib_version();
        udid = reportParameterManager.getReportParameter().getDeviceId();
        car_type = reportParameterManager.getCarType();

        car_id = reportParameterManager.getReportCarParameter().getFirstAppId();
        market_type = reportParameterManager.getReportCarParameter().getMarketType();
        oem = reportParameterManager.getReportCarParameter().getOem();
        car_brand = reportParameterManager.getReportCarParameter().getCarBrand();
        appid_type = reportParameterManager.getReportCarParameter().getAppIdType();
        developer = reportParameterManager.getReportCarParameter().getDeveloper();
        channel = reportParameterManager.getReportParameter().getChannel();

        model = reportParameterManager.getModel();
        imsi = reportParameterManager.getImsi();
        timestamp = String.valueOf(System.currentTimeMillis());
        lon = reportParameterManager.getLon();
        lat = reportParameterManager.getLat();
        os = reportParameterManager.getOs();
        screen_height = reportParameterManager.getScreen_height();
        screen_width = reportParameterManager.getScreen_width();
        osversion = reportParameterManager.getOs_version();
//        if (!ReportHelper.getInstance().isUseBySDK) {
            app_version = reportParameterManager.getVersionName();
            if (app_version != null && app_version.contains(".")) {
                app_version = app_version.substring(0, app_version.lastIndexOf("."));
            }
            app_version2 = reportParameterManager.getApp_version();
            Logging.d(ReportConstants.REPORT_TAG, "获取版本名称: " + app_version + ", 版本号: " + app_version2);
//        }
        sessionid = String.valueOf(reportParameterManager.getmSessionId());
        action_id = String.valueOf(reportParameterManager.getActionId());
        wifi = reportParameterManager.getWifi();
        network = reportParameterManager.getNetwork_type();
        ip = reportParameterManager.getIP();
        operator = reportParameterManager.getCarrier();
        carrier = reportParameterManager.getCarrier();
        screen_direction = reportParameterManager.getScreen_direction();
        manufacturer = reportParameterManager.getManufacturer();
        page = reportParameterManager.getPage();
        playid = reportParameterManager.getPlayId();
        product_id = reportParameterManager.getProduct_id();
        app_mode = reportParameterManager.getApp_mode();
    }


    public String getProduct_id() {
        return product_id;
    }

    public void setProduct_id(String product_id) {
        this.product_id = product_id;
    }

    public String getApp_mode() {
        return app_mode;
    }

    public void setApp_mode(String app_mode) {
        this.app_mode = app_mode;
    }

    public String getDsource() {
        return dsource;
    }

    public void setDsource(String dsource) {
        this.dsource = dsource;
    }

    public String getApplicationcode() {
        return applicationcode;
    }

    public void setApplicationcode(String applicationcode) {
        this.applicationcode = applicationcode;
    }

    public String getSourcetype() {
        return sourcetype;
    }

    public void setSourcetype(String sourcetype) {
        this.sourcetype = sourcetype;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getImsi() {
        return imsi;
    }

    public void setImsi(String imsi) {
        this.imsi = imsi;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getEventcode() {
        return eventcode;
    }

    public void setEventcode(String eventcode) {
        this.eventcode = eventcode;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public String getLon() {
        return lon;
    }

    public void setLon(String lon) {
        this.lon = lon;
    }

    public String getLat() {
        return lat;
    }

    public void setLat(String lat) {
        this.lat = lat;
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public String getPage() {
        return page;
    }

    public void setPage(String page) {
        this.page = page;
    }

    public String getOs() {
        return os;
    }

    public void setOs(String os) {
        this.os = os;
    }

    public String getScreen_height() {
        return screen_height;
    }

    public void setScreen_height(String screen_height) {
        this.screen_height = screen_height;
    }

    public String getScreen_width() {
        return screen_width;
    }

    public void setScreen_width(String screen_width) {
        this.screen_width = screen_width;
    }


    public String getApp_version() {
        return app_version;
    }

    public void setApp_version(String app_version) {
        this.app_version = app_version;
    }

    public String getApp_version2() {
        return app_version2;
    }

    public void setApp_version2(String app_version2) {
        this.app_version2 = app_version2;
    }

    public String getLib_version() {
        return lib_version;
    }

    public void setLib_version(String lib_version) {
        this.lib_version = lib_version;
    }

    public String getSessionid() {
        return sessionid;
    }

    public void setSessionid(String sessionid) {
        this.sessionid = sessionid;
    }

    public String getAction_id() {
        return action_id;
    }

    public void setAction_id(String action_id) {
        this.action_id = action_id;
    }

    public String getWifi() {
        return wifi;
    }

    public void setWifi(String wifi) {
        this.wifi = wifi;
    }

    public String getNetwork() {
        return network;
    }

    public void setNetwork(String network) {
        this.network = network;
    }

    public String getCarrier() {
        return carrier;
    }

    public void setCarrier(String carrier) {
        this.carrier = carrier;
    }

    public String getCar_id() {
        return car_id;
    }

    public void setCar_id(String car_id) {
        this.car_id = car_id;
    }

    public String getMarket_type() {
        return market_type;
    }

    public void setMarket_type(String market_type) {
        this.market_type = market_type;
    }

    public String getAppid_type() {
        return appid_type;
    }

    public void setAppid_type(String appid_type) {
        this.appid_type = appid_type;
    }

    public String getOem() {
        return oem;
    }

    public void setOem(String oem) {
        this.oem = oem;
    }

    public String getCar_brand() {
        return car_brand;
    }

    public void setCar_brand(String car_brand) {
        this.car_brand = car_brand;
    }

    public String getCar_type() {
        return car_type;
    }

    public void setCar_type(String car_type) {
        this.car_type = car_type;
    }

    public String getScreen_direction() {
        return screen_direction;
    }

    public void setScreen_direction(String screen_direction) {
        this.screen_direction = screen_direction;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getPlayid() {
        return playid;
    }

    public void setPlayid(String playid) {
        this.playid = playid;
    }

    public String getReport_timely() {
        return report_timely;
    }

    public void setReport_timely(String report_timely) {
        this.report_timely = report_timely;
    }

    public String getUdid() {
        return udid;
    }

    public void setUdid(String udid) {
        this.udid = udid;
    }

    public String getOsversion() {
        return osversion;
    }

    public void setOsversion(String osversion) {
        this.osversion = osversion;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getDeveloper() {
        return developer;
    }

    public void setDeveloper(String developer) {
        this.developer = developer;
    }
}
