package com.kaolafm.opensdk.player.logic.playcontrol;

import android.net.Uri;
import android.util.Log;

import com.kaolafm.opensdk.api.media.VideoAudioRequest;
import com.kaolafm.opensdk.api.media.model.AudioFileInfo;
import com.kaolafm.opensdk.api.media.model.AudioPlayInfo;
import com.kaolafm.opensdk.api.media.model.ToneQuality;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.core.ijk.VideoView;
import com.kaolafm.opensdk.player.core.media.IRenderView;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

import java.util.List;

/**
 * 视频播放控制
 * <AUTHOR>
 */
public class VideoPlayControl extends BasePlayControl {

    @Override
    public void start(int type, PlayItem playItem, VideoView videoView, boolean isPlayNow) {
        super.start(type, playItem, videoView, isPlayNow);
    }


    @Override
    public void stop() {
        super.stop();
        if (getVideoView() != null) {
            try {
                getVideoView().stop();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    ToneQuality getToneQuality(PlayItem playItem, List<AudioFileInfo> playListUrlInfos) {
        return null;
    }

    //加密音频播放测试
//            String testUrl = "https://ytmedia.radio.cn/CCYT%2F2021%2F08%2F02%2F1627892285de0d974ced6fefe3381f20be9d8cd70alc.mp3";
////            String testUrl = Environment.getExternalStorageDirectory().getAbsolutePath() +"/Download/decode_test.mp3";
////            String testUrl = Environment.getExternalStorageDirectory().getAbsolutePath() +"/Download/encode_test.mp3";
//            Log.i("start play", "testUrl:" + testUrl);
////            String testUrl = "/sdcard/Download/encode_test.mp3";
//            playItem.setPlayUrl(testUrl);
//            playWithPosition(playItem);
    // 由于添加了付费的内容播放，专辑内容需要单独请求播放的url地址
    @Override
    void requestPlayUrl(PlayItem playItem, OnGetPlayUrlData callback) {
        new VideoAudioRequest().getAudioPlayInfo(playItem.getPlayUrlId(), new HttpCallback<AudioPlayInfo>() {
            @Override
            public void onSuccess(AudioPlayInfo audioPlayInfo) {
                List<AudioFileInfo> playList = audioPlayInfo.getPlayInfoList();
                if (playList == null) {
                    onError(new ApiException(PlayerConstants.ERROR_CODE_PLAY_LIST_URL_NULL, "playList is null"));
                    return;
                }
                //设置并回调播放地址
                setPlayUrl(playItem, playList);

//                if (mVideoView != null) {
//                    mVideoView.initRenders();
//                    if(mPlayerBinder!=null){
//                        mVideoView.setMediaPlayer(mPlayerBinder.getMediaPlayer());
//                    }
//                    mVideoView.setAspectRatio(IRenderView.AR_16_9_FIT_PARENT);
//                    if(playItem!=null){
//                        mVideoView.setVideoURI(Uri.parse(playItem.getPlayUrl()));
//                    }
//                }

                callback.onDataGet(playItem.getPlayUrl());
            }

            @Override
            public void onError(ApiException e) {
                Log.i("VideoPlayControl", "getPlayUrl error:" + e.toString());
                stop();
                if (e.getCode() == PlayerConstants.ERROR_CODE_NO_COPYRIGHT){
                    // code - 50816, message - 因版权原因，暂时无法播放
                    if(mBasePlayControlListener != null){
                        mBasePlayControlListener.onPlayerFailed(playItem, PlayerConstants.ERROR_CODE_NO_COPYRIGHT, e.getCode());
                    }
                    return;
                }
                if (e.getCode() == PlayerConstants.ERROR_CODE_PLAY_LIST_URL_NULL) {
                    if(mBasePlayControlListener != null){
                        mBasePlayControlListener.onPlayerFailed(playItem, PlayerConstants.ERROR_CODE_PLAY_LIST_URL_NULL, 404);
                    }
                    return;
                }
                if(mBasePlayControlListener != null){
                    mBasePlayControlListener.onPlayerFailed(playItem, PlayerConstants.ERROR_CODE_PLAY_LIST_URL_DECODE, e.getCode());
                }
            }
        });
    }

//    @Override
//    void requestPlayUrl(PlayItem playItem, OnGetPlayUrlData callback) {
//        VideoAlbumPlayItem videoAlbumPlayItem = (VideoAlbumPlayItem) playItem;
//        setPlayUrl(playItem, videoAlbumPlayItem.getPlayUrlDataList());
//        callback.onDataGet(videoAlbumPlayItem.getPlayUrl());
////        super.requestPlayUrl(playItem, callback);
//        if (mVideoView != null) {
//            mVideoView.initRenders();
//            if(mPlayerBinder!=null){
//                mVideoView.setMediaPlayer(mPlayerBinder.getMediaPlayer());
//            }
//            mVideoView.setAspectRatio(IRenderView.AR_16_9_FIT_PARENT);
//            if(playItem!=null){
//                mVideoView.setVideoURI(Uri.parse(playItem.getPlayUrl()));
//            }
//        }
//    }

    @Override
    String getPlayItemResourceType(PlayItem playItem) {
        return "mp4,mkv,mov,flv,ts";
    }

}
