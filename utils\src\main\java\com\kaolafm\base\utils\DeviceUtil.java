package com.kaolafm.base.utils;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Build;
import android.provider.Settings;
import android.telephony.TelephonyManager;
import android.text.TextUtils;
import android.util.DisplayMetrics;

import com.kaolafm.base.internal.DeviceId;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.lang.reflect.Method;
import java.net.Inet6Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Enumeration;

/**
 * 获取设备相关信息的工具类，ip、deviceId、手机串号、dns、分辨率、尺寸、服务运营商。
 * <AUTHOR>
 * @date 2018/8/13
 */

public class DeviceUtil {

    private DeviceUtil() {
    }

    /**
     * 获取客户端ip地址
     *
     * @return
     */
    public static String getClientIP() {
        String hostIp = null;
        try {
            Enumeration nis = NetworkInterface.getNetworkInterfaces();
            InetAddress ia = null;
            while (nis.hasMoreElements()) {
                NetworkInterface ni = (NetworkInterface) nis.nextElement();
                Enumeration<InetAddress> ias = ni.getInetAddresses();
                while (ias.hasMoreElements()) {
                    ia = ias.nextElement();
                    if (ia instanceof Inet6Address) {
                        continue;// skip ipv6
                    }
                    String ip = ia.getHostAddress();
                    if (!"127.0.0.1".equals(ip)) {
                        hostIp = ia.getHostAddress();
                        break;
                    }
                }
            }
        } catch (SocketException e) {
            e.printStackTrace();
        }
        if (TextUtils.isEmpty(hostIp)) {
            hostIp = "127.0.0.1";
        }
        return hostIp;
    }

    public static String getDeviceId(Context context) {
        return DeviceId.getDeviceId(context);
    }


    /**
     * 获得设备屏幕分辨率。返回 widthPixels*heightPixels
     */
    public static String getScreenResolution(Context context) {
        try {
            DisplayMetrics dm = context.getResources().getDisplayMetrics();
            return StringUtil.join(dm.widthPixels, "*", dm.heightPixels);
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 手机串号
     *
     * @param context
     * @return
     */
    @SuppressLint("MissingPermission")
    public static String getImei(Context context) {
        String imei = "";
        try {
            PackageManager packageManager = context.getPackageManager();
            if (packageManager.hasSystemFeature(PackageManager.FEATURE_TELEPHONY)) {
                TelephonyManager telephonyManager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
                if (telephonyManager != null) {
                    if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) {
                        imei = telephonyManager.getDeviceId();
                    } else {
                        Method method = telephonyManager.getClass().getMethod("getImei");
                        imei = (String) method.invoke(telephonyManager);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if(TextUtils.isEmpty(imei)){
            imei = Settings.System.getString(context.getContentResolver(), Settings.Secure.ANDROID_ID);
        }
        return imei;
    }

    /**
     * 获取当前屏幕的尺寸，单位：英寸，精确到2位小数;
     *
     * @param context
     * @return
     */
    public static String getScreenSize(Context context) {
        try {
            DisplayMetrics dm = context.getResources().getDisplayMetrics();
            int width = dm.widthPixels;
            int height = dm.heightPixels;
            double x = Math.pow(width / dm.xdpi, 2);
            double y = Math.pow(height / dm.ydpi, 2);
            double screenInches = Math.sqrt(x + y);
            return String.valueOf((Math.round(screenInches * 100) / 100.0));
        } catch (Throwable e) {
            return "";
        }
    }

    /**
     * 运营商未知
     */
    private final static int PRIVIDER_UNKNOWN = 0;
    /**
     * 中国移动
     */
    private final static int PRIVIDER_CHINA_MOBILE = 1;
    /**
     * 中国联通
     */
    private final static int PRIVIDER_CHINA_UNICOM = 2;
    /**
     * 中国电信
     */
    private final static int PRIVIDER_CHINA_TELE = 3;

    /**
     * 获取设备dns
     *
     * @return
     */
    public static String getDeviceDns() {
        Process cmdProcess = null;
        BufferedReader reader = null;
        String dnsIP;
        try {
            cmdProcess = Runtime.getRuntime().exec("getprop net.dns1");
            reader = new BufferedReader(new InputStreamReader(cmdProcess.getInputStream()));
            dnsIP = reader.readLine();
            return dnsIP;
        } catch (IOException e) {
            return null;
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (cmdProcess != null) {
                cmdProcess.destroy();
            }
        }
    }

    private static boolean isIdValid(String id) {
        return !TextUtils.isEmpty(id) && !"0".equals(id) && !"null".equalsIgnoreCase(id);
    }

    /**
     * 获得国际移动用户识别码
     *
     * @param context
     * @return
     */
    @SuppressLint("MissingPermission")
    public static String getIMSI(Context context) {
        try {
             String imsi = ((TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE)).getSubscriberId();
            return imsi == null ? "" : imsi;
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 获取服务运营商
     */
    public static int getProvidersName(Context context) {
        int carrier = PRIVIDER_UNKNOWN;
        String imsi = getIMSI(context);
        if (imsi != null) {
            if (imsi.startsWith("46000") || imsi.startsWith("46002")
                    || imsi.equals("46007")) {
                // 中国移动
                carrier = PRIVIDER_CHINA_MOBILE;
            } else if (imsi.startsWith("46001")) {
                // 中国联通
                carrier = PRIVIDER_CHINA_UNICOM;
            } else if (imsi.startsWith("46003")) {
                // 中国电信
                carrier = PRIVIDER_CHINA_TELE;
            } else {
            }
        } else {
        }
        return carrier;
    }
}
