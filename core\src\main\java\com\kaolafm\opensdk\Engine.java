package com.kaolafm.opensdk;

import android.app.Application;

import com.kaolafm.opensdk.http.core.HttpCallback;

/**
 * 引擎接口
 * <AUTHOR>
 * @date 2020-01-14
 */
public interface Engine<O extends Options> {

    /**
     * 初始化
     * @param application
     * @param options
     * @param callback
     */
    void init(Application application, O options, HttpCallback<Boolean> callback);

    /**
     * 是否已经激活
     * @return
     */
    boolean isActivated();

    /**
     * 激活
     * @param callback
     */
    void activate(HttpCallback<Boolean> callback);

    /**
     * 初始化并激活。
     * @param application
     * @param options
     * @param callback
     */
    void config(Application application, O options, HttpCallback<Boolean> callback);

    /**
     * 释放资源
     */
    void release();

    /**
     * 设置位置
     * @param lng
     * @param lat
     */
    void setLocation(String lng, String lat);
    String[] getLocation();
}
