package com.kaolafm.opensdk.player.logic.playlist;

import com.kaolafm.base.utils.DateUtil;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.api.broadcast.BroadcastDetails;
import com.kaolafm.opensdk.api.tv.TVRequest;
import com.kaolafm.opensdk.api.tv.model.TVDetails;
import com.kaolafm.opensdk.api.tv.model.TVProgramDetails;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.listener.ICheckCopyrightListener;
import com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener;
import com.kaolafm.opensdk.player.logic.model.PlayItemConstants;
import com.kaolafm.opensdk.player.logic.model.PlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.item.BroadcastPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.InvalidPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.TVPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.playlist.innerinterface.IDataListCallback;
import com.kaolafm.opensdk.player.logic.playlist.innerinterface.ISonPlayList;
import com.kaolafm.opensdk.player.logic.playlist.util.PlayListUtils;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;
import com.kaolafm.opensdk.player.logic.util.PlayerPreconditions;

import java.util.ArrayList;
import java.util.List;

/**
 *
 */

public class TVPlayListControl extends BasePlayListControl implements ISonPlayList {
    private int LOAD_PRE_PAGE = 1;
    private int LOAD_NEXT_PAGE = 2;
    private ArrayList<PlayItem> mSongPlayItemArrayList;
    private boolean isPlaySongList = false;
    private int mSongListPosition = -1;

    private TVRequest mTVRequest;

    private TVDetails mTvDetails;//用以保存当前收听电台的详情
    private TVDetails mTempTvDetails;//用以保存检查版权时获得的数据，可能不是当前正在收听的电台
    private TVPlayItem mTempPlayItem;//检查版权到初始化播单的过程中用来保存临时数据的。一旦获取播单成功，将不再有用

    public TVPlayListControl() {
        mSongPlayItemArrayList = new ArrayList<>();
        mTVRequest = new TVRequest();
    }

    @Override
    public void initPlayList(PlayerBuilder playerBuilder, IPlayListGetListener iPlayListGetListener) {
        super.initPlayList(playerBuilder, iPlayListGetListener);
        initTVInfo(iPlayListGetListener);
    }

    @Override
    public void loadPrePage(IPlayListGetListener iPlayListGetListener) {
    }


    @Override
    public void loadNextPage(IPlayListGetListener iPlayListGetListener) {
    }


    /**
     * 校验版权（通常用于在播放前校验是否已经授权，未授权时不可播）
     */
    public void checkCopyright(PlayerBuilder playerBuilder, ICheckCopyrightListener iCheckCopyrightListener) {
        long albumId = Long.parseLong(playerBuilder.getId());
        PlayerLogUtil.log(getClass().getSimpleName(), "checkCopyright", "broadcast id =" + albumId);

        TVPlayItem playItem = new TVPlayItem();
        playItem.getInfoData().setAlbumId(albumId);

        getTvDetails(albumId, new HttpCallback<TVDetails>() {
            @Override
            public void onSuccess(TVDetails tvDetails) {
                PlayerLogUtil.log(getClass().getSimpleName(), "checkCopyright", "success");
                if (PlayerPreconditions.checkNull(tvDetails) || tvDetails.getListenTVid() != albumId) {
                    PlayerLogUtil.log(getClass().getSimpleName(), "checkCopyright", "success, tvDetails is empty");
                    if (iCheckCopyrightListener != null)
                        iCheckCopyrightListener.onError(playItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_ID_NULL, -1);
                    return;
                }
                boolean isLite = tvDetails.getIsLite() == BroadcastDetails.COPYRIGHT_LITE;
                PlayerLogUtil.log(getClass().getSimpleName(), "checkCopyright", "success, broadcast is lite?:" + isLite);
                if (isLite) {
                    if (iCheckCopyrightListener != null)
                        iCheckCopyrightListener.onError(playItem, PlayerConstants.ERROR_CODE_RADIO_COPYRIGHT_LITE, -1);
                    return;
                }
                mTempTvDetails = tvDetails;
                if (iCheckCopyrightListener != null) iCheckCopyrightListener.onGranted();
            }

            @Override
            public void onError(ApiException e) {
                PlayerLogUtil.log(getClass().getSimpleName(), "checkCopyright", "error");
                if (iCheckCopyrightListener != null)
                    iCheckCopyrightListener.onError(playItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_ID_NULL, -1);
            }
        });
    }

    private void getTvDetails(long albumId, HttpCallback<TVDetails> callback) {
        mTVRequest.getTVDetails(albumId, callback);
    }

    private void initTVInfo(IPlayListGetListener iPlayListGetListener) {
        long albumId = string2Long(mPlaylistInfo.getId());
        PlayerLogUtil.log(getClass().getSimpleName(), "initTVInfo", "broadcast id =" + albumId);

        if (mTempTvDetails == null || mTempTvDetails.getListenTVid() != albumId) {
            //没有获取详情
            checkCopyright(mPlayerBuilder, new ICheckCopyrightListener() {
                @Override
                public void onGranted() {
                    initTVInfo(iPlayListGetListener);
                }

                @Override
                public void onError(PlayItem playItem, int errorCode, int errorExtra) {
                    PlayerLogUtil.log(getClass().getSimpleName(), "initTVInfo", "error");
                    initPlayListInfo(null);
                    notifyPlayListGetError(iPlayListGetListener, playItem, errorCode, errorExtra);
                    notifyPlayListChangeError(playItem, errorCode, errorExtra);
                }
            });
            return;
        }
        //走到这里就说明要播放的电台已经获得版权
        //已经对要播放的电台进行了checkCopyright操作
        mTempPlayItem = new TVPlayItem();
        mTempPlayItem.getInfoData().setAlbumId(albumId);
        mTempPlayItem.getInfoData().setAlbumName(mTempTvDetails.getName());
        mTempPlayItem.getInfoData().setAlbumPic(mTempTvDetails.getImg());
        mTempPlayItem.setListenCount(mTempTvDetails.getOnLineNum());

        mTvDetails = mTempTvDetails;
        mTempTvDetails = null;

        initPlayListInfo(mTvDetails);
        mPlaylistInfo.setListenNum(mTvDetails.getOnLineNum());
        loadPlayList(albumId, null, new IDataListCallback<List<TVProgramDetails>>() {
            @Override
            public void success(List<TVProgramDetails> programDetails) {
                PlayerLogUtil.log(getClass().getSimpleName(), "initTVInfo", "get play list success");
                ArrayList<PlayItem> playItemArrayList = PlayListUtils.programDetailsToTVPlayItem(programDetails, mPlaylistInfo.getBroadcastChannel(), mPlaylistInfo.getListenNum());
                if (ListUtil.isEmpty(playItemArrayList)) {
                    PlayerLogUtil.log(getClass().getSimpleName(), "initTVInfo", "get play list success, list is empty");
                    notifyPlayListGetError(iPlayListGetListener, mTempPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_PAGE_NULL, -1);
                    notifyPlayListChangeError(mTempPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_PAGE_NULL, -1);
                    return;
                }
                release();
                updatePlayListContent(playItemArrayList, iPlayListGetListener);
            }

            @Override
            public void error(ApiException e) {
                PlayerLogUtil.log(getClass().getSimpleName(), "initTVInfo", "get play list error");
                notifyPlayListGetError(iPlayListGetListener, mTempPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_ID_NULL, e.getCode());
                notifyPlayListChangeError(mTempPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_ID_NULL, e.getCode());
            }
        });
    }

    /**
     * 加载信息
     *
     * @param albumId
     * @param data
     * @param iDataListCallback
     */
    private void loadPlayList(long albumId, String data, IDataListCallback<List<TVProgramDetails>> iDataListCallback) {
        mTVRequest.getTVProgramList(albumId, data, new HttpCallback<List<TVProgramDetails>>() {
            @Override
            public void onSuccess(List<TVProgramDetails> programDetails) {
//                if (ListUtil.isEmpty(programDetails)) {
//                    if (iDataListCallback != null) {
//                        iDataListCallback.error();
//                    }
//                    return;
//                }
                if (iDataListCallback != null) {
                    iDataListCallback.success(programDetails);
                }
            }

            @Override
            public void onError(ApiException e) {
                if (iDataListCallback != null) {
                    iDataListCallback.error(e);
                }
            }
        });
    }

    @Override
    public void getPrePlayItem(IPlayListGetListener iPlayListGetListener) {
        if (isPlaySongList) {
            if (mSongListPosition > 0) {
                PlayerLogUtil.log(getClass().getSimpleName(), "getPrePlayItem", "is son play list");
                notifyPlayListGet(iPlayListGetListener, mSongPlayItemArrayList.get(--mSongListPosition), null);
            }
        } else {
            if (PlayerPreconditions.checkNull(mPlayItemArrayList)) {
                notifyPlayListGetError(iPlayListGetListener, new InvalidPlayItem(), PlayerConstants.ERROR_CODE_PLAY_LIST_ONE_KEY_TO_TOP_ALBUM_PRE_ITEM_PAGE_NULL, -1);
                return;
            }
            if (mPosition - 1 >= mPlayItemArrayList.size()) {
                notifyPlayListGetError(iPlayListGetListener, new InvalidPlayItem(), PlayerConstants.ERROR_CODE_PLAY_LIST_PRE_INDEX_OUT_OF_BOUNDS, -1);
                return;
            }
            if (mPosition - 1 < 0) {
                PlayerLogUtil.log(getClass().getSimpleName(), "getPrePlayItem", "current page is start");
                if (hasPrePage()) {
                    PlayerLogUtil.log(getClass().getSimpleName(), "getPrePlayItem", "has pre page");
                    loadPrePage(iPlayListGetListener);
                } else {
                    notifyPlayListGetError(iPlayListGetListener, new InvalidPlayItem(), PlayerConstants.ERROR_CODE_PLAY_LIST_IS_FIRST_ONE, -1);
                }
                return;
            }
            PlayerLogUtil.log(getClass().getSimpleName(), "getPrePlayItem", "position = " + mPosition);
            TVPlayItem playItem = (TVPlayItem) (mPlayItemArrayList.get(mPosition - 1));
            if (PlayerPreconditions.checkNull(playItem)) {
                notifyPlayListGetError(iPlayListGetListener, new InvalidPlayItem(), PlayerConstants.ERROR_CODE_PLAY_LIST_PRE_ITEM_NULL, -1);
                return;
            }
            if (playItem.getStatus() == PlayerConstants.BROADCAST_STATUS_PLAYBACK) {
                PlayerLogUtil.log(getClass().getSimpleName(), "getPrePlayItem", "is play back");
                notifyPlayListGet(iPlayListGetListener, mPlayItemArrayList.get(mPosition - 1), null);
            } else {
                if (playItem.getTimeInfoData().getStartTime() > DateUtil.getServerTime()) {
//                    InvalidPlayItem invalidPlayItem = PlayListUtils.translateTVToInvalidPlayItem(playItem);
                    notifyPlayListGetError(iPlayListGetListener, playItem, PlayerConstants.ERROR_CODE_PLAY_LIST_PRE_ITEM_TIME, -1);
                    return;
                }
                setAutoPlay(mPlayItemArrayList.get(mPosition), playItem);
                initLiving(LOAD_PRE_PAGE, playItem, iPlayListGetListener);
            }
        }
    }

    @Override
    public void getNextPlayItem(IPlayListGetListener iPlayListGetListener) {
        if (isPlaySongList) {
            if (mSongListPosition < mSongPlayItemArrayList.size() - 1) {
                PlayerLogUtil.log(getClass().getSimpleName(), "getNextPlayItem", "is son play list");
                notifyPlayListGet(iPlayListGetListener, mSongPlayItemArrayList.get(++mSongListPosition), null);
            }
        } else {
            if (PlayerPreconditions.checkNull(mPlayItemArrayList)) {
                notifyPlayListGetError(iPlayListGetListener, new InvalidPlayItem(), PlayerConstants.ERROR_CODE_PLAY_LIST_ONE_KEY_TO_TOP_ALBUM_NEXT_ITEM_PAGE_NULL, -1);
                return;
            }
            if (mPosition + 1 < 0) {
                notifyPlayListGetError(iPlayListGetListener, new InvalidPlayItem(), PlayerConstants.ERROR_CODE_PLAY_LIST_NEXT_INDEX_OUT_OF_BOUNDS, -1);
                return;
            }
            if (mPosition + 1 >= mPlayItemArrayList.size()) {
                PlayerLogUtil.log(getClass().getSimpleName(), "getNextPlayItem", "current page is end");
                if (hasNextPage()) {
                    PlayerLogUtil.log(getClass().getSimpleName(), "getNextPlayItem", "has next page");
                    loadNextPage(iPlayListGetListener);
                } else {
                    notifyPlayListGetError(iPlayListGetListener, new InvalidPlayItem(), PlayerConstants.ERROR_CODE_PLAY_LIST_IS_LAST_ONE, -1);
                }
                return;
            }
            PlayerLogUtil.log(getClass().getSimpleName(), "getNextPlayItem", "position = " + mPosition);
            TVPlayItem playItem = (TVPlayItem) (mPlayItemArrayList.get(mPosition + 1));
            if (PlayerPreconditions.checkNull(playItem)) {
                notifyPlayListGetError(iPlayListGetListener, new InvalidPlayItem(), PlayerConstants.ERROR_CODE_PLAY_LIST_NEXT_ITEM_NULL, -1);
                return;
            }
            if (playItem.getStatus() == PlayerConstants.BROADCAST_STATUS_PLAYBACK) {
                PlayerLogUtil.log(getClass().getSimpleName(), "getNextPlayItem", "is play back");
                notifyPlayListGet(iPlayListGetListener, mPlayItemArrayList.get(mPosition + 1), null);
            } else {
                if (playItem.getTimeInfoData().getStartTime() > DateUtil.getServerTime()) {
//                    InvalidPlayItem invalidPlayItem = PlayListUtils.translateTVToInvalidPlayItem(playItem);
                    notifyPlayListGetError(iPlayListGetListener, playItem, PlayerConstants.ERROR_CODE_PLAY_LIST_NEXT_ITEM_TIME, -1);
                    return;
                }
                setAutoPlay(mPlayItemArrayList.get(mPosition), playItem);
                initLiving(LOAD_NEXT_PAGE, playItem, iPlayListGetListener);
            }
        }
    }

    private void setAutoPlay(PlayItem prePlayItem, PlayItem playItem) {
        if (prePlayItem == null || playItem == null) {
            return;
        }

        PlayItem resItem = prePlayItem;
        //如果自动播放，设置一个标记：播放器自动切换。
        String autoPlayToNext = prePlayItem.getMapCacheData(PlayItemConstants.ITEM_KEY_BROADCAST_AUTO_PLAY_NEXT_ITEM);
        //需要额外判断mCurPlayItem的数据，因为如果上层自组播单，
        // 可能造成PlayControl中正播放的PlayItem与当前播单中的PlayItem不是同一个对象的情况发生
        //一旦发生这种情况，自动切换到下一时段节目时设置的MapCacheData数据将会无法在prePlayItem中获取到
        if (StringUtil.isEmpty(autoPlayToNext) && mCurPlayItem != null) {
            autoPlayToNext = mCurPlayItem.getMapCacheData(PlayItemConstants.ITEM_KEY_BROADCAST_AUTO_PLAY_NEXT_ITEM);
            resItem = mCurPlayItem;
        }
        if (!StringUtil.isEmpty(autoPlayToNext)) {
            resItem.removeMapCacheData(PlayItemConstants.ITEM_KEY_BROADCAST_AUTO_PLAY_NEXT_ITEM);
            playItem.addMapCacheData(PlayItemConstants.ITEM_KEY_BROADCAST_AUTO_PLAY_NEXT_ITEM, autoPlayToNext);
        }

        resItem = prePlayItem;
        String autoPlay = prePlayItem.getMapCacheData(PlayItemConstants.ITEM_KEY_BROADCAST_NO_PLAY);
        if (StringUtil.isEmpty(autoPlay) && mCurPlayItem != null) {
            autoPlay = mCurPlayItem.getMapCacheData(PlayItemConstants.ITEM_KEY_BROADCAST_NO_PLAY);
            resItem = mCurPlayItem;
        }
        if (!StringUtil.isEmpty(autoPlay)) {
            resItem.removeMapCacheData(PlayItemConstants.ITEM_KEY_BROADCAST_NO_PLAY);
            resItem.setPosition(0);
            playItem.addMapCacheData(PlayItemConstants.ITEM_KEY_BROADCAST_NO_PLAY, autoPlay);
        }
    }

    private void initLiving(int type, PlayItem playItem, IPlayListGetListener iPlayListGetListener) {
        PlayerLogUtil.log(getClass().getSimpleName(), "initLiving", "get living info id: " + playItem.getAudioId());
        int errorCode = -1;
        if (type == LOAD_PRE_PAGE) {
            errorCode = PlayerConstants.ERROR_CODE_PLAY_LIST_PRE_PAGE_SERVER;
        } else if (type == LOAD_NEXT_PAGE) {
            errorCode = PlayerConstants.ERROR_CODE_PLAY_LIST_NEXT_PAGE_SERVER;
        }
        int finalErrorCode = errorCode;
        mTVRequest.getTVProgramDetails(playItem.getAudioId(), new HttpCallback<TVProgramDetails>() {
            @Override
            public void onSuccess(TVProgramDetails programDetails) {
                PlayerLogUtil.log(getClass().getSimpleName(), "initLiving", "get detail success");
                if (PlayerPreconditions.checkNull(programDetails)) {
                    return;
                }
                PlayerLogUtil.log(getClass().getSimpleName(), "initLiving", "get detal success status: " + programDetails.getStatus());
                TVPlayItem TVPlayItem = (TVPlayItem) playItem;
                TVPlayItem.setStatus(PlayerConstants.BROADCAST_STATUS_LIVING);
                TVPlayItem.getTimeInfoData().setStartTime(programDetails.getStartTime());
                TVPlayItem.getTimeInfoData().setFinishTime(programDetails.getFinishTime());
                TVPlayItem.getTimeInfoData().setBeginTime(programDetails.getBeginTime());
                TVPlayItem.getTimeInfoData().setEndTime(programDetails.getEndTime());
                TVPlayItem.getTimeInfoData().setCurSystemTime(DateUtil.getServerTime());
                PlayerLogUtil.log(getClass().getSimpleName(), "initLiving", "success, old url = " + playItem.getPlayUrl() + " , new url= " + programDetails.getPlayUrl());
                TVPlayItem.setPlayUrl(programDetails.getPlayUrl());
                TVPlayItem.setPlayInfoList(programDetails.getPlayInfoList());
                TVPlayItem.setBackPlayInfoList(programDetails.getBackPlayInfoList());
                notifyPlayListGet(iPlayListGetListener, playItem, null);
            }

            @Override
            public void onError(ApiException exception) {
                PlayerLogUtil.log(getClass().getSimpleName(), "initLiving", "error");
                notifyPlayListGetError(iPlayListGetListener, new InvalidPlayItem(), finalErrorCode, -1);
            }
        });
    }

    private long string2Long(String strValue) {
        long longValue = 0;
        if (!StringUtil.isEmpty(strValue)) {
            try {
                longValue = Long.parseLong(strValue);
            } catch (Exception e) {

            }
        }
        return longValue;
    }

    private void updatePlayListContent(ArrayList<PlayItem> playItemArrayList, IPlayListGetListener iPlayListGetListener) {
        mPlayItemArrayList.addAll(playItemArrayList);
        int index = PlayListUtils.getLivingTVPlayItem(playItemArrayList);
        if (index >= playItemArrayList.size()) {
            index = 0;
        }
        mPosition = index;
        notifyPlayListGet(iPlayListGetListener, playItemArrayList.get(index), playItemArrayList);
        notifyPlayListChange(playItemArrayList);
    }

    private void initPlayListInfo(TVDetails tvDetails) {
        if (tvDetails != null) {
            mPlaylistInfo.setAlbumName(tvDetails.getName());
            mPlaylistInfo.setAlbumPic(tvDetails.getImg());
        }
    }

    @Override
    public PlayItem getPlayItem(PlayerBuilder playerBuilder) {
        long tempId = string2Long(playerBuilder.getId());
        if (isPlaySongList) {
            for (int i = 0; i < mSongPlayItemArrayList.size(); i++) {
                PlayItem playItem = mSongPlayItemArrayList.get(i);
                if (PlayerPreconditions.checkNull(playItem)) {
                    continue;
                }
                if (playItem.getAudioId() == tempId) {
                    PlayerLogUtil.log(getClass().getSimpleName(), "getPlayItem", "son play list has id");
                    mSongListPosition = i;
                    isPlaySongList = true;
                    PlayItem playItem1 = mSongPlayItemArrayList.get(mSongListPosition);
                    if (playItem1 != null) {
                        playItem1.setPosition(0);
                    }
                    return playItem1;
                }
            }
        }
        isPlaySongList = false;
        mSongPlayItemArrayList.clear();

        TVPlayItem playItem = (TVPlayItem) super.getPlayItem(playerBuilder);
        if (PlayerPreconditions.checkNull(playItem)) {
            return null;
        }
        if (playItem.getTimeInfoData().getFinishTime() < DateUtil.getServerTime()) {
            playItem.setStatus(PlayerConstants.BROADCAST_STATUS_PLAYBACK);
        }
        playItem.getTimeInfoData().setCurSystemTime(DateUtil.getServerTime());
        playItem.setPosition(0);
        return playItem;
    }


    @Override
    public ArrayList getSongPlayList() {
        return mSongPlayItemArrayList;
    }

    @Override
    public void addSongPlayItem(Object o) {
        mSongPlayItemArrayList.addAll((ArrayList<PlayItem>) o);
    }

    @Override
    public void removeSongPlayItem(Object o) {
        mSongPlayItemArrayList.clear();
    }

    @Override
    public boolean isPlayingSonList() {
        return isPlaySongList;
    }

    @Override
    public boolean isExistPlayItem(long id) {
        boolean isExist = super.isExistPlayItem(id);
        if (isExist) {
            return true;
        }
        PlayerLogUtil.log(getClass().getSimpleName(), "isExistPlayItem", "father play list not has");
        if (PlayerPreconditions.checkNull(mSongPlayItemArrayList)) {
            return false;
        }
        for (int i = 0; i < mSongPlayItemArrayList.size(); i++) {
            PlayItem playItem = mSongPlayItemArrayList.get(i);
            if (PlayerPreconditions.checkNull(playItem)) {
                continue;
            }
            if (playItem.getAudioId() == id) {
                PlayerLogUtil.log(getClass().getSimpleName(), "isExistPlayItem", "son play list, position = " + i);
                mSongListPosition = i;
                isPlaySongList = true;
                return true;
            }
        }
        PlayerLogUtil.log(getClass().getSimpleName(), "isExistPlayItem", "son play list not has");
        return false;
    }

    @Override
    public boolean hasNext() {
        if (isPlaySongList) {
            if (mSongListPosition + 1 < 0) {
                return false;
            }
            if (mSongListPosition + 1 < mPlayItemArrayList.size()) {
                return true;
            }
            return false;
        }
        return super.hasNext();
    }

    @Override
    public void release() {
        super.release();
        if (mSongPlayItemArrayList != null) {
            mSongPlayItemArrayList.clear();
            isPlaySongList = false;
            mSongListPosition = -1;
        }
    }

    @Override
    public void setCurPosition(PlayItem playItem) {
        if (isPlaySongList) {
            for (int i = 0; i < mPlayItemArrayList.size(); i++) {
                PlayItem playItemTemp = mPlayItemArrayList.get(i);
                if (PlayerPreconditions.checkNull(playItemTemp)) {
                    continue;
                }
                if (playItemTemp.getAudioId() == playItem.getAudioId()) {
                    playItem.setPosition(0);
                    mSongListPosition = i;
                    PlayerLogUtil.log(getClass().getSimpleName(), "setCurPosition", "position: " + mSongListPosition);
                    return;
                }
            }
        } else {
//            TVPlayItem tvPlayItem = ((TVPlayItem)playItem);
//            if(tvPlayItem.getTimeInfoData().getStartTime() > DateUtil.getServerTime()){
////                InvalidPlayItem invalidPlayItem = PlayListUtils.translateTVToInvalidPlayItem(tvPlayItem);
////                notifyPlayListChangeError(invalidPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_SET_ITEM_TIME, -1);
//                return;
//            }
            super.setCurPosition(playItem);
        }
    }

    @Override
    public int getCurPosition() {
        if (isPlaySongList) {
            return mSongListPosition;
        }
        return super.getCurPosition();
    }
}
