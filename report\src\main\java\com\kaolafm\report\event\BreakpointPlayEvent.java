package com.kaolafm.report.event;

import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.log.Logging;
import com.kaolafm.report.model.PlayReportParameter;
import com.kaolafm.report.util.ReportConstants;

/**
 * @Package: com.kaolafm.report.event
 * @Description: 断点续播
 * @Author: Maclay
 * @Date: 10:07
 */
public class BreakpointPlayEvent extends BaseReportEventBean {
    /**
     * 1.当前通过qqapi接口获取的内容（排行榜等）单曲id为qqapi
     * 2.QQ音乐电台（一人一首招牌歌等）中单曲，上报的audioid 为 qqradio
     */
    private String audioid;
    /**
     * 专辑同radioid；电台流为当前播放单曲所属专辑的id
     * qq api接口获取内容，为空
     * qq音乐电台的单曲，为空
     */
    private String albumid;

    /**
     * 播放类型	0：离线播放；1：在线播放
     */
    private String type = ReportConstants.PLAY_TYPE_ON_LINE;
    /**
     * 播放器位置	1：app播放器；2：外部播放器
     */
    private String position = ReportConstants.POSITION_INNER_APP;

    /**
     * 播放器实际播放时长
     */
    private String playtime;

    /**
     * 开机收听 0:否；1：是
     */
    private String remarks1;
    /**
     * 1语音点播；2搜索结果选择；3其他 4断点续播 5一键收听
     */
    private String remarks2;

    /**
     * 首次收听 0: 否; 1:是
     */
    private String remarks4 = "0";

    /**
     * 内置播放器必传，固定值：player
     */
    private String remarks6 = "player";

    private String ai_mz_location;

    /**
     * 0点播 1广播
     */
    private int other_type = 0;

    /**
     * 播放来源
     */
    private String source;

    /**
     * 精品 VIP 无
     */
    private String tag;
    /**
     * 0免费 1付费 2试听
     */
    private int audioid_type;
    /**
     * 1直播中 2回访中 直播不存在断点，此处默认2
     */
    private int status_fm = 2;

    public BreakpointPlayEvent() {
        setEventcode(ReportConstants.EVENT_ID_BREAKPOINT_PLAY);
    }

    public String getAudioid() {
        return audioid;
    }

    public void setAudioid(String audioid) {
        this.audioid = audioid;
    }

    public String getAlbumid() {
        return albumid;
    }

    public void setAlbumid(String albumid) {
        this.albumid = albumid;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getPlaytime() {
        return playtime;
    }

    public void setPlaytime(String playtime) {
        this.playtime = playtime;
    }

    public String getRemarks1() {
        return remarks1;
    }

    public void setRemarks1(String remarks1) {
        this.remarks1 = remarks1;
    }

    public String getRemarks2() {
        return remarks2;
    }

    public void setRemarks2(String remarks2) {
        this.remarks2 = remarks2;
    }

    public String getRemarks4() {
        return remarks4;
    }

    public void setRemarks4(String remarks4) {
        this.remarks4 = remarks4;
    }

    public String getRemarks6() {
        return remarks6;
    }

    public void setRemarks6(String remarks6) {
        this.remarks6 = remarks6;
    }

    public String getAi_mz_location() {
        return ai_mz_location;
    }

    public void setAi_mz_location(String ai_mz_location) {
        this.ai_mz_location = ai_mz_location;
    }

    public int getOther_type() {
        return other_type;
    }

    public void setOther_type(int other_type) {
        this.other_type = other_type;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public int getAudioid_type() {
        return audioid_type;
    }

    public void setAudioid_type(int audioid_type) {
        this.audioid_type = audioid_type;
    }

    public int getStatus_fm() {
        return status_fm;
    }

    public void setStatus_fm(int status_fm) {
        this.status_fm = status_fm;
    }


    public void playParameterToEvent(PlayReportParameter parameter) {
        setAlbumid(parameter.getAlbumid());
        setAudioid(parameter.getAudioid());
        long total = parameter.getTotalLength() / 1000;
        long play = parameter.getPlayPosition() / 1000;
        if (play > total) {
            play = total;
        }
        setPlaytime(String.valueOf(play));
        setPosition(parameter.getPosition());

        setRemarks1(parameter.getIsStartFirstPlay());
        setRemarks2("4");
//        setRemarks2(parameter.getContentObtainType());
        setRemarks4(parameter.getIsFirst());
        setPlayid(parameter.getPlayId());
        if (!StringUtil.isEmpty(parameter.getInnerPlayer())) {
            Logging.i(ReportConstants.REPORT_TAG, "是内部播放器");
            setRemarks6(parameter.getInnerPlayer());
        }
        setAi_mz_location(parameter.getRadioType());
        setSource(parameter.getAudioSource());
        setAudioid_type(parameter.getAudioid_type());
        setTag(parameter.getTag());
    }
}
