package com.kaolafm.opensdk.player.core.ijk;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Bitmap;
import android.net.Uri;
import android.util.Log;
import android.view.Surface;
import android.view.SurfaceHolder;

import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.di.component.ComponentKit;
import com.kaolafm.opensdk.player.core.listener.IPlayerBufferProgressListener;
import com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener;
import com.kaolafm.opensdk.player.core.listener.IPlayerStateCoreListener;
import com.kaolafm.opensdk.player.core.listener.IPlayerStateVideoCoreListener;
import com.kaolafm.opensdk.player.core.media.IRenderView;
import com.kaolafm.opensdk.player.core.model.AMediaPlayer;
import com.kaolafm.opensdk.player.core.model.AudioFadeConfig;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.ref.WeakReference;

import io.reactivex.Single;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import tv.danmaku.ijk.media.player.IjkMediaPlayer;
import tv.danmaku.ijk.media.player.IjkMediaPlayerConstants;

import static tv.danmaku.ijk.media.player.IjkMediaPlayerConstants.STREAM_MUSIC;

/**
 * <AUTHOR> on 2019-05-24.
 */

public class IJKMediaPlayerAdapter extends AMediaPlayer {
    private static final String TAG = "IJKMediaPlayerAdapter";

    private static final float DEF_PLAYBACKRATE = -1f;
    /**
     * 播放状态
     */
    private IPlayerStateCoreListener mIPlayerState;
    private IPlayerStateVideoCoreListener mIPlayerStateVideoCoreListener;

    /**
     * 缓冲回调
     */
    private IPlayerBufferProgressListener mIPlayerBufferProgressListener;

    /**
     * 播放器初始化成功
     */
    private IPlayerInitCompleteListener mIPlayerInitCompleteListener;

    private IjkMediaPlayer mIjkMediaPlayer;
    /**
     * 正在播放的url
     */
    private String mDataSource;
    private WeakReference<VideoView> mVideoViewRef;
    /**
     * 正在播放的SurfaceHolder
     */
    private SurfaceHolder mSurfaceHolder;

    /**
     * 当前播放状态
     */
    private int mPlayStatus;

    /**
     * 当前是否是 默认播放. 为了满足特殊需求.
     */
    private boolean isNotAutoPlay = false;

    /**
     * 视频宽高
     */
    private int mVideoWidth, mVideoHeight;

    /**
     * 倍速
     */
    private float mPlaybackRate = DEF_PLAYBACKRATE;

    private boolean mNeedResetLastPlaybackRateFlag;

    public IJKMediaPlayerAdapter() {
        mIjkMediaPlayer = new IjkMediaPlayer();
        mIjkMediaPlayer.setIjkPlayerCallBack(new IJKCallBack(IJKMediaPlayerAdapter.this));
    }

    @Override
    public long getDuration() {
        return mIjkMediaPlayer.getDuration();
    }

    @Override
    public long getCurrentPosition() {
        return mIjkMediaPlayer.getCurrentPosition();
    }

    @Override
    public boolean isPlaying() {
        return mIjkMediaPlayer.isPlaying();
    }

    @Override
    public void pause() {
        try {
            mIjkMediaPlayer.pause();
        } catch (Exception e) {
            e.printStackTrace();
        }
//        notifyPlayerPaused();
    }

    @Override
    public void play() {
        try {
            mIjkMediaPlayer.play();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void preload(String url) {

    }

    @Override
    public void reset(boolean needResetLastPlaybackRateFlag) {
        PlayerLogUtil.log(getClass().getSimpleName(), "reset() --- needResetLastPlaybackRateFlag=" + needResetLastPlaybackRateFlag);
        mNeedResetLastPlaybackRateFlag = needResetLastPlaybackRateFlag;
        resetIjk();
        if (mNeedResetLastPlaybackRateFlag) {
            resetPlaybackRate("reset()");
        }
        //notifyPlayerEnd();
        notifyPlayerIdle();
    }

    private void resetIjk() {
        try {
            mIjkMediaPlayer.reset();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void release() {
        try {
            mIjkMediaPlayer.release();
        } catch (Exception e) {

        }
    }

    @Override
    public void prepare() {
        prepare(0);
    }

    @Override
    public void prepareAsync() {
        try {
            mIjkMediaPlayer.prepareAsync();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void prepare(int needSeek) {
        prepare(needSeek, STREAM_MUSIC);
    }

    @Override
    public void prepare(int needSeek, int streamTypeChannel) {
        try {
            mIjkMediaPlayer.prepare(needSeek, streamTypeChannel);
        } catch (Exception e) {
            e.printStackTrace();
        }
        notifyPlayerPreparing();
    }

    @Override
    public void seek(long msec) {
        try {
            mIjkMediaPlayer.seek(msec);
            notifySeekStart();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void setDataSource(String source) {
        mDataSource = source;
        try {
            mIjkMediaPlayer.setDataSource(source);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void setPlaybackRate(float rate) {
        PlayerLogUtil.log(getClass().getSimpleName(), "setPlaybackRate() --- playbackRate=" + rate);
        try {
            mIjkMediaPlayer.setPlaybackRate(rate);
            mPlaybackRate = rate;
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public float getPlaybackRate() {
        float playbackRate = mIjkMediaPlayer.getPlaybackRate();
        PlayerLogUtil.log(getClass().getSimpleName(), "getPlaybackRate()=" + playbackRate);
        return playbackRate;
    }

    @Override
    public void resetPlaybackRate(String refMethodName) {
        mPlaybackRate = DEF_PLAYBACKRATE;
        PlayerLogUtil.log(getClass().getSimpleName(), "resetPlaybackRate() --- refMethodName=" + refMethodName + ", mPlaybackRate=" + mPlaybackRate);
    }

    @Override
    public void setDisplay(SurfaceHolder sh) {
        mSurfaceHolder = sh;
        try {
            mIjkMediaPlayer.setDisplay(sh);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void setSurface(Surface surface) {
        try {
            mIjkMediaPlayer.setSurface(surface);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public boolean getCurrentFrame(Bitmap bitmap) {
        try {
            return mIjkMediaPlayer.getCurrentFrame(bitmap);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public void stop() {
        notifyPlayerIdle();
        stopIjk();
    }

    @Override
    public void rePlay() {
        try {
            mIjkMediaPlayer.reset();
            mIjkMediaPlayer.setDisplay(mSurfaceHolder);
            mIjkMediaPlayer.setDataSource(mDataSource);
            mIjkMediaPlayer.prepare(0, STREAM_MUSIC);
            mIjkMediaPlayer.play();
        } catch (Throwable t) {
            t.printStackTrace();
        }
    }

    private void stopIjk() {
        try {
            mIjkMediaPlayer.stop();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void start(String url, long duration, long position, int streamTypeChannel, boolean audioFadeEnabled, AudioFadeConfig audioFadeConfig, String httpProxy, boolean clearDnsCache, VideoView videoView, boolean needUseLastPlaybackRate, boolean shouldResetPlaybackRate) {

        mDataSource = url;
        mVideoViewRef = new WeakReference<>(videoView);
        PlayerLogUtil.log(getClass().getSimpleName(), "notifyPlayerIdle, processId= " + android.os.Process.myTid());
        notifyPlayerIdle();
        PlayerLogUtil.log(getClass().getSimpleName(), "stopIjk, processId= " + android.os.Process.myTid());
        stopIjk();
        PlayerLogUtil.log(getClass().getSimpleName(), "resetIjk, processId= " + android.os.Process.myTid());
        resetIjk();
        PlayerLogUtil.log(getClass().getSimpleName(), "start() --- needUseLastPlaybackRate=" + needUseLastPlaybackRate + ", shouldResetPlaybackRate=" + shouldResetPlaybackRate + ", mNeedResetLastPlaybackRateFlag=" + mNeedResetLastPlaybackRateFlag + ", mPlaybackRate=" + mPlaybackRate);
        if (needUseLastPlaybackRate && mPlaybackRate > 0) {
            // 此处是最终设置上次播放倍速的地方
            setPlaybackRate(mPlaybackRate);
        } else {
            if (shouldResetPlaybackRate || mNeedResetLastPlaybackRateFlag) {
                resetPlaybackRate("start()");
            }
        }
        mNeedResetLastPlaybackRateFlag = true;

        try {
            PlayerLogUtil.log(getClass().getSimpleName(), "IJKMediaPlayerAdapter.start, processId= " + android.os.Process.myTid());
            PlayerLogUtil.log(getClass().getSimpleName(), "setAutoPlayOnPrepared, processId= " + android.os.Process.myTid());
            PlayerLogUtil.log(getClass().getSimpleName(), "start, audioFadeEnabled = " + audioFadeEnabled +
                    "; audioFadeConfig = " + audioFadeConfig.toString());
            mIjkMediaPlayer.setAutoPlayOnPrepared(false);
        } catch (Exception e) {
            Log.i(PlayerConstants.LOG_TAG, "mIjkMediaPlayer.setAutoPlayOnPrepared exception");
            e.printStackTrace();
        }

        try {
            if (!StringUtil.isEmpty(httpProxy)) {
                mIjkMediaPlayer.setProxyAddress(httpProxy);
            } else {
                mIjkMediaPlayer.clearProxyAddress();
            }
            if (clearDnsCache) {
                mIjkMediaPlayer.clearDnsCache("1");
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        PlayerLogUtil.log(getClass().getSimpleName(), "setAudioFadeEnabled, processId= " + android.os.Process.myTid() + "; audioFadeEnabled = " + audioFadeEnabled);
        // 设置淡入淡出效果
        setAudioFadeEnabled(audioFadeEnabled);
        if (audioFadeEnabled) {
            if (null == audioFadeConfig) {
                AudioFadeConfig audioFadeConfig1 = new AudioFadeConfig();
                setAudioFadeConfig(audioFadeConfig1);
                PlayerLogUtil.log(getClass().getSimpleName(), "->start, audioFadeEnabled = " + audioFadeEnabled +
                        "audioFadeConfig1" + audioFadeConfig1.toString());
            } else {
                setAudioFadeConfig(audioFadeConfig);
                PlayerLogUtil.log(getClass().getSimpleName(), "->start, audioFadeEnabled = " + audioFadeEnabled +
                        "audioFadeConfig" + audioFadeConfig.toString());
            }
        }

        if (videoView != null) {
            videoView.setMediaPlayer(this);
            videoView.setAspectRatio(IRenderView.AR_ASPECT_FIT_PARENT);
            videoView.setVideoURI(Uri.parse(url));
//                videoView.bindSurface();
            PlayerLogUtil.log(getClass().getSimpleName(), "setDisplay, processId= " + android.os.Process.myTid());
//                setDisplay(mVideoView.getSurfaceHolder());
        }

        PlayerLogUtil.log(getClass().getSimpleName(), "setDataSource, processId= " + android.os.Process.myTid());
        setDataSource(url);

        if (duration > 0) {
            setDuration(duration, duration);
        }
        PlayerLogUtil.log(getClass().getSimpleName(), "prepare, processId= " + android.os.Process.myTid());
        if (position > 0) {
            seekAtStart(position);
            prepare(1, streamTypeChannel);
        } else {
            prepare(0, streamTypeChannel);
        }

        if (videoView != null) {
            videoView.notifyPreparing();
        }
    }

    @Override
    public void setAudioFadeConfig(AudioFadeConfig audioFadeConfig) {
        PlayerLogUtil.log(getClass().getSimpleName(), "->setAudioFadeConfig, audioFadeConfig" + audioFadeConfig.toString());

        if (audioFadeConfig.isPlayStartFadeIn()) {
            setAudioFadeInDuration(0x11, audioFadeConfig.getPlayStartFadeInDuration());
        }
        if (audioFadeConfig.isSeekStartFadeIn()) {
            setAudioFadeInDuration(0x12, audioFadeConfig.getSeekStartFadeInDuration());
        }
        if (audioFadeConfig.isPauseStartFadeIn()) {
            setAudioFadeInDuration(0x14, audioFadeConfig.getPauseStartFadeInDuration());
        }
        if (audioFadeConfig.isPlayFinishFadeOut()) {
            setAudioFadeOutDuration(0x21, audioFadeConfig.getPlayFinishFadeOutDuration());
        }
        if (audioFadeConfig.isPlayStopFadeOut()) {
            setAudioFadeOutDuration(0x22, audioFadeConfig.getPlayStopFadeOutDuration());
        }
        if (audioFadeConfig.isPauseFadeOut()) {
            setAudioFadeOutDuration(0x24, audioFadeConfig.getPauseFadeOutDuration());
        }

    }

    @Override
    public String getDnsAddress() {
        return mIjkMediaPlayer.getDnsAddress();
    }

    @Override
    public void setMediaVolume(float leftVolume, float rightVolume) {
        mIjkMediaPlayer.setMediaVolume(leftVolume, rightVolume);
    }

    /**
     * 设置AudioTrack的 Usage和ContentType
     *
     * @param usage
     * @param contentType
     */
    @Override
    public void setUsageAndContentType(int usage, int contentType) {
        mIjkMediaPlayer.setAttributesUsage(usage);
        mIjkMediaPlayer.setAttributesContentType(contentType);
    }

    @Override
    public void setPlayerStateListener(IPlayerStateCoreListener iPlayerState) {
        mIPlayerState = iPlayerState;
    }

    @Override
    public void setPlayerStateVideoListener(IPlayerStateVideoCoreListener iPlayerStateVideoCoreListener) {
        mIPlayerStateVideoCoreListener = iPlayerStateVideoCoreListener;
    }

    @Override
    public void setBufferProgressListener(IPlayerBufferProgressListener progressListener) {
        mIPlayerBufferProgressListener = progressListener;
    }

    @Override
    public void setInitPlayerInitCompleteListener(IPlayerInitCompleteListener initPlayerInitCompleteListener) {
        mIPlayerInitCompleteListener = initPlayerInitCompleteListener;
    }

    @Override
    public void setDuration(long urlDuration, long totalDuration) {
        try {
            mIjkMediaPlayer.setDuration(urlDuration, totalDuration);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void setPlayRatio(float ratio) {

    }

    @Override
    public void seekAtStart(long msec) {
        try {
            mIjkMediaPlayer.seekAtStart(msec);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public int getPlayStatus() {
        return mPlayStatus;
    }


    public void setPlayStatus(int playStatus) {
        this.mPlayStatus = playStatus;
    }

    private static class IJKCallBack implements IjkMediaPlayer.ijkPlayerCallBack {
        private int mArg1;
        private int mArg2;
        private WeakReference<IJKMediaPlayerAdapter> ijkMediaPlayerAdapterWeakReference;

        public IJKCallBack(IJKMediaPlayerAdapter ijkMediaPlayerAdapter) {
            ijkMediaPlayerAdapterWeakReference = new WeakReference<>(ijkMediaPlayerAdapter);
        }


        private void rePlay() {
            IJKMediaPlayerAdapter ijkMediaPlayerAdapter = ijkMediaPlayerAdapterWeakReference.get();
            if (ijkMediaPlayerAdapter == null) {
                return;
            }
            try {
                ijkMediaPlayerAdapter.reset(true);
                ijkMediaPlayerAdapter.setDisplay(ijkMediaPlayerAdapter.mSurfaceHolder);
                ijkMediaPlayerAdapter.setDataSource(ijkMediaPlayerAdapter.mDataSource);
                ijkMediaPlayerAdapter.prepare();
                ijkMediaPlayerAdapter.play();
            } catch (Throwable t) {
                t.printStackTrace();
            }
        }


        @Override
        public void message(int what, int arg1, int arg2, Object obj) {
            //ijkplayer中抛出 post_event(env, weak_thiz, MEDIA_ERROR（what）, MEDIA_ERROR_IJK_PLAYER(arg1), msg.arg1(arg2));
            IJKMediaPlayerAdapter ijkMediaPlayerAdapter = ijkMediaPlayerAdapterWeakReference.get();
            if (ijkMediaPlayerAdapter == null) {
                return;
            }
            if (what != IjkMediaPlayer.MEDIA_PLAYER_PTS_UPDATE && what != IjkMediaPlayer.MEDIA_PLAYER_VOD_PTS_PRELOAD_UPDATE &&
                    what != IjkMediaPlayer.MEDIA_BUFFERING_UPDATE) {
                Log.i(PlayerConstants.LOG_TAG, "message = " + what + ", arg1 = " + arg1 + ", arg2 = " + arg2);
            }
            switch (what) {
                case IjkMediaPlayer.MEDIA_PREPARED: {
                    /**
                     * 没有播放开始回调. prepare结束后,就开始播放了.
                     */
                    if (!ijkMediaPlayerAdapter.isNotAutoPlay) {
                        ijkMediaPlayerAdapter.notifyPlayerPreparingComplete();
                    }
                    ijkMediaPlayerAdapter.isNotAutoPlay = false;
                }
                break;
                case IjkMediaPlayer.MEDIA_PLAYBACK_COMPLETE: {
                    ijkMediaPlayerAdapter.notifyPlayerEnd();
                }
                break;
                case IjkMediaPlayer.MEDIA_STARTED: {
                    ijkMediaPlayerAdapter.notifyPlayerPlaying();
                }
                break;
                case IjkMediaPlayer.MEDIA_PAUSED: {
                    // 暂停
                    ijkMediaPlayerAdapter.notifyPlayerPaused();
                }
                break;
                case IjkMediaPlayer.MEDIA_PLAYER_VOD_PTS_PRELOAD_UPDATE: {
                    int dDuration = arg2;
                    int dPosition = arg1;

                    if (dPosition == dDuration) {
                        ijkMediaPlayerAdapter.notifyBufferProgress(dPosition, dDuration);
                        return;
                    }
                    if (dPosition < 0 || dDuration < 0) {
                        return;
                    }
                    ijkMediaPlayerAdapter.notifyBufferProgress(dPosition, dDuration);
                }
                break;
                case IjkMediaPlayer.MEDIA_PLAYER_PTS_UPDATE: {
                    long position = arg1;
                    long duration = arg2;
//                    if (position < 0 || duration < 0 || (duration < position)) { // 处理M3U8点播节目出现时间戳混乱问题
//                        ijkMediaPlayerAdapter.notifyPlayerEnd();
//                        return;
//                    }
                    ijkMediaPlayerAdapter.notifyProgress(position, duration);
                }
                break;
                case IjkMediaPlayer.MEDIA_BUFFERING_UPDATE:
                    break;
                case IjkMediaPlayer.MEDIA_SEEK_COMPLETE: {
                    ijkMediaPlayerAdapter.notifySeekComplete();
                }
                break;
                case IjkMediaPlayer.MEDIA_ERROR: {
                    if (arg1 == IjkMediaPlayer.MEDIA_ERROR_IJK_PLAYER && arg2 == IjkMediaPlayer.MEDIA_CONNECTION_TIMEOUT_ERROR &&
                            mArg1 != IjkMediaPlayer.MEDIA_ERROR_IJK_PLAYER && mArg2 != IjkMediaPlayer.MEDIA_CONNECTION_TIMEOUT_ERROR) {
                        rePlay();
                    }
                    mArg1 = arg1;
                    mArg2 = arg2;
                    String dnsAddress = ijkMediaPlayerAdapter.getDnsAddress();
                    Log.i(TAG, "notifyPlayerFailed:" + "IJKERROR:" + arg1 + "---->" + arg2);
                    ijkMediaPlayerAdapter.notifyPlayerFailed(arg1, arg2, dnsAddress);//此处将arg1(IJK的错误码)作为what
                }
                break;
                case IjkMediaPlayer.MEDIA_INFO: {
                    if (arg1 == tv.danmaku.ijk.media.player.IMediaPlayer.MEDIA_INFO_BUFFERING_START) {
                        ijkMediaPlayerAdapter.notifyBufferingStart();
                    } else if (arg1 == tv.danmaku.ijk.media.player.IMediaPlayer.MEDIA_INFO_BUFFERING_END) {
                        ijkMediaPlayerAdapter.notifyBufferingEnd();
                    } else if (arg1 == IjkMediaPlayerConstants.MEDIA_INFO_VIDEO_RENDERING_START) {
                        ijkMediaPlayerAdapter.notifyVideoRenderingStart();
                    }
                }
                break;
                case IjkMediaPlayerConstants.MEDIA_IJK_SO_INIT_SUCCESS: {
                    Single.fromCallable(() -> {
                                ijkMediaPlayerAdapter.setAvCodecOption();
                                return 1;
                            }).observeOn(AndroidSchedulers.mainThread()).subscribeOn(Schedulers.io())
                            .subscribe(integer -> {
                                ijkMediaPlayerAdapter.notifyInitComplete();
                            });
                }
                break;
                case IjkMediaPlayerConstants.MEDIA_INTERACTION_FIRED: {
                    //arg1 是 int 类型：当前触发事件的毫秒数（软广告是秒级触发，这里给的是真实触发的毫秒数，可自行转换为秒）
                    //arg2 是 int 类型：广告位ID
                    ijkMediaPlayerAdapter.notifyInteractionFired(arg1, arg2);
                }
                break;
                case IjkMediaPlayerConstants.MEDIA_SET_VIDEO_SIZE: {
                    //视频尺寸获取
                    ijkMediaPlayerAdapter.notifyVideoSize(arg1, arg2);
                }
                break;
                default:
                    break;
            }
        }
    }

    @SuppressLint("Wakelock")
    public void setWakeMode(Context context, int mode) {
    }

    private void notifyPlayerPreparingComplete() {
        setPlayStatus(PlayerConstants.TYPE_PLAYER_PREPARING_COMPLETE);
        if (mIPlayerState != null) {
            mIPlayerState.onPlayerPreparingComplete(mDataSource);
        }
    }

    private void notifyPlayerPreparing() {
        setPlayStatus(PlayerConstants.TYPE_PLAYER_PREPARING);
//        if (mIPlayerState != null) {
//            mIPlayerState.onPlayerPreparing(mDataSource);
//        }
    }

    private void notifyPlayerIdle() {
        setPlayStatus(PlayerConstants.TYPE_PLAYER_IDLE);
        if (mIPlayerState != null) {
            mIPlayerState.onIdle(mDataSource);
        }
    }

    private void notifyPlayerPlaying() {
        setPlayStatus(PlayerConstants.TYPE_PLAYER_PLAYING);
        if (mIPlayerState != null) {
            mIPlayerState.onPlayerPlaying(mDataSource);
        }
    }

    private void notifyPlayerPaused() {
        setPlayStatus(PlayerConstants.TYPE_PLAYER_PAUSED);
        if (mIPlayerState != null) {
            mIPlayerState.onPlayerPaused(mDataSource);
        }
    }

    private void notifyProgress(long progress, long total) {
        // Log.i(PlayerConstants.LOG_PROGRESS_TAG, getClass().getSimpleName() + "   progress = " + progress);
        if (mIPlayerState != null) {
            mIPlayerState.onProgress(mDataSource, progress, total);
        }
    }

    private void notifyPlayerFailed(int what, int extra, String dnsAddress) {
        setPlayStatus(PlayerConstants.TYPE_PLAYER_FAILED);
        if (mIPlayerState != null) {
            mIPlayerState.onPlayerFailed(mDataSource, what, extra, dnsAddress);
        }
    }

    private void notifyPlayerEnd() {
        setPlayStatus(PlayerConstants.TYPE_PLAYER_END);
        if (mIPlayerState != null) {
            mIPlayerState.onPlayerEnd(mDataSource);
        }
    }

    private void notifySeekStart() {
        setPlayStatus(PlayerConstants.TYPE_SEEK_START);
        if (mIPlayerState != null) {
            mIPlayerState.onSeekStart(mDataSource);
        }
    }

    private void notifySeekComplete() {
        setPlayStatus(PlayerConstants.TYPE_SEEK_COMPLETE);
        if (mIPlayerState != null) {
            mIPlayerState.onSeekComplete(mDataSource);
        }
    }

    private void notifyBufferingStart() {
        setPlayStatus(PlayerConstants.TYPE_BUFFERING_START);
        if (mIPlayerState != null) {
            mIPlayerState.onBufferingStart(mDataSource);
        }
    }

    private void notifyBufferingEnd() {
        setPlayStatus(PlayerConstants.TYPE_BUFFERING_END);
        if (mIPlayerState != null) {
            mIPlayerState.onBufferingEnd(mDataSource);
        }
    }

    private void notifyBufferProgress(long position, long total) {
        if (mIPlayerBufferProgressListener != null) {
            mIPlayerBufferProgressListener.onBufferProgress(position, total);
        }
    }

    private void notifyInitComplete() {
        if (mIPlayerInitCompleteListener != null) {
            mIPlayerInitCompleteListener.onPlayerInitComplete(true);
        }
    }

    private void notifyInteractionFired(int position, int id) {
        if (mIPlayerState != null) {
            mIPlayerState.onInteractionFired(mDataSource, position, id);
        }
    }

    private void notifyVideoRenderingStart() {
        if (mIPlayerStateVideoCoreListener != null) {
            mIPlayerStateVideoCoreListener.onPlayerVideoRenderingStart(mDataSource);
        }
    }

    private void notifyVideoSize(int arg1, int arg2) {
        this.mVideoWidth = arg1;
        this.mVideoHeight = arg2;
        if (mIPlayerStateVideoCoreListener != null) {
            mIPlayerStateVideoCoreListener.onPlayerVideoSizeChanged(mDataSource, this.mVideoWidth, this.mVideoHeight);
        }
    }

    @Override
    public void setLogInValid() {
        mIjkMediaPlayer.setLogInValid();
    }

    @Override
    public void setAutoPlayOnPrepared(boolean enabled) {
        isNotAutoPlay = !enabled;
        mIjkMediaPlayer.setAutoPlayOnPrepared(enabled);
    }

    @Override
    public void setAudioFadeEnabled(boolean enabled) {
        mIjkMediaPlayer.setAudioFadeEnabled(enabled);
    }

    /**
     * 设置音频淡入时长
     *
     * @param type 淡入类型（或类型的组合）0x11, 0x12, 0x13：播放开始淡入、Seek起播淡入、Pause起播淡入
     * @param msec 淡入时长，单位为毫秒（ms）
     */
    private void setAudioFadeInDuration(int type, long msec) {
        mIjkMediaPlayer.setAudioFadeInDuration(type, msec);
    }

    /**
     * 设置音频淡出时长
     *
     * @param type 淡出类型（或类型的组合）0x21, 0x22, 0x23：播放结束淡出、Stop起播淡出、Pause停播淡出
     * @param msec 淡出时长，单位为毫秒（ms）
     */
    private void setAudioFadeOutDuration(int type, long msec) {
        mIjkMediaPlayer.setAudioFadeOutDuration(type, msec);
    }

    /**
     * 设置解码参数，环路滤波
     */
    private void setAvCodecOption() {
        Context context = ComponentKit.getInstance().getApplication();
        try {
            copyMcuFile(context, "model.bin", new File(context.getFilesDir() + "/model.bin"));
            mIjkMediaPlayer.setAvCodecOption("model_path", context.getFilesDir() + "/model.bin");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void copyMcuFile(Context context, String sourceFileName, File dest) throws IOException {
        InputStream in = null;
        OutputStream out = null;
        final int READ_BUFFER_SIZE = 4 * 1024 * 1024;
        try {
            in = context.getAssets().open(sourceFileName);
            out = new FileOutputStream(dest);
            byte[] buf = new byte[READ_BUFFER_SIZE];
            int bytesRead;
            while ((bytesRead = in.read(buf)) > 0) {
                out.write(buf, 0, bytesRead);
            }
            Log.d(TAG, "copyFile  finished");
        } catch (Exception e) {
            Log.d(TAG, "copyFile  Exception:" + e);
        } finally {
            if (in != null) {
                in.close();
            }
            if (out != null) {
                out.close();
            }
        }
    }

}
