package com.kaolafm.opensdk.player.core.model;

import android.graphics.Bitmap;
import android.view.Surface;
import android.view.SurfaceHolder;

import com.kaolafm.opensdk.player.core.ijk.IjkVideoView;
import com.kaolafm.opensdk.player.core.ijk.VideoView;
import com.kaolafm.opensdk.player.core.listener.IPlayerBufferProgressListener;
import com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener;
import com.kaolafm.opensdk.player.core.listener.IPlayerStateCoreListener;
import com.kaolafm.opensdk.player.core.listener.IPlayerStateVideoCoreListener;
import com.kaolafm.opensdk.player.logic.listener.IPlayerStateVideoListener;

/**
 * <AUTHOR> on 2019-05-23.
 */

public abstract class AMediaPlayer {

    public abstract void play();

    public abstract void pause();

    public abstract void stop();
    public abstract void rePlay();

//    public abstract void start(String url, long duration, long position, int streamTypeChannel, boolean audioFadeEnabled, AudioFadeConfig audioFadeConfig, String httpProxy, boolean clearDnsCache);

    public abstract void start(String url, long duration, long position, int streamTypeChannel, boolean audioFadeEnabled, AudioFadeConfig audioFadeConfig, String httpProxy, boolean clearDnsCache, VideoView videoView, boolean needUseLastPlaybackRate, boolean shouldResetPlaybackRate);

    public abstract void seek(long mSec);

    public abstract boolean isPlaying();

    public abstract void setPlayerStateListener(IPlayerStateCoreListener listener);
    public abstract void setPlayerStateVideoListener(IPlayerStateVideoCoreListener listener);

    public abstract void setBufferProgressListener(IPlayerBufferProgressListener progressListener);

    /**
     * 设置播放器初始化成功回调
     *
     * @param initPlayerInitCompleteListener
     */
    public abstract void setInitPlayerInitCompleteListener(IPlayerInitCompleteListener initPlayerInitCompleteListener);


    /**
     * 设置进度
     *
     * @param urlDuration
     * @param totalDuration
     */
    abstract public void setDuration(long urlDuration, long totalDuration);

    public abstract void setPlayRatio(float ratio);

    public abstract void seekAtStart(long msec);

    public abstract int getPlayStatus();

    abstract public long getDuration();

    abstract public long getCurrentPosition();

    abstract public void preload(String url);

    abstract public void reset(boolean needResetLastPlaybackRateFlag);

    abstract public void release();

    abstract public void prepare();

    abstract public void prepareAsync();

    abstract public void prepare(int needSeek);

    abstract public void prepare(int needSeek, int stream_type_channel);

    abstract public void setDataSource(String source);
    abstract public void setPlaybackRate(float rate);
    abstract public float getPlaybackRate();
    abstract public void resetPlaybackRate(String refMethodName);
    abstract public void setDisplay(SurfaceHolder sh);

    abstract public void setSurface(Surface surface);
    abstract public boolean getCurrentFrame(Bitmap bitmap);

    public String getDnsAddress() {
        return null;
    }

    public void setMediaVolume(float leftVolume, float rightVolume) {

    }

    /**
     * 设置AudioTrack的 Usage和ContentType
     * @param usage
     * @param contentType
     */
    public void setUsageAndContentType(int usage, int contentType) {

    }

    public void setLoudnessNormalization(int active) {

    }

    public void setLogInValid() {

    }

    public void setAutoPlayOnPrepared(boolean enabled) {

    }

    public void setAudioFadeEnabled(boolean enabled) {

    }

    public void setAudioFadeConfig(AudioFadeConfig audioFadeConfig) {

    }

}
