package com.kaolafm.opensdk.api.brandpage;

import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.brandpage.model.BrandPageListBean;
import com.kaolafm.opensdk.api.operation.model.column.Column;
import com.kaolafm.opensdk.http.core.HttpCallback;

import java.util.List;

/**
 * 品牌电台相关
 *
 * <AUTHOR>
 * @date 2023-03-13
 */
public class BrandPageRequest extends BaseRequest {

    private final BrandPageService mBrandService;

    public BrandPageRequest() {
        mBrandService = obtainRetrofitService(BrandPageService.class);
    }
    /**
     * 获取品牌品牌主页板块列表
     */
    public void getBrandPageList(String brandPageId, HttpCallback<BrandPageListBean> callback) {
        doHttpDeal(mBrandService.getBrandPageList(brandPageId), BaseResult::getResult, callback);
    }

    /**
     * 获取品牌品牌主页内容
     */
    public void getBrandPageContent(String sectionId, String open_uid, String access_token, HttpCallback<List<Column>> callback) {
        doHttpDeal(mBrandService.getBrandPageContent(sectionId, open_uid, access_token), BaseResult::getResult, callback);
    }
}
