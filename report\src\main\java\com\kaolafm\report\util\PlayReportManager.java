package com.kaolafm.report.util;

import android.text.TextUtils;

import com.google.gson.Gson;
import com.kaolafm.base.utils.MD5;
import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.log.Logging;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.BaseReportEventBean;
import com.kaolafm.report.event.BreakpointPlayEvent;
import com.kaolafm.report.event.BroadcastEndListenReportEvent;
import com.kaolafm.report.event.BroadcastPlayingReportEvent;
import com.kaolafm.report.event.BroadcastStartListenReportEvent;
import com.kaolafm.report.event.EndListenReportEvent;
import com.kaolafm.report.event.LivingEndListenReportEvent;
import com.kaolafm.report.event.LivingStartListenReportEvent;
import com.kaolafm.report.event.StartListenReportEvent;
import com.kaolafm.report.listener.IReportEventIntercept;
import com.kaolafm.report.model.PlayReportParameter;

import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

/**
 * <AUTHOR> on 2019/1/25.
 */

public class PlayReportManager {
    private static final int SAVE_POSITION_TIMER = 20;

    private PlayReportParameter mPlayReportParameter;
    private String mPlayType;
    private String mPlayCallBack;

    /**
     * 播放上报拦截器
     */
    private IReportEventIntercept mReportEventIntercept;

    public PlayReportManager() {
        savePlayPosition();
    }

    public void setPlayReportParameter(PlayReportParameter playReportParameter) {
        if (playReportParameter == null) {
            return;
        }
        playReportParameter.setStartTime(String.valueOf(System.currentTimeMillis()));
        if (mPlayReportParameter != null && (!StringUtil.isEmpty(mPlayReportParameter.getRadioid()) || !StringUtil.isEmpty(mPlayReportParameter.getAudioid()))) {
            addEndEventNew();
            clearPlayReportParameter();
        }
        mPlayReportParameter = playReportParameter;
        if (!StringUtil.isEmpty(mPlayType)) {
            mPlayReportParameter.setContentObtainType(mPlayType);
            mPlayType = null;
        }
        if (!StringUtil.isEmpty(mPlayCallBack)) {
            mPlayReportParameter.setSearchResultContent(mPlayCallBack);
            mPlayCallBack = null;
        }
        mPlayReportParameter.setIsStartFirstPlay(ReportParameterManager.getInstance().getStartFirst());
        if (ReportParameterManager.getInstance().isFirstListen()) {
            mPlayReportParameter.setIsFirst("1");
        }

        makePlayId(playReportParameter.getAudioid());
        saveDB();
        addStartEventNew();
    }

    @Deprecated
    private void addStartEvent() {
        if (mPlayReportParameter == null) {
            return;
        }
        if (mPlayReportParameter.getSourceType() == ReportConstants.SOURCE_TYPE_LIVING) {
            addStartListenLiving();
        } else if (mPlayReportParameter.getSourceType() == ReportConstants.SOURCE_TYPE_BROADCAST
                || mPlayReportParameter.getSourceType() == ReportConstants.SOURCE_TYPE_TV) {
            //新增：听电视需要上报，但回放的听电视不上报
            addStartListenBroadcast();
        } else {
            addStartListenAlbum();
        }
    }

    private void addStartEventNew() {
        if (mPlayReportParameter == null) {
            return;
        }
        if (mPlayReportParameter.getSourceType() == ReportConstants.SOURCE_TYPE_LIVING) {
            addStartListenLiving();
        } else {
            addStartListenOther();
        }
//        else if (mPlayReportParameter.getSourceType() == ReportConstants.SOURCE_TYPE_BROADCAST
//                || mPlayReportParameter.getSourceType() == ReportConstants.SOURCE_TYPE_TV) {
//            //新增：听电视需要上报，但回放的听电视不上报
//            addStartListenBroadcast();
//        } else {
//            addStartListenAlbum();
//        }
    }

    private void addStartListenOther() {
        StartListenReportEvent startListenReportEvent = new StartListenReportEvent();
        startListenReportEvent.setAlbumid(mPlayReportParameter.getAlbumid());
        startListenReportEvent.setAudioid(mPlayReportParameter.getAudioid());
        startListenReportEvent.setRadioid(mPlayReportParameter.getRadioid());
        startListenReportEvent.setType(mPlayReportParameter.getType());
        startListenReportEvent.setPosition(mPlayReportParameter.getPosition());
        startListenReportEvent.setRemarks1(mPlayReportParameter.getIsStartFirstPlay());
        startListenReportEvent.setRemarks2(mPlayReportParameter.getContentObtainType());
        startListenReportEvent.setRemarks12(mPlayReportParameter.getContentObtainType());
        startListenReportEvent.setRemarks4(mPlayReportParameter.getIsFirst());
        if (!StringUtil.isEmpty(mPlayReportParameter.getSearchResultContent())) {
            startListenReportEvent.setRemarks9(mPlayReportParameter.getSearchResultContent());
        }
        startListenReportEvent.setAi_mz_location(mPlayReportParameter.getRadioType());
        startListenReportEvent.setRemarks6(mPlayReportParameter.getInnerPlayer());
        startListenReportEvent.setSource(mPlayReportParameter.getAudioSource());
        if (!StringUtil.isEmpty(mPlayReportParameter.getRecommendResultCallback())) {
            startListenReportEvent.setRemarks11(mPlayReportParameter.getRecommendResultCallback());
        }
        startListenReportEvent.setAudioid_type(mPlayReportParameter.getAudioid_type());
        startListenReportEvent.setTag(mPlayReportParameter.getTag());

        if (mPlayReportParameter.getSourceType() == ReportConstants.SOURCE_TYPE_BROADCAST ||
                mPlayReportParameter.getSourceType() == ReportConstants.SOURCE_TYPE_TV) {
            startListenReportEvent.setStatus(mPlayReportParameter.getBroadcast_status());
        }

        startListenReportEvent.setSourcetype(String.valueOf(mPlayReportParameter.getSourceType()));

        reportIntercept(ReportConstants.EVENT_ID_LISTEN_START, startListenReportEvent);
        ReportHelper.getInstance().addEvent(startListenReportEvent, mPlayReportParameter.isSendEventNow());
    }

    private void addStartListenBroadcast() {
        BroadcastStartListenReportEvent startListenReportEvent = new BroadcastStartListenReportEvent();
        startListenReportEvent.setAudioid(mPlayReportParameter.getAudioid());
        startListenReportEvent.setRadioid(mPlayReportParameter.getRadioid());
        startListenReportEvent.setStatus(mPlayReportParameter.getBroadcast_status());
        reportIntercept(ReportConstants.EVENT_ID_BROADCAST_START_LISTEN, startListenReportEvent);
        ReportHelper.getInstance().addEvent(startListenReportEvent, false);
    }

    private void addStartListenLiving() {
        LivingStartListenReportEvent startListenReportEvent = new LivingStartListenReportEvent();
        startListenReportEvent.setLive_id(mPlayReportParameter.getLiveType_live_id());
        startListenReportEvent.setPlan_id(mPlayReportParameter.getLiveType_plan_id());
        startListenReportEvent.setLive_manager_uid(mPlayReportParameter.getLiveType_compereid());
        startListenReportEvent.setPosition(mPlayReportParameter.getLiveType_position());
        startListenReportEvent.setStatus(mPlayReportParameter.getLiveType_status());
        reportIntercept(ReportConstants.EVENT_ID_LIVING_START_LISTEN, startListenReportEvent);
        ReportHelper.getInstance().addEvent(startListenReportEvent, false);
    }

    private void addStartListenAlbum() {
        StartListenReportEvent startListenReportEvent = new StartListenReportEvent();
        startListenReportEvent.setAlbumid(mPlayReportParameter.getAlbumid());
        startListenReportEvent.setAudioid(mPlayReportParameter.getAudioid());
        startListenReportEvent.setRadioid(mPlayReportParameter.getRadioid());
        startListenReportEvent.setType(mPlayReportParameter.getType());
        startListenReportEvent.setPosition(mPlayReportParameter.getPosition());
        startListenReportEvent.setRemarks1(mPlayReportParameter.getIsStartFirstPlay());
        startListenReportEvent.setRemarks2(mPlayReportParameter.getContentObtainType());
        startListenReportEvent.setRemarks12(mPlayReportParameter.getContentObtainType());
        startListenReportEvent.setRemarks4(mPlayReportParameter.getIsFirst());
        if (!StringUtil.isEmpty(mPlayReportParameter.getSearchResultContent())) {
            startListenReportEvent.setRemarks9(mPlayReportParameter.getSearchResultContent());
        }
        startListenReportEvent.setAi_mz_location(mPlayReportParameter.getRadioType());
        startListenReportEvent.setRemarks6(mPlayReportParameter.getInnerPlayer());
        startListenReportEvent.setSource(mPlayReportParameter.getAudioSource());
        if (!StringUtil.isEmpty(mPlayReportParameter.getRecommendResultCallback())) {
            startListenReportEvent.setRemarks11(mPlayReportParameter.getRecommendResultCallback());
        }
        startListenReportEvent.setAudioid_type(mPlayReportParameter.getAudioid_type());
        startListenReportEvent.setTag(mPlayReportParameter.getTag());

        reportIntercept(ReportConstants.EVENT_ID_LISTEN_START, startListenReportEvent);
        ReportHelper.getInstance().addEvent(startListenReportEvent, mPlayReportParameter.isSendEventNow());
    }

    @Deprecated
    private void addEndEvent() {
        if (mPlayReportParameter == null) {
            return;
        }
        if (mPlayReportParameter.getSourceType() == ReportConstants.SOURCE_TYPE_LIVING) {
            LivingEndListenReportEvent endListenReportEvent = new LivingEndListenReportEvent();
            endListenReportEvent.playParameterToEvent(mPlayReportParameter);
            reportIntercept(ReportConstants.EVENT_ID_LIVING_END_LISTEN, endListenReportEvent);
            ReportHelper.getInstance().addEvent(endListenReportEvent, false);
        } else if (mPlayReportParameter.getSourceType() == ReportConstants.SOURCE_TYPE_BROADCAST) {
            BroadcastEndListenReportEvent endListenReportEvent = new BroadcastEndListenReportEvent();
            endListenReportEvent.playParameterToEvent(mPlayReportParameter);
            reportIntercept(ReportConstants.EVENT_ID_BROADCAST_END_LISTEN, endListenReportEvent);
            endListenReportEvent.setPlaytime("10");
            ReportHelper.getInstance().addEvent(endListenReportEvent, false);
        } else {
            if (StringUtil.isEmpty(mPlayReportParameter.getAudioid())) {
                return;
            }
            EndListenReportEvent endListenReportEvent = new EndListenReportEvent();
            endListenReportEvent.playParameterToEvent(mPlayReportParameter);
            reportIntercept(ReportConstants.EVENT_ID_LISTEN_END, endListenReportEvent);
            ReportHelper.getInstance().addEvent(endListenReportEvent, false);
            if (ReportConstants.COTENT_BY_BREAKPOINT.equals(mPlayReportParameter.getContentObtainType())) {
                //断点续播的
                BreakpointPlayEvent breakpointPlayEvent = new BreakpointPlayEvent();
                breakpointPlayEvent.playParameterToEvent(mPlayReportParameter);
                ReportHelper.getInstance().addEvent(breakpointPlayEvent, false);
            }
        }
    }

    private void addEndEventNew() {
        if (mPlayReportParameter == null) {
            return;
        }
        if (mPlayReportParameter.getSourceType() == ReportConstants.SOURCE_TYPE_LIVING) {
            LivingEndListenReportEvent endListenReportEvent = new LivingEndListenReportEvent();
            endListenReportEvent.playParameterToEvent(mPlayReportParameter);
            reportIntercept(ReportConstants.EVENT_ID_LIVING_END_LISTEN, endListenReportEvent);
            ReportHelper.getInstance().addEvent(endListenReportEvent, false);
        } else {
            EndListenReportEvent endListenReportEvent = new EndListenReportEvent();
            endListenReportEvent.playParameterToEvent(mPlayReportParameter);
            reportIntercept(ReportConstants.EVENT_ID_LISTEN_END, endListenReportEvent);
            ReportHelper.getInstance().addEvent(endListenReportEvent, false);
            if (ReportConstants.COTENT_BY_BREAKPOINT.equals(mPlayReportParameter.getContentObtainType())) {
                //断点续播的
                BreakpointPlayEvent breakpointPlayEvent = new BreakpointPlayEvent();
                breakpointPlayEvent.playParameterToEvent(mPlayReportParameter);
                ReportHelper.getInstance().addEvent(breakpointPlayEvent, false);
            }
        }
    }

    public void addBroadcastPlaying() {
        if (mPlayReportParameter == null) {
            return;
        }
        BroadcastPlayingReportEvent broadcastPlayingReportEvent = new BroadcastPlayingReportEvent();
        broadcastPlayingReportEvent.playParameterToEvent(mPlayReportParameter);
        reportIntercept(ReportConstants.EVENT_ID_BROADCAST_PLAYING, broadcastPlayingReportEvent);
        ReportHelper.getInstance().addEvent(broadcastPlayingReportEvent, false);
    }

    /**
     * @param reason       结束原因
     * @param isNeedReport 是否需要上报.
     */
    public void addEndEvent(String reason, boolean isNeedReport) {
        if (mPlayReportParameter == null) {
            return;
        }
        mPlayReportParameter.setChangeType(reason);
        if (isNeedReport) {
            addEndEventNew();
            clearPlayReportParameter();
        }
    }

    /**
     * @param reason           结束原因
     * @param isNeedReport     是否需要上报.
     * @param isClearParameter 是否清除Parameter
     */
    public void addEndEvent(String reason, boolean isNeedReport, boolean isClearParameter) {
        if (mPlayReportParameter != null) {
            mPlayReportParameter.setChangeType(reason);
            if (isNeedReport) {
                addEndEventNew();
                if (isClearParameter) {
                    clearPlayReportParameter();
                }
            }

        }
    }

    public void addBroadcastPlaying(boolean isClearParameter) {
        if (mPlayReportParameter != null) {
            mPlayReportParameter.setChangeType("");
            addBroadcastPlaying();
            if (isClearParameter) {
                clearPlayReportParameter();
            }
        }
    }

    public void clearPlayReportParameter() {
        mPlayReportParameter = null;
        // stopSave();
        ReportHelper.getInstance().deleteDB();
    }

    /**
     * 设置播放长度
     *
     * @param position
     */
    public void setPosition(long position, long totalLength) {
        if (mPlayReportParameter == null) {
            return;
        }
        mPlayReportParameter.setPlayPosition(position);
        mPlayReportParameter.setTotalLength(totalLength);
    }

    private Disposable mDisposable;

    private void savePlayPosition() {
        Logging.i(ReportConstants.REPORT_TAG, "save play position!");
        stopSave();
        mDisposable = Observable.interval(SAVE_POSITION_TIMER, TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(aLong -> saveDB());
    }

    private void saveDB() {
        Logging.i(ReportConstants.REPORT_TAG, "save play position db !");
        if (mPlayReportParameter == null) {
            Logging.i(ReportConstants.REPORT_TAG, "save play position db is null!");
            return;
        }
        if (true) {
            Logging.i(ReportConstants.REPORT_TAG, "save play position db ! test return");
            return;
        }
        String json = new Gson().toJson(mPlayReportParameter);
        Logging.i(ReportConstants.REPORT_TAG, "save play position db save json = " + json);

        ReportHelper.getInstance().saveDB(json);
    }

    private void stopSave() {
        if (mDisposable != null && !mDisposable.isDisposed()) {
            mDisposable.dispose();
            mDisposable = null;
        }
    }

    public void setSearchPlayCallBack(String playType, String callback) {
        mPlayType = playType;
        mPlayCallBack = callback;
    }

    private void makePlayId(String audioId) {
        if (mPlayReportParameter == null) {
            return;
        }
        if (TextUtils.isEmpty(audioId)) {
            return;
        }
        String playId = MD5.getMD5Str(StringUtil.join(audioId, System.currentTimeMillis()));
        mPlayReportParameter.setPlayId(playId);
        ReportParameterManager.getInstance().setPlayId(playId);
    }

    public void initPlayReportParameter(PlayReportParameter playReportParameter) {
        if (playReportParameter == null) {
            mPlayReportParameter = new PlayReportParameter();
            return;
        }
        mPlayReportParameter = playReportParameter;

        if (!StringUtil.isEmpty(mPlayReportParameter.getAudioid())) {
            addEndEventNew();
        }
        clearPlayReportParameter();
    }

    public void setReportEventIntercept(IReportEventIntercept reportEventIntercept) {
        this.mReportEventIntercept = reportEventIntercept;
    }

    public void release() {
        saveDB();
        stopSave();
    }

    private void reportIntercept(String eventCode, BaseReportEventBean baseReportEventBean) {
        if (mReportEventIntercept == null) {
            return;
        }
        mReportEventIntercept.report(eventCode, baseReportEventBean);
    }

    public void setLiveStatus(int liveStatus) {
        if (mPlayReportParameter == null) return;
        mPlayReportParameter.setLiveType_status(liveStatus + "");
    }
}
