package com.kaolafm.opensdk.player.logic.model;

import androidx.annotation.NonNull;

import com.kaolafm.opensdk.player.core.ijk.VideoView;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

/**
 * <AUTHOR> on 2019/3/28.
 */

public class PlayerBuilder {
    private String mId;

    private int mType = PlayerConstants.RESOURCES_TYPE_INVALID;

    /**
     * 排序方式
     */
    private int mSort = PlayerConstants.SORT_ACS;

    /**
     * 是否可以订阅 只有为1的时候不能订阅
     */
    private int noSubscribe;

    private boolean isPlayNow = true;
    private VideoView videoView;

    public PlayerBuilder setId(@NonNull String id) {
        this.mId = id;
        return this;
    }

    public String getId() {
        return mId;
    }

    public int getType() {
        return mType;
    }

    public PlayerBuilder setType(int type) {
        this.mType = type;
        return this;
    }

    public int getSort() {
        return mSort;
    }

    public PlayerBuilder setSort(int mSort) {
        this.mSort = mSort;
        return this;
    }

    public int getNoSubscribe() {
        return noSubscribe;
    }

    public void setNoSubscribe(int noSubscribe) {
        this.noSubscribe = noSubscribe;
    }

    public boolean isPlayNow() {
        return isPlayNow;
    }

    public PlayerBuilder setPlayNow(boolean playNow) {
        isPlayNow = playNow;
        return this;
    }

    public PlayerBuilder setVideoView(VideoView videoView) {
        this.videoView = videoView;
        return this;
    }

    public VideoView getVideoView() {
        return this.videoView;
    }
}
