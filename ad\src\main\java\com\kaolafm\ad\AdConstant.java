package com.kaolafm.ad;

import com.kaolafm.opensdk.http.urlmanager.UrlManager;

/**
 * <AUTHOR>
 * @date 2019-12-31
 */
public class AdConstant {


    /**
     * 广告引擎的host地址
     */
    // attention：域名的test接口目前还没有，必须打release包才能正常获取数据
    public static final String AD_ENGINE_HOST = BuildConfig.DOMAIN_TYPE.contains("test") ? "https://iovae-test.radio.cn" : "https://ae"+BuildConfig.DOMAIN_TYPE+".kaolafm.com";
    public static final String AD_ENGINE_HOST_HTTP = "http://engine.audiobuy.cc";

    public static final String AD_ENGINE_VERSION = "/2.0.0";

    /**
     * 广告长连接地址
     */
    // attention：域名的test接口目前还没有，必须打release包才能正常获取数据
    public static final String AD_SOCKET_HOST = BuildConfig.DOMAIN_TYPE.contains("test") ? "https://iovaew-test.radio.cn" : "https://aew"+BuildConfig.DOMAIN_TYPE+".kaolafm.com";

    public static final String AD_SOCKET_HOST_HTTP = "http://ws.engine.audiobuy.cc";


    /**
     * 广告上报接口
     */
    public static final String AD_REPROT_HOST = "https://at.kaolafm.com";

    public static final String AD_REPROT_HOST_HTTP = "http://track.audiobuy.cc";

    public static final String DOMAIN_NAME_AD_REPORT = "advertising-report";

    public static final String DOMAIN_HEADER_AD_REPORT = UrlManager.DOMAIN_NAME_HEADER + DOMAIN_NAME_AD_REPORT;

    public static final String DOMAIN_NAME_AD = "advertising";

    public static final String DOMAIN_HEADER_AD = UrlManager.DOMAIN_NAME_HEADER + DOMAIN_NAME_AD;

    public static final String DOMAIN_NAME_AD_SOCKET = "advertising-socket";

    /**
     * 获取广告接口
     */
    public static final String ENGINE = AD_ENGINE_VERSION + "/e";

    /**
     * 长连接获取广告URL
     */
    public static final String SOCKET_ENGINE_URL = AD_SOCKET_HOST + ENGINE;

    /**
     * 显示上报
     */
    public static final String REPORT_DISPLAY = "/s.gif";

    /**
     * 开始播放上报
     */
    public static final String REPORT_PLAY = "/p.gif";

    /**
     * 点击事件上报
     */
    public static final String REPORT_CLICK = "/c.gif";

    /**
     * 广告引擎错误上报
     */
    public static final String REPORT_ERROR = "/e.gif";

    /**
     * 激活上报
     */
    public static final String REPORT_ACTIVE = "/a.gif?";

    /**
     * 数据上报，跳过
     */
    public static final String REPORT_SKIP = "/k.gif";

    /**
     * 显示结束时间上报
     */
    public static final String REPORT_DISPLAY_END = "/b.gif";

    /**
     * 显示打断上报
     */
    public static final String REPORT_DISPLAY_INTERRUPT = "/i.gif";

    /**
     * 二次互动开始显示时间上报
     */
    public static final String REPORT_MORE_INTERACTION_DISPLAY = "/m.gif";

    /**
     * 二次互动显示结束时间上报
     */
    public static final String REPORT_MORE_INTERACTION_DISPLAY_END = "/n.gif";

    /**
     * 定时广告类型
     */
    public static final int TYPE_TIMED_ADVERT = 2001;

    public static final String KEY_EXTRA_MONITOR_TYPE = "key_extra_monitor_type";

    public static final String KEY_EXTRA_CLICK_MONITOR_URL = "key_extra_click_monitor_url";

    public static final String KEY_EXTRA_PV_MONITOR_URL = "key_extra_pv_monitor_url";

    /**用于上报的bean的额外信息的key*/
    public static final String KEY_EXTRA_REPORT = "key_extra_report";

    /**定时广告长连接事件*/
    public static final String TIMED_ADVERT_EVENT = "client_new_ad";

    /**
     * 广告保存SP的文件名
     */
    public static final String SP_NAME = "ad";

}
