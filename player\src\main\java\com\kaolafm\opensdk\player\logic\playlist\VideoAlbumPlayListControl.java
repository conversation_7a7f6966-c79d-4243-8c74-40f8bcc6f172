package com.kaolafm.opensdk.player.logic.playlist;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.media.VideoAlbumRequest;
import com.kaolafm.opensdk.api.media.VideoAudioRequest;
import com.kaolafm.opensdk.api.media.model.AlbumDetails;
import com.kaolafm.opensdk.api.media.model.VideoAlbumDetails;
import com.kaolafm.opensdk.api.media.model.VideoAudioDetails;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener;
import com.kaolafm.opensdk.player.logic.model.CustomPlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.PlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.item.VideoAlbumPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.InfoData;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.playlist.innerinterface.IDataListCallback;
import com.kaolafm.opensdk.player.logic.playlist.util.PlayListUtils;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;
import com.kaolafm.opensdk.player.logic.util.PlayerPreconditions;

import java.util.ArrayList;
import java.util.List;

/**
 * 视频专辑-播放逻辑
 * <AUTHOR>

public class VideoAlbumPlayListControl extends BasePlayListControl {
    private static final int LOAD_DATA = 0;
    private static final int LOAD_DATA_NEXT = 1;
    private static final int LOAD_DATA_PRE = 2;
    public static final int LOAD_DATA_PAGE = 3; //获取整页数据
    private static final int LOAD_DATA_PAGE_AND_PLAY_NEXT_ITEM = 4; //获取整页数据后播放下一个
    private static final int LOAD_DATA_PAGE_AND_PLAY_PRE_ITEM = 5; //获取整页数据后播放上一个
    private VideoAlbumRequest mAlbumRequest;
    private VideoAudioRequest mVideoAudioRequest;
    private PlayerBuilder tempPlayerBuilder;
    private int mPageSize = PlayerConstants.PAGE_NUMBER_10;
    private VideoAlbumDetails mAlbumDetails;

    public VideoAlbumPlayListControl() {
        super();

        mAlbumRequest = new VideoAlbumRequest();
        mVideoAudioRequest = new VideoAudioRequest();
    }

    @Override
    public void initPlayList(PlayerBuilder playerBuilder, IPlayListGetListener iPlayListGetListener) {
        PlayerLogUtil.log(getClass().getSimpleName(), "initPlayList", "videoAlbum");
        if (playerBuilder.getType() == PlayerConstants.RESOURCES_TYPE_VIDEO_AUDIO) {
            PlayerLogUtil.log(getClass().getSimpleName(), "initPlayList", "play audio, get audio detail");
            tempPlayerBuilder = new CustomPlayerBuilder();
            ((CustomPlayerBuilder) tempPlayerBuilder).setChildId(playerBuilder.getId()).setType(playerBuilder.getType());
            getAudioInfo(iPlayListGetListener);
            return;
        }
        tempPlayerBuilder = playerBuilder;
        super.initPlayList(tempPlayerBuilder, iPlayListGetListener);
        getAlbumInfo(iPlayListGetListener, false);
    }

    @Override
    public int getCurPosition() {
        if (mPosition != -1 && !isInList(mPlayItemArrayList, mCurPlayItem)) {
            return -1;
        }
        return mPosition;
    }

    @Override
    public void getNextPlayItem(IPlayListGetListener iPlayListGetListener) {
        if (mPlayItemArrayList != null && mCurPlayItem != null && mPosition != -1) {
            boolean isInList = isInList(mPlayItemArrayList, mCurPlayItem);
            if (!isInList) {
                loadPageData(LOAD_DATA_PAGE_AND_PLAY_NEXT_ITEM,
                        mCurPlayItem.getAudioId(), 1, iPlayListGetListener);
                return;
            }
        }
        super.getNextPlayItem(iPlayListGetListener);
    }

    @Override
    public void getPrePlayItem(IPlayListGetListener iPlayListGetListener) {
        if (mPlayItemArrayList != null && mCurPlayItem != null && mPosition != -1) {
            boolean isInList = isInList(mPlayItemArrayList, mCurPlayItem);
            if (!isInList) {
                loadPageData(LOAD_DATA_PAGE_AND_PLAY_PRE_ITEM,
                        mCurPlayItem.getAudioId(), 1, iPlayListGetListener);
                return;
            }
        }
        super.getPrePlayItem(iPlayListGetListener);
    }

    @Override
    public void loadNextPage(IPlayListGetListener iPlayListGetListener) {
        PlayerLogUtil.log(getClass().getSimpleName(), "loadNextPage", "album get next page ...");
        long albumId = string2Long(mPlaylistInfo.getId());

        VideoAlbumPlayItem invalidPlayItem = new VideoAlbumPlayItem();
        InfoData infoData = new InfoData();
        infoData.setAlbumId(albumId);
        invalidPlayItem.setInfodata(infoData);

        loadPlayListData(albumId, mPlaylistInfo.getSort(), mPlaylistInfo.getNextPage(), new IDataListCallback<BasePageResult<List<VideoAudioDetails>>>() {
            @Override
            public void success(BasePageResult<List<VideoAudioDetails>> listBasePageResult) {
                ArrayList<PlayItem> playItemArrayList = PlayListUtils.videoAudioDetailToAlbumPlayItem(listBasePageResult.getDataList(), mPlaylistInfo, mAlbumDetails != null ? mAlbumDetails.getASentenceRecommend() : "");
                if (ListUtil.isEmpty(playItemArrayList)) {
                    notifyPlayListGetError(iPlayListGetListener, invalidPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_NEXT_PAGE_NULL, -1);
                    notifyPlayListChangeError(invalidPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_NEXT_PAGE_NULL, -1);
                    return;
                }
                updatePlayListInfo(LOAD_DATA_NEXT, listBasePageResult);
                updatePlayListContent(LOAD_DATA_NEXT, playItemArrayList, iPlayListGetListener);
            }

            @Override
            public void error(ApiException e) {
                PlayerLogUtil.log(getClass().getSimpleName(), "loadNextPage", "get playlist error....");
                notifyPlayListGetError(iPlayListGetListener, invalidPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_NEXT_PAGE_SERVER, e.getCode());
                notifyPlayListChangeError(invalidPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_NEXT_PAGE_SERVER, e.getCode());
            }
        });
    }

    @Override
    public void loadPrePage(IPlayListGetListener iPlayListGetListener) {
        PlayerLogUtil.log(getClass().getSimpleName(), "loadPrePage", "album get pre page playlist...");
//        if (!mPlaylistInfo.isHasPrePage()) {
//            return;
//        }
        long albumId = string2Long(mPlaylistInfo.getId());

        VideoAlbumPlayItem albumPlayItem = new VideoAlbumPlayItem();
        InfoData infoData = new InfoData();
        infoData.setAlbumId(albumId);
        albumPlayItem.setInfodata(infoData);

        loadPlayListData(albumId, mPlaylistInfo.getSort(), mPlaylistInfo.getPrePage(), new IDataListCallback<BasePageResult<List<VideoAudioDetails>>>() {
            @Override
            public void success(BasePageResult<List<VideoAudioDetails>> listBasePageResult) {
                ArrayList<PlayItem> playItemArrayList = PlayListUtils.videoAudioDetailToAlbumPlayItem(listBasePageResult.getDataList(), mPlaylistInfo, mAlbumDetails != null ? mAlbumDetails.getASentenceRecommend() : "");
                if (ListUtil.isEmpty(playItemArrayList)) {
                    notifyPlayListGetError(iPlayListGetListener, albumPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_PRE_PAGE_NULL, -1);
                    notifyPlayListChangeError(albumPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_PRE_PAGE_NULL, -1);
                    return;
                }
                updatePlayListInfo(LOAD_DATA_PRE, listBasePageResult);
                updatePlayListContent(LOAD_DATA_PRE, playItemArrayList, iPlayListGetListener);
            }

            @Override
            public void error(ApiException e) {
                PlayerLogUtil.log(getClass().getSimpleName(), "loadPrePage", "get playlist error....");
                notifyPlayListGetError(iPlayListGetListener, albumPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_PRE_PAGE_SERVER, e.getCode());
                notifyPlayListChangeError(albumPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_PRE_PAGE_SERVER, e.getCode());
            }
        });
    }

    @Override
    public void loadPageData(int type, long audioId, int pageNum, IPlayListGetListener iPlayListGetListener) {
        PlayerLogUtil.log(getClass().getSimpleName(), "loadPageData", "album get page data playlist...");

        int errorCode1 = -1;
        int errorCode2 = -1;
        if (type == LOAD_DATA_PAGE_AND_PLAY_NEXT_ITEM) {
            errorCode1 = PlayerConstants.ERROR_CODE_PLAY_LIST_ONE_KEY_TO_TOP_ALBUM_NEXT_ITEM_PAGE_NULL;
            errorCode2 = PlayerConstants.ERROR_CODE_PLAY_LIST_ONE_KEY_TO_TOP_ALBUM_NEXT_ITEM_PAGE_SERVER;
        } else if (type == LOAD_DATA_PAGE_AND_PLAY_PRE_ITEM) {
            errorCode1 = PlayerConstants.ERROR_CODE_PLAY_LIST_ONE_KEY_TO_TOP_ALBUM_PRE_ITEM_PAGE_NULL;
            errorCode2 = PlayerConstants.ERROR_CODE_PLAY_LIST_ONE_KEY_TO_TOP_ALBUM_PRE_ITEM_PAGE_SERVER;
        } else if (type == LOAD_DATA_PAGE) {
            errorCode1 = PlayerConstants.ERROR_CODE_PLAY_LIST_ONE_KEY_TO_TOP_ALBUM_PAGE_NULL;
            errorCode2 = PlayerConstants.ERROR_CODE_PLAY_LIST_ONE_KEY_TO_TOP_ALBUM_PAGE_SERVER;
        }
        int finalErrorCode1 = errorCode1;
        int finalErrorCode2 = errorCode2;
        long albumId = string2Long(mPlaylistInfo.getId());

        VideoAlbumPlayItem albumPlayItem = new VideoAlbumPlayItem();
        InfoData infoData = new InfoData();
        infoData.setAlbumId(albumId);
        albumPlayItem.setInfodata(infoData);
        albumPlayItem.setAudioId(audioId);

        loadPlayListData(albumId, audioId, mPlaylistInfo.getSort(), pageNum, new IDataListCallback<BasePageResult<List<VideoAudioDetails>>>() {
            @Override
            public void success(BasePageResult<List<VideoAudioDetails>> listBasePageResult) {
                ArrayList<PlayItem> playItemArrayList = PlayListUtils.videoAudioDetailToAlbumPlayItem(listBasePageResult.getDataList(), mPlaylistInfo, mAlbumDetails != null ? mAlbumDetails.getASentenceRecommend() : "");
                if (ListUtil.isEmpty(playItemArrayList)) {
                    notifyPlayListGetError(iPlayListGetListener, albumPlayItem, finalErrorCode1, -1);
                    return;
                }
                updatePlayListInfo(type, listBasePageResult);
                updatePlayListContent(type, playItemArrayList, iPlayListGetListener);
            }

            @Override
            public void error(ApiException e) {
                PlayerLogUtil.log(getClass().getSimpleName(), "loadPageData", "get playlist error....");
                notifyPlayListGetError(iPlayListGetListener, albumPlayItem, finalErrorCode2, e.getCode());
                notifyPlayListChangeError(albumPlayItem, finalErrorCode2, e.getCode());
            }
        });
    }

    private void loadPlayListData(long albumId, int sort, int pageNum, IDataListCallback<BasePageResult<List<VideoAudioDetails>>> iDataListCallback) {
        loadPlayListData(albumId, 0, sort, pageNum, iDataListCallback);
    }

    /**
     * 加载数据
     *
     * @param albumId
     * @param audioId
     * @param sort
     * @param pageNum
     * @param iDataListCallback
     */
    private void loadPlayListData(long albumId, long audioId, int sort, int pageNum, IDataListCallback<BasePageResult<List<VideoAudioDetails>>> iDataListCallback) {


        mAlbumRequest.getPlaylist(albumId, audioId, sort, mPageSize, pageNum,  new HttpCallback<BasePageResult<List<VideoAudioDetails>>>() {
            @Override
            public void onSuccess(BasePageResult<List<VideoAudioDetails>> listBasePageResult) {
                if (iDataListCallback != null) {
                    iDataListCallback.success(listBasePageResult);
                }
            }

            @Override
            public void onError(ApiException e) {
                if (iDataListCallback != null) {
                    iDataListCallback.error(e);
                }
            }
        });
    }

    private int getNotifyPlayListIndex(ArrayList<PlayItem> playItemArrayList, long audioId) {
        if (audioId <= 0) {
            return 0;
        }
        for (int i = 0; i < playItemArrayList.size(); i++) {
            PlayItem playItem = playItemArrayList.get(i);
            if (PlayerPreconditions.checkNull(playItem)) {
                continue;
            }

            if (playItem.getAudioId() == audioId) {
                return i;
            }
        }

        return 0;
    }

    private void getAlbumInfo(final IPlayListGetListener iPlayListGetListener, boolean isFromAudio) {
        PlayerLogUtil.log(getClass().getSimpleName(), "getAlbumInfo");

//        mPlayItemArrayList.clear();
//        VideoAlbumPlayItem data1 = new VideoAlbumPlayItem();
//        data1.getInfoData().setTitle("蜂鸟计划 中国预告片：速度与金钱版 (中文字幕)");
//        data1.getInfoData().setAudioPic("https://img3.doubanio.com/img/trailer/medium/2631410731.jpg?1611566097");
//        data1.setPlayUrl("https://vt1.doubanio.com/202102020903/722442386dcd5076fd70c4ac2bf093bb/view/movie/M/402710160.mp4");
//        data1.setAudioId(1);
//        data1.setPlayUrlId("1");
//        mPlayItemArrayList.add(data1);
//
//        VideoAlbumPlayItem data2 = new VideoAlbumPlayItem();
//        data2.getInfoData().setTitle("旺达幻视 预告片");
//        data2.getInfoData().setAudioPic("https://img1.doubanio.com/img/trailer/medium/2628042057.jpg?");
//        data2.setPlayUrl("https://vt1.doubanio.com/202102011621/94e560ba4d88c562e0768f6339822d99/view/movie/M/402690624.mp4");
//        data2.setAudioId(2);
//        data2.setPlayUrlId("2");
//        mPlayItemArrayList.add(data2);
//
//        VideoAlbumPlayItem data3 = new VideoAlbumPlayItem();
//        data3.getInfoData().setTitle("无耻之徒(美版) 第十一季 预告片");
//        data3.getInfoData().setAudioPic("https://img1.doubanio.com/img/trailer/medium/2626877508.jpg?");
//        data3.setPlayUrl("https://vt1.doubanio.com/202101120940/a3e7ae32c21341710eaceba2d2e56039/view/movie/M/402680931.mp4");
//        data3.setAudioId(3);
//        data3.setPlayUrlId("3");
//        mPlayItemArrayList.add(data3);
//
//        VideoAlbumPlayItem data4 = new VideoAlbumPlayItem();
//        data4.getInfoData().setTitle("发现女巫 第二季 预告片");
//        data4.getInfoData().setAudioPic("https://img9.doubanio.com/img/trailer/medium/2628112124.jpg?");
//        data4.setPlayUrl("https://vt1.doubanio.com/202101120938/d05ce0af6cefa6b88dd699e1f8150f2f/view/movie/M/402690672.mp4");
//        data4.setAudioId(4);
//        data4.setPlayUrlId("4");
//        mPlayItemArrayList.add(data4);
//
//        VideoAlbumPlayItem data5 = new VideoAlbumPlayItem();
//        data5.getInfoData().setTitle("天国与地狱 预告片");
//        data5.getInfoData().setAudioPic("https://img2.doubanio.com/img/trailer/medium/2628313153.jpg?");
//        data5.setPlayUrl("https://vt1.doubanio.com/202102051113/07846ae6e7dd67089ff46a4d070b5f5d/view/movie/M/402690752.mp4");
//        data5.setAudioId(5);
//        data5.setPlayUrlId("5");
//        mPlayItemArrayList.add(data5);
//
//        notifyPlayListGet(iPlayListGetListener, mPlayItemArrayList.get(0), mPlayItemArrayList);
//        notifyPlayListChange(mPlayItemArrayList);

        long albumId = string2Long(tempPlayerBuilder.getId());
        long audioId = 0;
        if (tempPlayerBuilder instanceof CustomPlayerBuilder) {
            audioId = string2Long(((CustomPlayerBuilder) tempPlayerBuilder).getChildId());
        }

        long finalAudioId = audioId;
        VideoAlbumPlayItem videoAlbumPlayItem = new VideoAlbumPlayItem();
        videoAlbumPlayItem.setFromAudio(isFromAudio);
        videoAlbumPlayItem.getInfoData().setAlbumId(albumId);
        videoAlbumPlayItem.setAudioId(audioId);

        mAlbumRequest.getAlbumDetails(albumId, new HttpCallback<VideoAlbumDetails>() {
            @Override
            public void onSuccess(VideoAlbumDetails albumDetails) {
                PlayerLogUtil.log(getClass().getSimpleName(), "getAlbumInfo", "success");
                mAlbumDetails = albumDetails;
                initPlayListInfo(albumDetails);

                if (PlayerPreconditions.checkNull(albumDetails)) {
                    PlayerLogUtil.log(getClass().getSimpleName(), "getAlbumInfo", "success, but data is null");
                    notifyPlayListGetError(iPlayListGetListener, videoAlbumPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_ID_NULL, -1);
                    notifyPlayListChangeError(videoAlbumPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_ID_NULL, -1);
                    return;
                }

                videoAlbumPlayItem.setListenCount(albumDetails.getCountNum());
                videoAlbumPlayItem.getInfoData().setAlbumName(albumDetails.getName());
                videoAlbumPlayItem.getInfoData().setAlbumPic(albumDetails.getImg());
                videoAlbumPlayItem.getAlbumInfoData().setASentenceRecommend(albumDetails.getASentenceRecommend());
                mPlaylistInfo.setNoSubscribe(albumDetails.getNoSubscribe());
                mPlaylistInfo.setEnableReverse(albumDetails.getEnableReverse() == AlbumDetails.REVERSE_ENABLE);
                tempPlayerBuilder.setNoSubscribe(albumDetails.getNoSubscribe());
                if (albumDetails.getIsOnline() != PlayerConstants.ALBUM_ONLINE) {
                    PlayerLogUtil.log(getClass().getSimpleName(), "getAlbumInfo", "success, offline");
                    notifyPlayListGetError(iPlayListGetListener, videoAlbumPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_ALBUM_OFFLINE, -1);
                    notifyPlayListChangeError(videoAlbumPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_ALBUM_OFFLINE, -1);
                    return;
                }

                loadPlayListData(albumId, finalAudioId, mPlaylistInfo.getSort(), 1, new IDataListCallback<BasePageResult<List<VideoAudioDetails>>>() {
                    @Override
                    public void success(BasePageResult<List<VideoAudioDetails>> listBasePageResult) {
                        if (PlayerPreconditions.checkNull(listBasePageResult) || ListUtil.isEmpty(listBasePageResult.getDataList())) {
                            PlayerLogUtil.log(getClass().getSimpleName(), "getAlbumInfo", "get play list success, but list is null");
                            notifyPlayListGetError(iPlayListGetListener, videoAlbumPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_PAGE_NULL, -1);
                            notifyPlayListChangeError(videoAlbumPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_PAGE_NULL, -1);
                            return;
                        }
                        PlayerLogUtil.log(getClass().getSimpleName(), "getAlbumInfo", "get play list success");
//                        AlbumPlayListControl.super.initPlayList(tempPlayerBuilder, iPlayListGetListener);
                        updatePlayListInfo(LOAD_DATA, listBasePageResult);
                        ArrayList<PlayItem> playItemArrayList = PlayListUtils.videoAudioDetailToAlbumPlayItem(listBasePageResult.getDataList(), mPlaylistInfo, mAlbumDetails != null ? mAlbumDetails.getASentenceRecommend() : "", isFromAudio);
                        if (ListUtil.isEmpty(playItemArrayList)) {
                            notifyPlayListGetError(iPlayListGetListener, videoAlbumPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_PAGE_NULL, -1);
                            notifyPlayListChangeError(videoAlbumPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_PAGE_NULL, -1);
                            return;
                        }
                        release();
                        updatePlayListContent(LOAD_DATA, finalAudioId, playItemArrayList, iPlayListGetListener);
                    }

                    @Override
                    public void error(ApiException e) {
                        PlayerLogUtil.log(getClass().getSimpleName(), "getAlbumInfo", "get play list error");
                        notifyPlayListGetError(iPlayListGetListener, videoAlbumPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_PAGE_SERVER, e.getCode());
                        notifyPlayListChangeError(videoAlbumPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_PAGE_SERVER, e.getCode());
                    }
                });
            }

            @Override
            public void onError(ApiException e) {
                PlayerLogUtil.log(getClass().getSimpleName(), "getAlbumInfo", "get album info error");
                initPlayListInfo(null);
                notifyPlayListGetError(iPlayListGetListener, videoAlbumPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_ID_SERVER, e.getCode());
                notifyPlayListChangeError(videoAlbumPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_ID_SERVER, e.getCode());
            }
        });

    }

    private long string2Long(String strValue) {
        long longValue = 0;
        if (!StringUtil.isEmpty(strValue)) {
            try {
                longValue = Long.parseLong(strValue);
            } catch (Exception e) {

            }
        }
        return longValue;
    }

    private void getAudioInfo(final IPlayListGetListener iPlayListGetListener) {

        long audioId = string2Long(((CustomPlayerBuilder) tempPlayerBuilder).getChildId());
        Long[] audioIds = new Long[1];
        audioIds[0] = audioId;

        VideoAlbumPlayItem videoAlbumPlayItem = new VideoAlbumPlayItem();
        videoAlbumPlayItem.setAudioId(audioId);

        mVideoAudioRequest.getAudioDetails(audioIds, new HttpCallback<List<VideoAudioDetails>>() {
            @Override
            public void onSuccess(List<VideoAudioDetails> videoDetailsList) {
                if (videoDetailsList==null || videoDetailsList.size()<1) {
                    PlayerLogUtil.log(getClass().getSimpleName(), "getAudioInfo", "success, but data is null");
                    notifyPlayListGetError(iPlayListGetListener, videoAlbumPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_ID_NULL, -1);
                    notifyPlayListChangeError(videoAlbumPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_ID_NULL, -1);
                    return;
                }

                VideoAudioDetails videoDetails = videoDetailsList.get(0);
                //此处获取到的audioDetails中只有id，不再包含url信息
                PlayerLogUtil.log(getClass().getSimpleName(), "getAudioInfo", "success");
                mPlaylistInfo.setTempId(String.valueOf(videoDetails.getAlbumId()));
                mPlaylistInfo.setTempChildId(String.valueOf(videoDetails.getId()));
                if(videoDetails.getAlbumId()>0){
                    tempPlayerBuilder.setId(String.valueOf(videoDetails.getAlbumId()));
                    getAlbumInfo(iPlayListGetListener, true);
                }else {
                    VideoAlbumPlayItem videoAlbumPlayItem = (VideoAlbumPlayItem) PlayListUtils.translateVideoAlbumToPlayItem(videoDetails, true);
                    if (addNewPlayItemToList(mPlayItemArrayList, videoAlbumPlayItem)) {
                        mPlayItemArrayList.add(videoAlbumPlayItem);
                    }
                    PlayerLogUtil.log(getClass().getSimpleName(), "updatePlayListContent", "needPlayIndex = " + 0);
                    notifyPlayListGet(iPlayListGetListener, mPlayItemArrayList.get(0), mPlayItemArrayList);
                    notifyPlayListChange(mPlayItemArrayList);
                }


            }

            @Override
            public void onError(ApiException e) {
                PlayerLogUtil.log(getClass().getSimpleName(), "getAudioInfo", "error.code = "+e.getCode()+", error.msg = " + e.getMessage());
                VideoAlbumPlayItem playItem = new VideoAlbumPlayItem();
                playItem.setAudioId(audioId);
                notifyPlayListGetError(iPlayListGetListener, playItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_ID_SERVER, e.getCode());
                notifyPlayListChangeError(playItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_ID_SERVER, e.getCode());
            }
        });
    }

    private boolean addNewPlayItemToList(ArrayList<PlayItem> mPlayItemArrayList, PlayItem newItem) {
        if (mPlayItemArrayList.size() == 0) {
            return true;
        }
        for (PlayItem addPlayItem: mPlayItemArrayList) {
            if (((VideoAlbumPlayItem) addPlayItem).getAudioId() == newItem.getAudioId()) {
                return false;
            }
        }
        return true;
    }

    private void updatePlayListContent(int type, ArrayList<PlayItem> playItems, final IPlayListGetListener iPlayListGetListener) {
        updatePlayListContent(type, 0, playItems, iPlayListGetListener);
    }

    private void updatePlayListContent(int type, long audioId, ArrayList<PlayItem> playItems, final IPlayListGetListener iPlayListGetListener) {
        int needPlayIndex = 0;
        switch (type) {
            case LOAD_DATA:
                needPlayIndex = getNotifyPlayListIndex(playItems, audioId);
                break;
            case LOAD_DATA_NEXT:

                break;
            case LOAD_DATA_PRE:
                needPlayIndex = playItems.size() - 1;
                break;
            case LOAD_DATA_PAGE_AND_PLAY_NEXT_ITEM:
                needPlayIndex = getNotifyPlayListIndex(playItems, mCurPlayItem.getAudioId());
                if (++needPlayIndex >= playItems.size()) {
                    loadNextPage(iPlayListGetListener);
                    return;
                }
                break;
            case LOAD_DATA_PAGE_AND_PLAY_PRE_ITEM:
                needPlayIndex = getNotifyPlayListIndex(playItems, mCurPlayItem.getAudioId());
                if (--needPlayIndex < 0) {
                    loadPrePage(iPlayListGetListener);
                    return;
                }
                break;
            default:
                break;
        }
        if (type == LOAD_DATA_PRE) {
            PlayItem playItem = getCurPlayItem();
            PlayerLogUtil.log(getClass().getSimpleName(), "updatePlayListContent", "current position = " + getCurPosition());
            mPlayItemArrayList.addAll(0, playItems);
            setCurPosition(playItem);
        } else if (type == LOAD_DATA_PAGE || type == LOAD_DATA_PAGE_AND_PLAY_NEXT_ITEM || type == LOAD_DATA_PAGE_AND_PLAY_PRE_ITEM) {
            PlayItem playItem = getPlayItem(mCurPlayItem, playItems);
            PlayerLogUtil.log(getClass().getSimpleName(), "updatePlayListContent", "current position = " + getCurPosition());
            mPlayItemArrayList.clear();
            mPlayItemArrayList.addAll(playItems);
            setCurPosition(playItem);
        } else {
            List<PlayItem> diffItems = getDiffList(mPlayItemArrayList, playItems);
            mPlayItemArrayList.addAll(diffItems);
        }

        PlayerLogUtil.log(getClass().getSimpleName(), "updatePlayListContent", "needPlayIndex = " + needPlayIndex);
        notifyPlayListGet(iPlayListGetListener, playItems.get(needPlayIndex), playItems);
        notifyPlayListChange(playItems);
    }

    private PlayItem getPlayItem(PlayItem playItem, List<PlayItem> playItemList) {
        if (playItem == null) {
            return null;
        }
        PlayItem item = null;
        for (int i = 0; i < playItemList.size(); i++) {
            if (playItem.getAudioId() == playItemList.get(i).getAudioId()) {
                item = playItemList.get(i);
                break;
            }
        }

        return item;
    }

    /**
     * 去重
     * 资讯内容更新比较即时，当拉取下一页的时候可能更新的多条内容，导致拉取重复数据，播放列表显示异常
     */
    public static List<PlayItem> getDiffList(List<PlayItem> fromList,
                                             List<PlayItem> onList) {
        List<PlayItem> result = new ArrayList<>();
        for (PlayItem bean : onList) {
            boolean hasValue = false;
            for (PlayItem item : fromList) {
                if (bean.getAudioId() == item.getAudioId()) {
                    hasValue = true;
                    break;
                }
            }
            if (!hasValue) {
                result.add(bean);
            }
        }
        return result;
    }

    public static boolean isInList(List<PlayItem> fromList,
                                   PlayItem playItem) {
        boolean hasValue = false;
        if (playItem == null) {
            return false;
        }
        for (PlayItem item : fromList) {
            if (playItem.getAudioId() == item.getAudioId()) {
                hasValue = true;
                break;
            }
        }

        return hasValue;
    }

    private void initPlayListInfo(VideoAlbumDetails albumDetails) {
        if (!StringUtil.isEmpty(mPlaylistInfo.getTempId())) {
            mPlaylistInfo.setId(mPlaylistInfo.getTempId());
        }
        mPlaylistInfo.setChildId(null);
        if (albumDetails != null) {
            mPlaylistInfo.setSort(tempPlayerBuilder.getSort());
            mPlaylistInfo.setAlbumName(albumDetails.getName());
            mPlaylistInfo.setAlbumPic(albumDetails.getImg());
            mPlaylistInfo.setCountNum(albumDetails.getCountNum());
            mPlaylistInfo.setFollowedNum(albumDetails.getFollowedNum());
            mPlaylistInfo.setListenNum(albumDetails.getListenNum());
            mPlaylistInfo.setSourceName(albumDetails.getSourceName());
            mPlaylistInfo.setSourceLogo(albumDetails.getSourceLogo());
            mPlaylistInfo.setBreakPointContinue(albumDetails.getBreakPointContinue());
        }
    }

    private void updatePlayListInfo(int type, BasePageResult basePageResult) {
        switch (type) {
            case LOAD_DATA: {
                mPlaylistInfo.setNextPage(basePageResult.getNextPage());
                mPlaylistInfo.setHasNextPage(basePageResult.getHaveNext() == PlayerConstants.HAVE_PAGE_DATA);
                mPlaylistInfo.setPrePage(basePageResult.getPrePage());
                mPlaylistInfo.setHasPrePage(basePageResult.getHavePre() == PlayerConstants.HAVE_PAGE_DATA);
            }
            break;
            case LOAD_DATA_PRE: {
                mPlaylistInfo.setPrePage(basePageResult.getPrePage());
                mPlaylistInfo.setHasPrePage(basePageResult.getHavePre() == PlayerConstants.HAVE_PAGE_DATA);
            }
            break;
            case LOAD_DATA_NEXT: {
                mPlaylistInfo.setNextPage(basePageResult.getNextPage());
                mPlaylistInfo.setHasNextPage(basePageResult.getHaveNext() == PlayerConstants.HAVE_PAGE_DATA);
            }
            break;
            case LOAD_DATA_PAGE_AND_PLAY_NEXT_ITEM:
            case LOAD_DATA_PAGE_AND_PLAY_PRE_ITEM:
            case LOAD_DATA_PAGE:
                mPlaylistInfo.resetNextPage(basePageResult.getNextPage());
                mPlaylistInfo.setHasNextPage(basePageResult.getHaveNext() == PlayerConstants.HAVE_PAGE_DATA);
                mPlaylistInfo.resetPrePage(basePageResult.getPrePage());
                mPlaylistInfo.setHasPrePage(basePageResult.getHavePre() == PlayerConstants.HAVE_PAGE_DATA);
                break;
            default:
                break;
        }
        mPlaylistInfo.setPageIndex(String.valueOf(basePageResult.getNextPage()));
        mPlaylistInfo.setAllSize(basePageResult.getCount());
    }

    private void setPageSize(int pageSize) {
        mPageSize = pageSize;
    }

    @Override
    public void setCurPosition(int position) {
        super.setCurPosition(position);
    }

    @Override
    public void setCurPosition(PlayItem playItem) {
        super.setCurPosition(playItem);
    }
}
