package com.kaolafm.opensdk.player.logic.util;

/**
 * <AUTHOR> on 2019/3/18.
 */

public class PlayerConstants {

    public static final String LOG_TAG = "player_log_tag";

    public static final String LOG_PROGRESS_TAG = "player_log_progress_tag";


    public static final int PAGE_NUMBER_10 = 10;

    /**
     * 有页面信息
     */
    public static final int HAVE_PAGE_DATA = 1;


    /**
     * 播放历史时要减去5秒
     */
    public static final int PLAY_MAGIC_TIME = 5 * 1000;

    /**
     * 失败类型
     */
    public static final int RESOURCES_TYPE_ERROR = -2;

    /**
     * 无效类型
     */
    public static final int RESOURCES_TYPE_INVALID = -1;
    /**
     * 专辑
     */
    public static final int RESOURCES_TYPE_ALBUM = 0;
    /**
     * 单曲
     */
    public final static int RESOURCES_TYPE_AUDIO = 1;
    /**
     * 电台
     */
    public static final int RESOURCES_TYPE_RADIO = 3;

    /**
     * 直播
     */
    public static final int RESOURCES_TYPE_LIVING = 5;

    /**
     * 一键收听电台类型
     */
    public static final int RESOURCES_TYPE_ONE_KEY_LISTENER_TYPE = 4;
    /**
     * 一键收听-已购
     */
    public static final int RESOURCES_TYPE_PURCHASE_ONE_KEY_LISTENER_TYPE = 6;
    /**
     * 在线广播
     */
    public static final int RESOURCES_TYPE_BROADCAST = 11;

    /**
     * 听电视
     */
    public static final int RESOURCES_TYPE_TV = 12;
    /**
     * 专题
     */
    public static final int RESOURCES_TYPE_FEATURE = 13;

    /**
     * 视频专辑
     */
    public static final int RESOURCES_TYPE_VIDEO_ALBUM = 15;
    public static final int RESOURCES_TYPE_VIDEO_AUDIO = 16;

    /**
     * 资讯碎片
     */
    public static final int RESOURCES_TYPE_INFO_FRAGMENT = 17;

    public static final int RESOURCES_TYPE_TEMP_TASK = 60;

    public static final int RESOURCES_TYPE_LIVE_STREAM = 61;
    /**
     * QQ音乐
     */
    public static final int RESOURCES_TYPE_QQ_MUSIC = 100;


    public static final int RESOURCES_TYPE_MIN_SIZE = 1000;


    public static final int TYPE_PLAYER_IDLE = 1;
    public static final int TYPE_PLAYER_PREPARING = 2;
    public static final int TYPE_PLAYER_PREPARING_COMPLETE = 3;
    public static final int TYPE_PLAYER_PLAYING = 4;
    public static final int TYPE_PLAYER_PROGRESS = 5;
    public static final int TYPE_PLAYER_PAUSED = 6;
    public static final int TYPE_SEEK_START = 7;
    public static final int TYPE_SEEK_COMPLETE = 8;
    public static final int TYPE_BUFFERING_START = 9;
    public static final int TYPE_BUFFERING_END = 10;
    public static final int TYPE_PLAYER_END = 11;
    public static final int TYPE_PLAYER_DOWNLOAD_PROGRESS = 12;
    public static final int TYPE_PLAYER_VIDEO_RENDER_START = 13;
    public static final int TYPE_PLAYER_VIDEO_SIZE_CHANGED = 14;
    public static final int TYPE_PLAYER_FAILED = 404;

    /**
     * 临时任务类型-报时
     */
    public static final int TEMP_TASK_TYPE_CLOCK = 1;

    /**
     * 临时任务类型-提示音
     */
    public static final int TEMP_TASK_TYPE_HINT = 2;

    /**
     * 默认状态
     */
    public static final int BROADCAST_STATUS_DEFAULT = 0;
    /**
     * 直播中
     */
    public static final int BROADCAST_STATUS_LIVING = 1;
    /**
     * 可回放
     */
    public static final int BROADCAST_STATUS_PLAYBACK = 2;
    /**
     * 未开播
     */
    public static final int BROADCAST_STATUS_NOT_ON_AIR = 3;

    /**
     * 国家台
     */
    public static final int BROADCAST_CLASSIFY_COUNTRY = 1;
    /**
     * 地方台
     */
    public static final int BROADCAST_CLASSIFY_AREA = 2;

    /**
     * 正序
     */
    public static final int SORT_ACS = 1;

    /**
     * 倒序
     */
    public static final int SORT_DESC = -1;

    /**
     * 最大音量
     */
    public static final float LEFT_VOLUME_MAX = 1.0F;
    public static final float RIGHT_VOLUME_MAX = 1.0F;

    /**
     * 混音最小音量
     */
    public static final float LEFT_VOLUME_MIN = 0.3F;
    public static final float RIGHT_VOLUME_MIN = 0.3F;

    /**
     * 广播播单状态
     */
    public static final int BROADCAST_PROGRAM_HIDE = 0; //不展示
    public static final int BROADCAST_PROGRAM_SHOW = 1; //展示且可播。有播放器地址正常可播可展示、无播放地址提示“转码中”
    public static final int BROADCAST_PROGRAM_CANNOT_PLAY = 2;  //展示不可播

    /**
     * 音质Type
     */
    public static final int TONE_QUALITY_LOW = 1;   //低音质
    public static final int TONE_QUALITY_STANDARD = 2;  //标准音质
    public static final int TONE_QUALITY_HIGH = 3;   //高音质
    public static final int TONE_QUALITY_ULTRA_HIGH = 4;   //超高音质

    /**
     * 台宣类型
     */
    public static final int CTG_TYPE_TX = 131;

    /**
     * 来源于历史记录
     */
    public static final int DATA_SRC_HISTORY = 1;

    /**
     * 专辑已上线
     */
    public static final int ALBUM_ONLINE = 1;

    /**
     * 专辑下线错误
     */
    public static final int ERROR_CODE_PLAY_LIST_ALBUM_OFFLINE = 10001;

    /**
     * 是最后一首
     */
    public static final int ERROR_CODE_PLAY_LIST_IS_LAST_ONE = 10002;

    /**
     * 是第一首
     */
    public static final int ERROR_CODE_PLAY_LIST_IS_FIRST_ONE = 10003;

    /**
     * 播单id无效(详情数据为空)
     */
    public static final int ERROR_CODE_PLAY_LIST_INIT_ID_NULL = 10010;

    /**
     * 播单id无效(服务器错误)
     */
    public static final int ERROR_CODE_PLAY_LIST_INIT_ID_SERVER = 10011;

    /**
     * 初始化播单失败(列表数据为空)
     */
    public static final int ERROR_CODE_PLAY_LIST_INIT_PAGE_NULL = 10012;

    /**
     * 初始化播单失败(服务器错误)
     */
    public static final int ERROR_CODE_PLAY_LIST_INIT_PAGE_SERVER = 10013;

    /**
     * 初始化播单失败(url为空)
     */
    public static final int ERROR_CODE_PLAY_LIST_URL_NULL = 10014;

    /**
     * 初始化播单失败(加解密获取url失败)
     */
    public static final int ERROR_CODE_PLAY_LIST_URL_DECODE = 10015;

    /**
     * 初始化播单失败(直播url为空)
     */
    public static final int ERROR_CODE_PLAY_LIST_URL_LIVE_NULL = 10016;
    public static final int ERROR_CODE_PLAY_LIST_URL_LIVE_STATUS = 10017;

    /**
     * 获取下一页播单失败(列表数据为空)
     */
    public static final int ERROR_CODE_PLAY_LIST_NEXT_PAGE_NULL = 10020;

    /**
     * 获取下一页播单失败(服务器错误)
     */
    public static final int ERROR_CODE_PLAY_LIST_NEXT_PAGE_SERVER = 10021;

    /**
     * 获取上一页播单失败(列表数据为空)
     */
    public static final int ERROR_CODE_PLAY_LIST_PRE_PAGE_NULL = 10022;

    /**
     * 获取上一页播单失败(服务器错误)
     */
    public static final int ERROR_CODE_PLAY_LIST_PRE_PAGE_SERVER = 10023;

    /**
     * 一键置顶触发的获取播单失败(列表数据为空)
     */
    public static final int ERROR_CODE_PLAY_LIST_ONE_KEY_TO_TOP_ALBUM_PAGE_NULL = 10030;

    /**
     * 一键置顶触发的获取播单失败(服务器错误)
     */
    public static final int ERROR_CODE_PLAY_LIST_ONE_KEY_TO_TOP_ALBUM_PAGE_SERVER = 10031;

    /**
     * 一键置顶播放下一首触发的获取播单失败(列表数据为空)
     */
    public static final int ERROR_CODE_PLAY_LIST_ONE_KEY_TO_TOP_ALBUM_NEXT_ITEM_PAGE_NULL = 10032;

    /**
     * 一键置顶播放下一首触发的获取播单失败(服务器错误)
     */
    public static final int ERROR_CODE_PLAY_LIST_ONE_KEY_TO_TOP_ALBUM_NEXT_ITEM_PAGE_SERVER = 10033;

    /**
     * 一键置顶播放上一首触发的获取播单失败(列表数据为空)
     */
    public static final int ERROR_CODE_PLAY_LIST_ONE_KEY_TO_TOP_ALBUM_PRE_ITEM_PAGE_NULL = 10034;

    /**
     * 一键置顶播放上一首触发的获取播单失败(服务器错误)
     */
    public static final int ERROR_CODE_PLAY_LIST_ONE_KEY_TO_TOP_ALBUM_PRE_ITEM_PAGE_SERVER = 10035;

    /**
     * 获取下一首失败(数据为空)
     */
    public static final int ERROR_CODE_PLAY_LIST_NEXT_ITEM_NULL = 10040;

    /**
     * 获取上一首失败(数据为空)
     */
    public static final int ERROR_CODE_PLAY_LIST_PRE_ITEM_NULL = 10041;

    /**
     * 获取下一首失败(数组位置越界)
     */
    public static final int ERROR_CODE_PLAY_LIST_NEXT_INDEX_OUT_OF_BOUNDS = 10042;

    /**
     * 获取上一首失败(数组位置越界)
     */
    public static final int ERROR_CODE_PLAY_LIST_PRE_INDEX_OUT_OF_BOUNDS = 10043;

    /**
     * 获取下一首失败(时间错误)
     */
    public static final int ERROR_CODE_PLAY_LIST_NEXT_ITEM_TIME = 10044;

    /**
     * 获取上一首失败(时间错误)
     */
    public static final int ERROR_CODE_PLAY_LIST_PRE_ITEM_TIME = 10045;

    /**
     * 电台无版权（简版）
     */
    public static final int ERROR_CODE_RADIO_COPYRIGHT_LITE = 10046;

    /**
     * 因版权原因，暂时无法播放
     */
    public static final int ERROR_CODE_NO_COPYRIGHT = 50816;

}

