package com.kaolafm.opensdk;

import com.kaolafm.opensdk.utils.BaseHttpsStrategy;

import okhttp3.Interceptor;

/**
 * SDK 配置文件。
 *
 * <AUTHOR>
 * @date 2018/12/17
 */
public class Options {
    public static final Options DEFAULT = new Options();
    private Interceptor interceptor;
    /**
     * 应用的版本号
     */
    private String versionName;
    private long httpTimeout;
    private String carType;
    private BaseHttpsStrategy mHttpsStrategy;
    private String deviceId;

    public class defaultHttpsStrategy extends BaseHttpsStrategy{
        @Override
        public void updateChannelHttpsStrategy() {
        }
    }

    public Options() {
        this(new Builder());
    }


    protected Options(Builder builder) {
        this.versionName = builder.versionName;
        this.httpTimeout = builder.httpTimeout;
        this.interceptor = builder.interceptor;
        this.carType = builder.carType;
        this.mHttpsStrategy = builder.mHttpsStrategy;
        this.deviceId = builder.deviceId;
    }

    public String versionName() {
        return versionName;
    }

    public long httpTimeout() {
        return httpTimeout;
    }

    public Interceptor interceptor() {
        return interceptor;
    }

    public String carType() {
        return carType;
    }

    /**
     * 使用简单的策略模式解决https替换灵活配置逻辑,需要在渠道适配代码里面生成渠道对应的策略，告诉sdk
     * 目前已知适配逻辑
     *      1. 大众车机会把Http包装成https，由于SDK中部分Https和Http的host不一样，
     *      所以需要使用Https的host，但是请求的时候必须是http。
     *
     * @param httpsStrategyKey
     * @return
     */

    public boolean isUseHttps(String httpsStrategyKey) {
        //如果https策略为空，则生成一个默认全部使用https的
        if(mHttpsStrategy==null){
            mHttpsStrategy = new defaultHttpsStrategy();
        }
        return mHttpsStrategy.isHttps(httpsStrategyKey);
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public static class Builder {

        /**
         * 应用版本号
         */
        private String versionName;

        /**
         * 网络请求超时时间
         */
        private long httpTimeout;

        /**
         * 网络请求拦截器
         */
        private Interceptor interceptor;

        /**
         * 车型
         */
        private String carType;

        private BaseHttpsStrategy mHttpsStrategy;

        private String deviceId;

        public String getDeviceId() {
            return deviceId;
        }

        public void setDeviceId(String deviceId) {
            this.deviceId = deviceId;
        }

        /**
         * 应用版本号
         */
        public Builder versionName(String versionName) {
            this.versionName = versionName;
            return this;
        }

        /**
         * 网络请求超时时间
         */
        public Builder httpTimeout(long timeout) {
            this.httpTimeout = timeout;
            return this;
        }

        /**
         * 网络请求拦截器
         */
        public Builder interceptor(Interceptor interceptor) {
            this.interceptor = interceptor;
            return this;
        }

        /**
         * 车型
         */
        public Builder carType(String carType) {
            this.carType = carType;
            return this;
        }

        /**
         * 网络请求是否使用https
         * @param strategy
         * @return
         */
        public Builder useHttps(BaseHttpsStrategy strategy) {
            this.mHttpsStrategy = strategy;
            return this;
        }

        public Options build() {
            return new Options(this);
        }

    }
}
