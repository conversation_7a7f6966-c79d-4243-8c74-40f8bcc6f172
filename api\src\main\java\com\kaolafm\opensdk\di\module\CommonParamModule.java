package com.kaolafm.opensdk.di.module;

import android.app.Application;
import android.os.Build;
import android.text.TextUtils;

import com.kaolafm.base.utils.DateUtil;
import com.kaolafm.base.utils.DeviceUtil;
import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.Options;
import com.kaolafm.opensdk.account.profile.KaolaProfile;
import com.kaolafm.opensdk.account.token.KaolaAccessToken;
import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.KaolaApiConstant;
import com.kaolafm.opensdk.di.qualifier.AccessTokenQualifier;
import com.kaolafm.opensdk.di.qualifier.DomainQualifier;
import com.kaolafm.opensdk.di.qualifier.KaolaInitParam;
import com.kaolafm.opensdk.di.qualifier.KaolaParam;
import com.kaolafm.opensdk.di.qualifier.ParamQualifier;
import com.kaolafm.opensdk.di.qualifier.ProfileQualifier;
import com.kaolafm.opensdk.utils.BaseHttpsStrategy;

import java.util.HashMap;
import java.util.Map;

import dagger.Module;
import dagger.Provides;
import dagger.multibindings.IntoMap;
import dagger.multibindings.StringKey;

/**
 * 提供考拉、Kradio公共参数
 *
 * <AUTHOR> Yan
 * @date 2018/7/25
 */
@Module
public class CommonParamModule {
    /**
     * 提供考拉的接口公共参数
     */
    @Provides
    @KaolaParam
    public Map<String, String> provideKaolaParams(@ProfileQualifier KaolaProfile profile,
                                           @AccessTokenQualifier KaolaAccessToken accessToken) {
        HashMap<String, String> params = new HashMap<>();
        params.put(KaolaApiConstant.APP_ID, profile.getAppId());
        params.put(KaolaApiConstant.PACKAGE_NAME, profile.getPackageName());
        params.put(KaolaApiConstant.OS, KaolaApiConstant.OS_NAME);
        params.put(KaolaApiConstant.DEVICE_ID, profile.getDeviceId());

        String openId = accessToken.getOpenId();
        if (!TextUtils.isEmpty(openId)) {
            params.put(KaolaApiConstant.OPEN_ID, openId);
        }
        params.put(KaolaApiConstant.SIGN, StringUtil.createSign(params, profile.getAppKey()));
        //下面参数不需要加入MD5签名中。
        params.put(KaolaApiConstant.CHANNEL, profile.getChannel());
        params.put(KaolaApiConstant.VERSION, profile.getVersionName());

        String udid;
        String userId = accessToken.getUserId();
        if (!TextUtils.isEmpty(userId)) {
            params.put(KaolaApiConstant.OPEN_UID, userId);
            udid = userId;
        } else {
            udid = profile.getDeviceId();
        }
        params.put(KaolaApiConstant.UDID, udid);
        String token = accessToken.getAccessToken();
        if (!TextUtils.isEmpty(token)) {
            params.put(KaolaApiConstant.ACCESS_TOKEN, token);
        }

        String sdkVersionName = profile.getSdkVersionName();
        if (!TextUtils.isEmpty(sdkVersionName)) {
            params.put(KaolaApiConstant.SDK_VERSION, sdkVersionName);
        }
        String lat = profile.getLat();
        params.put(KaolaApiConstant.LAT, lat == null ? "" : lat);
        String lng = profile.getLng();
        params.put(KaolaApiConstant.LNG, lng == null ? "" : lng);

        String carType = profile.getCarType();
        if (!TextUtils.isEmpty(carType)) {
            params.put(KaolaApiConstant.CAR_TYPE, carType);
        }
        String capabilities = profile.getCapabilities();
        if (!TextUtils.isEmpty(capabilities)) {
            params.put("capabilities", capabilities);
        }
        return params;
    }

    @Provides
    @ParamQualifier
    @IntoMap
    @StringKey(ApiHostConstants.OPEN_KAOLA_DOMAIN_NAME)
    Map<String, String> provideOpenKaolaParams(@KaolaParam Map<String, String> params) {
        return params;
    }

    @Provides
    @ParamQualifier
    @IntoMap
    @StringKey(ApiHostConstants.SEARCH_DOMAIN_NAME)
    Map<String, String> provideVoiceSearchParams(@KaolaParam Map<String, String> params) {
        return params;
    }

    @Provides
    @KaolaInitParam
    Map<String, String> provideKaoloInitParams(Application context) {
        HashMap<String, String> params = new HashMap<>();

        //应用版本
//        params.put(KaolaApiConstant.VERSION, profile.getVersionName());
        //操作系统版本
        putNotNull(params, KaolaApiConstant.OSVERSION, Build.VERSION.RELEASE);
        //设备名
        putNotNull(params, KaolaApiConstant.DEVICE_NAME, Build.MANUFACTURER);
        //设备屏幕分辨率
        putNotNull(params, KaolaApiConstant.RESOLUTION, DeviceUtil.getScreenResolution(context));
        //屏幕大小
        putNotNull(params, KaolaApiConstant.SCREEN_SIZE, DeviceUtil.getScreenSize(context));
        //imei
        putNotNull(params, KaolaApiConstant.IMEI, DeviceUtil.getImei(context));
        //设备类型
        params.put(KaolaApiConstant.DEVICE_TYPE, "0");
        return params;
    }

    private void putNotNull(HashMap<String, String> map, String key, String value) {
        if (!TextUtils.isEmpty(key) && !TextUtils.isEmpty(value)) {
            map.put(key, value);
        }
    }

    @Provides
    @DomainQualifier
    @IntoMap
    @StringKey(ApiHostConstants.OPEN_KAOLA_DOMAIN_NAME)
    String provideKaolaHost() {
        return ApiHostConstants.OPEN_KAOLA_HOST;
    }

    @Provides
    @DomainQualifier
    @IntoMap
    @StringKey(ApiHostConstants.SEARCH_DOMAIN_NAME)
    String provideSearchHost(Options options) {
        return options.isUseHttps(BaseHttpsStrategy.GET_HOST) ? ApiHostConstants.SEARCH_HTTPS_HOST : ApiHostConstants.SEARCH_HTTP_HOST;
    }


    /**
     * 生成品牌信息接口的签名，此签名是需要所有参数
     */
    @Provides
    @ParamQualifier
    @IntoMap
    @StringKey(ApiHostConstants.BRAND_DOMAIN_NAME)
    Map<String, String> provideBrandParams(@ProfileQualifier KaolaProfile profile) {
        HashMap<String, String> params = new HashMap<>();
        params.put(KaolaApiConstant.APP_ID, profile.getAppId());
        params.put(KaolaApiConstant.TIMESTAMP, String.valueOf(DateUtil.getServerTime()));
        params.put(KaolaApiConstant.DEVICE_ID, profile.getDeviceId());
        params.put(KaolaApiConstant.SIGN, StringUtil.createSign(params, profile.getAppKey()));
        return params;
    }


    @Provides
    @ParamQualifier
    @IntoMap
    @StringKey(ApiHostConstants.RECOMMEND_DOMAIN_NAME)
    Map<String, String> provideRecommendParams(@KaolaParam Map<String, String> params) {
        return params;
    }

//    /**
//     * 商城域名参数
//     */
//    @Provides
//    @MallParam
//    @ParamQualifier
//    @IntoMap
//    @StringKey(ApiHostConstants.MALL_DOMAIN_NAME)
//    Map<String, String> provideMallParams(@ProfileQualifier KaolaProfile profile,
//                                           @AccessTokenQualifier KaolaAccessToken accessToken) {
//        HashMap<String, String> params = new HashMap<>();
//        params.put(KaolaApiConstant.APP_ID, profile.getAppId());
//        params.put(KaolaApiConstant.PACKAGE_NAME, profile.getPackageName());
//        params.put(KaolaApiConstant.OS, KaolaApiConstant.OS_NAME);
//        params.put(KaolaApiConstant.DEVICE_ID, profile.getDeviceId());
//
//        String openId = accessToken.getOpenId();
//        if (!TextUtils.isEmpty(openId)) {
//            params.put(KaolaApiConstant.OPEN_ID, openId);
//        }
//        params.put(KaolaApiConstant.SIGN, StringUtil.createSign(params, profile.getAppKey()));
//        //下面参数不需要加入MD5签名中。
//        params.put(KaolaApiConstant.CHANNEL, profile.getChannel());
//        params.put(KaolaApiConstant.VERSION, profile.getVersionName());
//
//        String udid;
//        String userId = accessToken.getUserId();
//        if (!TextUtils.isEmpty(userId)) {
//            params.put(KaolaApiConstant.OPEN_UID, userId);
//            udid = userId;
//        } else {
//            udid = profile.getDeviceId();
//        }
//        params.put(KaolaApiConstant.UDID, udid);
//        String token = accessToken.getAccessToken();
//        if (!TextUtils.isEmpty(token)) {
//            params.put(KaolaApiConstant.ACCESS_TOKEN, token);
//        }
//
//        String sdkVersionName = profile.getSdkVersionName();
//        if (!TextUtils.isEmpty(sdkVersionName)) {
//            params.put(KaolaApiConstant.SDK_VERSION, sdkVersionName);
//        }
//        String lat = profile.getLat();
//        params.put(KaolaApiConstant.LAT, lat == null ? "" : lat);
//        String lng = profile.getLng();
//        params.put(KaolaApiConstant.LNG, lng == null ? "" : lng);
//
//        String carType = profile.getCarType();
//        if (!TextUtils.isEmpty(carType)) {
//            params.put(KaolaApiConstant.CAR_TYPE, carType);
//        }
//        String capabilities = profile.getCapabilities();
//        if (!TextUtils.isEmpty(capabilities)) {
//            params.put("capabilities", capabilities);
//        }
//        return params;
//    }
}
