package com.kaolafm.opensdk.player.logic.playcontrol;


import com.kaolafm.opensdk.player.core.PlayerService;
import com.kaolafm.opensdk.player.core.ijk.IjkVideoView;
import com.kaolafm.opensdk.player.core.ijk.VideoView;
import com.kaolafm.opensdk.player.core.listener.OnAudioFocusChangeInter;
import com.kaolafm.opensdk.player.core.model.AAudioFocus;
import com.kaolafm.opensdk.player.core.model.AudioFadeConfig;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.model.PlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

/**
 * 播放相关的接口
 * <AUTHOR> qian
 */
public interface IPlayControl {

    /**
     * 设置播放状态回调
     *
     * @param iPlayerStateListener
     */
    void setPlayStateListener(BasePlayStateListener iPlayerStateListener);

    /**
     * 设置serviceBind
     *
     * @param mediaPlayerServiceBind
     */
    void setBind(PlayerService.PlayerServiceBinder mediaPlayerServiceBind);


    void setVideoView(VideoView videoView);
    /**
     * 播放被暂停的音频
     */
    void play();

    /**
     * 暂停正在播放的音频
     */
    void pause();

    /**
     * 停止正在播放的音频
     */
    void stop();

    /**
     * 重置播放器使其回到IDLE状态
     */
    void reset(boolean needResetLastPlaybackRateFlag);
    void rePlay();

    /**
     * 释放播放器资源
     */
    void release();

    /**
     * 快进或快退到播放器的某个时间点
     *
     * @param position
     */
    void seek(int position);

    /**
     * 销毁播放器管理实例
     */
    void destroy();

    /**
     * 临时任务
     *
     * @param url
     */
    void playTempTask(String url, VideoView videoView, String playUrlId);

    /**
     * 预播放
     * 在Start播放前回调
     */
    void preStart(PlayItem playItem);

    /**
     * 开始播放
     */
    void start(int type, PlayItem playItem, VideoView videoView, boolean isPlayNow);

    /**
     * 设置是否支持音量均衡
     *
     * @param isActive
     */
    void setLoudnessNormalization(int isActive);

    /**
     * 是否正在播放
     *
     * @return
     */
    boolean isPlaying();

    /**
     * 请求音频焦点
     */
    boolean requestAudioFocus();

    /**
     * 释放音频焦点
     */
    boolean abandonAudioFocus();

    /**
     * 设置自定义音频焦点控制
     *
     * @param audioFocus
     */
    void setCustomAudioFocus(AAudioFocus audioFocus);

    /**
     * 设置音频焦点listener
     *
     * @param iAudioFocusListener
     */
    void setAudioFocusListener(OnAudioFocusChangeInter iAudioFocusListener);

    /**
     * 获取当前播放时间点
     */
    long getCurrentPosition();

    /**
     * 设置音量
     *
     * @param leftVolume
     * @param rightVolume
     */
    void setMediaVolume(float leftVolume, float rightVolume);
    void setPlaybackRate(float rate);
    float getPlaybackRate();

    /**
     * 设置AudioTrack的 Usage和ContentType
     *
     * @param usage
     * @param contentType
     */
    public void setUsageAndContentType(int usage, int contentType);

    /**
     * 获取播放状态
     *
     * @return
     */
    int getPlayStatus();

    /**
     * 设置log无效
     */
    void setLogInValid();

    /**
     * 设置要播放的url, 但是不播放
     *
     * @param uri
     * @param position
     */
    void setPlayUrl(String uri, long position, long duration, VideoView videoView);

    /**
     * 禁用淡入淡出效果
     */
    void disableAudioFade();

    /**
     * 设置HTTP代理
     *
     * @param httpProxy HTTP代理地址
     */
    void setHttpProxy(String httpProxy);

    /**
     * 清除HTTP代理
     */
    void clearHttpProxy();

    /**
     * 清除dns缓存
     */
    void clearDnsCache(boolean clearDnsCache);

    /**
     * 设置淡入淡出效果配置
     *
     * @param audioFadeConfig
     */
    void setAudioFadeConfig(AudioFadeConfig audioFadeConfig);

    /**
     * 异步启动播放是否正在执行
     *
     * @return
     */
//    boolean isAsyncStartExecuting();

    /**
     * V3.0路演临时方案，增加一个开关控制，为true时焦点丢失音频暂停，为false时焦点丢失音频继续播放
     *
     * @param isHandleLossFocus
     */
    void isHandleLossFocus(boolean isHandleLossFocus);

    /**
     * 当PlayItem妹子类型改变时,通知之前的PlayControl
     * 目前仅用于一种场景:当从广播切换为其他类型节目后,通知广播清除保存的PlayItem数据.为什么这样做:广播直播时到达下一节目时刻会忽略重新拉流,但是如果正在听广播直播,然后切换为其他节目类型后重新选择收听相同的广播直播,不会拉流播放,因此增加该方法.
     */
    void notifyPlayItemChangToOtherType();
}
