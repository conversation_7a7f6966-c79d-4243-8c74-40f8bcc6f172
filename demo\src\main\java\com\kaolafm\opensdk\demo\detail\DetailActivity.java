package com.kaolafm.opensdk.demo.detail;

import android.os.Bundle;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.method.ScrollingMovementMethod;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.SeekBar;
import android.widget.SeekBar.OnSeekBarChangeListener;
import android.widget.Switch;
import android.widget.TextView;
import android.widget.Toast;
import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import com.bumptech.glide.Glide;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.broadcast.BroadcastDetails;
import com.kaolafm.opensdk.api.broadcast.BroadcastRequest;
import com.kaolafm.opensdk.api.broadcast.ProgramDetails;
import com.kaolafm.opensdk.api.media.AlbumRequest;
import com.kaolafm.opensdk.api.media.AudioRequest;
import com.kaolafm.opensdk.api.media.RadioRequest;
import com.kaolafm.opensdk.api.media.model.AIAudioDetails;
import com.kaolafm.opensdk.api.media.model.AlbumDetails;
import com.kaolafm.opensdk.api.media.model.AudioDetails;
import com.kaolafm.opensdk.api.media.model.RadioDetails;
import com.kaolafm.opensdk.api.music.qq.QQMusicRequest;
import com.kaolafm.opensdk.api.music.qq.model.Song;
import com.kaolafm.opensdk.api.operation.OperationRequest;
import com.kaolafm.opensdk.api.operation.model.category.Category;
import com.kaolafm.opensdk.api.subscribe.SubscribeRequest;
import com.kaolafm.opensdk.api.subscribe.SubscribeStatus;
import com.kaolafm.opensdk.demo.AntiShake;
import com.kaolafm.opensdk.demo.BaseActivity;
import com.kaolafm.opensdk.demo.PlayerStateListenerWrapper;
import com.kaolafm.opensdk.demo.R;
import com.kaolafm.opensdk.demo.detail.StringAdapter.Item;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.listener.IPlayListStateListener;
import com.kaolafm.opensdk.player.logic.model.PlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.utils.BeanUtil;
import com.lcodecore.tkrefreshlayout.RefreshListenerAdapter;
import com.lcodecore.tkrefreshlayout.TwinklingRefreshLayout;
import java.util.ArrayList;
import java.util.List;

/**
 * 专辑详情
 *
 * <AUTHOR> Yan
 * @date 2018/7/23
 */

public class DetailActivity extends BaseActivity implements IPlayListStateListener {

    public static final String KEY_ID = "id";

    public static final String KEY_TYPE = "type";

    public static final int TYPE_ALBUM = ResType.TYPE_ALBUM;

    public static final int TYPE_RADIO = ResType.TYPE_RADIO;

    public static final int TYPE_BROADCAST = ResType.TYPE_BROADCAST;

    public static final int TYPE_AUDIO = ResType.TYPE_AUDIO;

    public static final int TYPE_QQ_MUSIC = ResType.TYPE_QQ_MUSIC;

    public static final int TYPE_LIVE = ResType.TYPE_LIVE;


    public static final String STR_SUBSCRIBE = "订阅";

    public static final String STR_UNSUBSCRIBE = "取消订阅";

    @BindView(R.id.iv_detail_play_next)
    ImageView mIvDetailPlayNext;

    @BindView(R.id.iv_detail_play_pause)
    ImageView mIvDetailPlayPause;

    @BindView(R.id.iv_detail_play_pre)
    ImageView mIvDetailPlayPre;

    @BindView(R.id.sb_detail_progress)
    SeekBar mSbDetailProgress;

    @BindView(R.id.switch_audio_focus)
    Switch mSwitchAudioFocus;

    @BindView(R.id.trf_detail_playlist)
    TwinklingRefreshLayout mTrfDetailPlaylist;

    private String mClockId = "";

    private Gson mGson;

    @BindView(R.id.iv_detail_cover)
    ImageView ivCover;

    @BindView(R.id.tv_details_content)
    TextView tvDetails;

    @BindView(R.id.rv_detail_playlist)
    RecyclerView mRecyclerView;

    @BindView(R.id.btn_detail_subscribe)
    Button btnSubscribe;

    private PlayerStateListenerWrapper mIPlayerStateListener;

    private long mId;

    private int mType;

//    private OnDownloadProgressListener mOnDownloadProgressListener;

//    @Override
//    public void onPlayerListChanged(ArrayList<PlayItem> arrayList) {
//        PlayItem curPlayItem = PlayerListManager.getInstance().getCurPlayItem();
//        int curPosition = PlayerListManager.getInstance().getCurPosition();
//        Log.e("DetailActivity", "onPlayerListChanged: " + curPosition + "=" + curPlayItem);
//    }


    @OnClick(R.id.btn_detail_subscribe)
    public void onSubscribeClick() {
        if (STR_SUBSCRIBE.equals(btnSubscribe.getText())) {
            subscribe((Long) btnSubscribe.getTag());
        } else {
            unsubscribe((Long) btnSubscribe.getTag());
        }
    }

    @OnClick({R.id.iv_detail_play_pre, R.id.iv_detail_play_pause, R.id.iv_detail_play_next})
    public void onViewClicked(View view) {
        if (AntiShake.check(view.getId())) {
            return;
        }
        switch (view.getId()) {
            case R.id.iv_detail_play_pre:
                PlayerManager.getInstance().playPre();
                break;
            case R.id.iv_detail_play_pause:
                PlayerManager.getInstance().switchPlayerStatus();
                break;
            case R.id.iv_detail_play_next:
                PlayerManager.getInstance().playNext();
                break;
            default:
                break;
        }
    }

    private void unsubscribe(long id) {
        new SubscribeRequest().unsubscribe(id, new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean result) {
                setSubscribeState(!result);
            }

            @Override
            public void onError(ApiException exception) {
                setSubscribeStateError(exception);
            }
        });
    }

    private void subscribe(long id) {
        new SubscribeRequest().subscribe(id, new HttpCallback<SubscribeStatus>() {
            @Override
            public void onSuccess(SubscribeStatus result) {
                setSubscribeState(result != null && result.getStatus() != SubscribeStatus.STATE_FAILURE);
            }

            @Override
            public void onError(ApiException exception) {
                setSubscribeStateError(exception);
            }
        });
    }


    private StringAdapter mAdapter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // TODO: add setContentView(...) invocation
        ButterKnife.bind(this);

        mGson = new GsonBuilder()
                .setPrettyPrinting()
                .create();
    }

    @Override
    public int getLayoutId() {
//        return R.layout.activity_album_detail;
        return R.layout.activity_player;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        setTitle("详情及播单");
        mRecyclerView.setLayoutManager(new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false));
        mRecyclerView.addItemDecoration(new DividerItemDecoration(this, DividerItemDecoration.VERTICAL));
        mAdapter = new StringAdapter();
        mAdapter.setOnItemClickListener((view, viewType, item, position) -> {
//            if (item.type == DetailActivity.TYPE_BROADCAST) {
//                PlayItem playItem = BroadcastRadioListManager.getInstance().getPlayItemByAudioId(item.id);
//                if (playItem != null) {
//                    BroadcastRadioPlayerManager.getInstance().play(playItem);
//                }
//            }else {
//                PlayItem playItem = PlayerListManager.getInstance().getPlayItemByAudioId(item.id);
//                Log.e("DetailActivity", "initView: " + playItem);
//                if (playItem != null) {
//                    PlayerManager.getInstance(DetailActivity.this).play(playItem);
//                }
//
//            }
            if (ResType.TYPE_BROADCAST == item.type) {
                PlayerManager.getInstance().start(new PlayerBuilder().setId(String.valueOf(item.id)).setType(PlayerConstants.RESOURCES_TYPE_BROADCAST));
            }else{
                PlayerManager.getInstance().start(new PlayerBuilder().setId(String.valueOf(item.id)).setType(PlayerConstants.RESOURCES_TYPE_AUDIO));
            }

        });
        mRecyclerView.setAdapter(mAdapter);
        tvDetails.setMovementMethod(ScrollingMovementMethod.getInstance());

//        PlayerManager.getInstance().setCanUseDefaultAudioFocusLogic(mSwitchAudioFocus.isChecked());

        mTrfDetailPlaylist.setEnableRefresh(false);
        mTrfDetailPlaylist.setEnableLoadmore(true);

        initListener();
    }

    private void initListener() {
        mIPlayerStateListener = new PlayerStateListenerWrapper() {
            @Override
            public void onIdle(PlayItem playItem) {
                super.onIdle(playItem);
                mSbDetailProgress.setMax(playItem.getDuration());

            }

            @Override
            public void onPlayerPlaying(PlayItem playItem) {
                super.onPlayerPlaying(playItem);
                mIvDetailPlayPause.setActivated(false);
                int curPosition = PlayerManager.getInstance().getPlayListCurrentPosition();
//                PlayItem curPlayItem = PlayerManager.getInstance().getCurPlayItem();
                select(curPosition);
            }

            @Override
            public void onPlayerPaused(PlayItem playItem) {
                super.onPlayerPaused(playItem);
                mIvDetailPlayPause.setActivated(true);
            }

            @Override
            public void onProgress(PlayItem playItem, long progress, long total) {
                super.onProgress(playItem, progress, total);
                mSbDetailProgress.setProgress((int) progress);
            }

            @Override
            public void onDownloadProgress(PlayItem playItem, long progress, long total) {
                super.onDownloadProgress(playItem, progress, total);
                if (progress <= total) {
                    int max = mSbDetailProgress.getMax();
                    long secondaryProgress = (progress / total) * max;
                    mSbDetailProgress.setSecondaryProgress((int) secondaryProgress);
                }
            }
        };

        mSwitchAudioFocus.setOnCheckedChangeListener(
                (buttonView, isChecked) -> {
                    Log.e("DetailActivity", "initView: " + isChecked);
//                    PlayerManager.getInstance()
//                            .setCanUseDefaultAudioFocusLogic(isChecked);
                });


        mTrfDetailPlaylist.setOnRefreshListener(new RefreshListenerAdapter() {
            @Override
            public void onLoadMore(TwinklingRefreshLayout refreshLayout) {
                super.onLoadMore(refreshLayout);
                if (mType == TYPE_RADIO) {
                    getPlaylistRadio(mId, true);
                }
            }
        });

        mSbDetailProgress.setOnSeekBarChangeListener(new OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (fromUser) {
                    PlayerManager.getInstance().seek(progress);
//                    if (mType == DetailActivity.TYPE_BROADCAST) {
//                        BroadcastRadioPlayerManager.getInstance().seek(progress);
//                    }else {
//                        PlayerManager.getInstance(DetailActivity.this).seek(progress);
//                    }
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {

            }
        });
        PlayerManager.getInstance().addPlayControlStateCallback(mIPlayerStateListener);
        PlayerManager.getInstance().addPlayListControlStateCallback(this);
//        if (mType == DetailActivity.TYPE_BROADCAST) {
////            BroadcastRadioPlayerManager.getInstance().addPlayerStateListener(mIPlayerStateListener);
////            BroadcastRadioPlayerManager.getInstance().regDownloadProgressListener(mOnDownloadProgressListener);
//            PlayerManager.getInstance().addPlayControlStateCallback(mIPlayerStateListener);
//        }else {
//            PlayerManager.getInstance(this).regDownloadProgressListener(mOnDownloadProgressListener);
//            PlayerManager.getInstance(this).addPlayerStateListener(mIPlayerStateListener);
//            PlayerListManager.getInstance().registerPlayerListChangedListener(this);
//        }

    }

    private void select(int pos) {
        if (mAdapter == null) {
            return;
        }
        mAdapter.setSelected(pos);
        ((LinearLayoutManager) mRecyclerView.getLayoutManager()).scrollToPositionWithOffset(pos, 0);
        View firstChildView = mRecyclerView.getChildAt(0);
        if (firstChildView == null) {
            return;
        }
        int position = pos;
        int itemHeight = firstChildView.getHeight();
        // 第一个可见位置
        int firstItem = mRecyclerView.getChildLayoutPosition(firstChildView);
        // 最后一个可见位置
        int lastItem = mRecyclerView
                .getChildLayoutPosition(mRecyclerView.getChildAt(mRecyclerView.getChildCount() - 1));
        if (position == -1) {
            return;
        }
        if (position < firstItem) {
            // 第一种可能:跳转位置在第一个可见位置之前
            mRecyclerView.smoothScrollToPosition(position);
        } else if (position <= lastItem) {
            // 第二种可能:跳转位置在第一个可见位置之后
            int movePosition = position - firstItem;
            if (movePosition >= 0 && movePosition < mRecyclerView.getChildCount()) {
                View childView = mRecyclerView.getChildAt(movePosition);
                if (childView == null) {
                    return;
                }
                int top = childView.getTop();
                mRecyclerView.smoothScrollBy(0, top);
            }
        } else {
            mRecyclerView.smoothScrollToPosition(position);
        }
    }

    @Override
    public void initArgs() {
        mId = getIntent().getLongExtra(KEY_ID, 0L);
        mType = getIntent().getIntExtra(KEY_TYPE, 0);
    }

    @Override
    public void initData() {
        if (mId > 0L) {
            switch (mType) {
                case TYPE_ALBUM:
                    getAlbumDetails(mId);
//                    moreInOnceAlbum();
                    getPlaylistAlbum(mId);
                    getSubscribeState(mId);
                    btnSubscribe.setVisibility(View.VISIBLE);
                    break;
                case TYPE_RADIO:
                    getRadioDetails(mId);
                    getPlaylistRadio(mId, false);
                    getSubscribeState(mId);
                    btnSubscribe.setVisibility(View.VISIBLE);
                    break;
                case TYPE_BROADCAST:
                    getBroadcastDetails(mId);
                    getBroadcastProgramList(mId);
                    getSubscribeState(mId);
                    break;
                case TYPE_AUDIO:
                    getAudioDetails(mId);
                    break;
                case ResType.TYPE_MUSIC_RADIO_LABEL:
                    getQQMusicLabel(mId);
                    break;
                case ResType.TYPE_MUSIC_RADIO_SCENE:
                    getQQMusicScene(mId);
                    break;
                case ResType.MUSIC_MINE_LIKE:
                    break;
                case TYPE_LIVE:
                    getLiveDetails(mId);
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 获取直播详情
     */
    private void getLiveDetails(long id) {
        // TODO: 2018/8/17
    }

    /**
     * 获取qq音乐详情
     */
    private void getQQMusicLabel(long id) {
        new QQMusicRequest().getSongListOfCategoryLabel(id, new HttpCallback<List<Song>>() {
            @Override
            public void onSuccess(List<Song> songs) {
                if (!ListUtil.isEmpty(songs)) {
                    ArrayList<Item> itemList = new ArrayList<>();
                    for (int i = 0, size = songs.size(); i < size; i++) {
                        Song song = songs.get(i);
                        Item item = song2Item(song);
                        itemList.add(item);
                    }
                    if (mAdapter != null) {
                        mAdapter.setDataList(itemList);
                    }
                }
            }

            @Override
            public void onError(ApiException exception) {
                showToast(exception.getMessage());
            }
        });
    }

    private Item song2Item(Song song) {
        Item item = new Item();
        item.id = song.getSongId();
        item.type = TYPE_QQ_MUSIC;
        item.title = song.getSongName();
        item.details = song.getAlbumName();
        return item;
    }

    private void getQQMusicScene(long id) {
        new QQMusicRequest().getSongListOfRadio(id, new HttpCallback<List<Song>>() {
            @Override
            public void onSuccess(List<Song> songs) {
                if (!ListUtil.isEmpty(songs)) {
                    ArrayList<Item> itemList = new ArrayList<>();
                    for (int i = 0, size = songs.size(); i < size; i++) {
                        Song song = songs.get(i);
                        Item item = song2Item(song);
                        itemList.add(item);
                    }
                    if (mAdapter != null) {
                        mAdapter.setDataList(itemList);
                    }
                }
            }

            @Override
            public void onError(ApiException exception) {
                showToast(exception.getMessage());
            }
        });
    }

    /**
     * 查看订阅状态
     */
    private void getSubscribeState(long id) {
        btnSubscribe.setTag(id);
        new SubscribeRequest().isSubscribed(id, new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean result) {
                setSubscribeState(result);
            }

            @Override
            public void onError(ApiException exception) {
                setSubscribeStateError(exception);
            }
        });
    }

    /**
     * 设置未点击状态
     */
    private void setSubscribeStateError(ApiException exception) {
        btnSubscribe.setText(exception.getMessage());
    }

    /**
     * 根据结果设置订阅状态
     */
    private void setSubscribeState(boolean result) {
        String strState;

        if (!result) {
            //未订阅
            strState = STR_SUBSCRIBE;
        } else {
            //已经是订阅状态
            strState = STR_UNSUBSCRIBE;
        }

        btnSubscribe.setText(strState);
    }

    private void getAlbumDetails(long albumId) {
        new AlbumRequest().getAlbumDetails(albumId, new HttpCallback<AlbumDetails>() {
            @Override
            public void onSuccess(AlbumDetails result) {
                tvDetails.setText(mGson.toJson(result));
                String picUrl = result.getImg();
                Glide.with(DetailActivity.this).load(picUrl).into(ivCover);
            }

            @Override
            public void onError(ApiException exception) {
                tvDetails.setText(mGson.toJson(exception));
            }
        });
    }

    /**
     * 一次获取多个专辑详情
     */
    public void moreInOnceAlbum() {
        new AlbumRequest()
                .getAlbumDetails(new Long[]{1100000000078L, 1100000000416L}, new HttpCallback<List<AlbumDetails>>() {
                    @Override
                    public void onSuccess(List<AlbumDetails> result) {
                        tvDetails.setText(mGson.toJson(result));
                    }

                    @Override
                    public void onError(ApiException exception) {
                        tvDetails.setText(mGson.toJson(exception));
                    }
                });
    }


    private void getRadioDetails(long id) {
        new RadioRequest().getRadioDetails(id, new HttpCallback<RadioDetails>() {
            @Override
            public void onSuccess(RadioDetails result) {
                tvDetails.setText(mGson.toJson(result));
                String picUrl = result.getImg();
                Glide.with(DetailActivity.this).load(picUrl).into(ivCover);
            }

            @Override
            public void onError(ApiException exception) {
                tvDetails.setText(mGson.toJson(exception));
            }
        });
    }

    /**
     * 获取单曲详情
     */
    private void getAudioDetails(long audioId) {

        new AudioRequest().getAudioDetails(audioId, new HttpCallback<AudioDetails>() {
            @Override
            public void onSuccess(AudioDetails result) {
                tvDetails.setText(mGson.toJson(result));
                String picUrl = result.getAudioPic();
                Glide.with(DetailActivity.this).load(picUrl).into(ivCover);
            }

            @Override
            public void onError(ApiException exception) {
                tvDetails.setText(mGson.toJson(exception));
            }
        });
    }

    /**
     * 获取多个单曲详情
     */
    public void getAudioDetailsMutil() {
        Long[] audioIds = new Long[]{1000000394424L, 1000000394424L};
        new AudioRequest().getAudioDetails(audioIds, new HttpCallback<List<AudioDetails>>() {
            @Override
            public void onSuccess(List<AudioDetails> audioDetails) {
                tvDetails.setText(mGson.toJson(audioDetails));
            }

            @Override
            public void onError(ApiException exception) {
                tvDetails.setText(mGson.toJson(exception));
            }
        });
    }

    /**
     * 获取专辑播单
     */
    private void getPlaylistAlbum(long id) {
        new AlbumRequest().getPlaylist(id, 1, 20, 1,
                new HttpCallback<BasePageResult<List<AudioDetails>>>() {
                    @Override
                    public void onSuccess(BasePageResult<List<AudioDetails>> result) {
                        List<Item> datas = new ArrayList<>();

                        if (result != null) {
                            List<AudioDetails> dataList = result.getDataList();
                            if (dataList != null) {
                                for (int i = 0; i < dataList.size(); i++) {
                                    AudioDetails item = dataList.get(i);

                                    Item sai = new Item();
                                    sai.id = item.getAudioId();
                                    sai.type = DetailActivity.TYPE_ALBUM;
                                    sai.title = item.getAudioName();
                                    sai.details = item.getAlbumName();//mGson.toJson(item);

                                    datas.add(sai);
                                }
                            }
                        }

                        if (datas.isEmpty()) {
                            Toast.makeText(DetailActivity.this, "列表为空", Toast.LENGTH_SHORT).show();
                        }

                        mAdapter.setDataList(datas);
                    }

                    @Override
                    public void onError(ApiException exception) {
                        tvDetails.setText(mGson.toJson(exception));
                    }
                });
    }

    /**
     * 获取电台播单
     */
    private void getPlaylistRadio(long radioId, boolean isLoadMore) {
        new RadioRequest().getPlaylist(radioId, mClockId, new HttpCallback<List<AIAudioDetails>>() {
            @Override
            public void onSuccess(List<AIAudioDetails> result) {
                List<Item> datas = new ArrayList<>();
                ArrayList<PlayItem> playItemList = new ArrayList<>();
                if (result != null) {
                    AudioDetails audioDetails = result.get(0);
                    if (audioDetails != null) {
                        mClockId = audioDetails.getClockId();
                    }
                    for (int i = 0; i < result.size(); i++) {
                        AudioDetails item = result.get(i);

                        Item sai = new Item();
                        sai.id = item.getAudioId();
                        sai.type = DetailActivity.TYPE_RADIO;
                        sai.title = item.getAudioName();
                        sai.details = item.getAlbumName();//mGson.toJson(item);
                        playItemList.add(BeanUtil.translateToPlayItem(item));
                        datas.add(sai);

                    }
                }

                if (datas.isEmpty() && !isLoadMore) {
                    Toast.makeText(DetailActivity.this, "列表为空", Toast.LENGTH_SHORT).show();
                }
                if (isLoadMore) {
                    mAdapter.addDataList(datas);
                    PlayerManager.getInstance().startPlayItemInList(playItemList.get(0),null);
                    mTrfDetailPlaylist.finishLoadmore();
                } else {
                    mAdapter.setDataList(datas);
                }
            }

            @Override
            public void onError(ApiException exception) {
                tvDetails.setText(mGson.toJson(exception));
            }
        });
    }


    /**
     * 获取在线广播节目列表
     */
    private void getBroadcastProgramList(long id) {
        String data = null;
        new BroadcastRequest().getBroadcastProgramList(id, data, new HttpCallback<List<ProgramDetails>>() {
            @Override
            public void onSuccess(List<ProgramDetails> result) {
                List<Item> datas = new ArrayList<>();

                if (result != null) {
                    List<ProgramDetails> dataList = result;
                    for (int i = 0; i < dataList.size(); i++) {
                        ProgramDetails item = dataList.get(i);

                        Item sai = new Item();
                        sai.id = item.getProgramId();
                        sai.type = DetailActivity.TYPE_BROADCAST;
                        sai.title = item.getTitle();
                        sai.details = item.getBroadcastName();

                        datas.add(sai);
                    }
                }

                if (datas.isEmpty()) {
                    Toast.makeText(DetailActivity.this, "列表为空", Toast.LENGTH_SHORT).show();
                }

                mAdapter.setDataList(datas);
            }

            @Override
            public void onError(ApiException exception) {
                tvDetails.setText(mGson.toJson(exception));
            }
        });

    }

    /************************************************在线广播******************************************/

    /**
     * 获取在线广播详情
     */
    private void getBroadcastDetails(long id) {
        new BroadcastRequest().getBroadcastDetails(id, new HttpCallback<BroadcastDetails>() {
            @Override
            public void onSuccess(BroadcastDetails result) {
                tvDetails.setText(mGson.toJson(result));
                String picUrl = result.getImg();//UrlUtil.getCustomPicUrl(result.getImg(), UrlUtil.PIC_250_250);
                Glide.with(DetailActivity.this).load(picUrl).into(ivCover);
            }

            @Override
            public void onError(ApiException exception) {
                tvDetails.setText(mGson.toJson(exception));
            }
        });
    }


    /**
     * 获取在线广播节目详情
     */
    public void getBroadcastProgramDetails() {
        long programid = 19196008;
        new BroadcastRequest().getBroadcastProgramDetails(programid, new HttpCallback<ProgramDetails>() {

            @Override
            public void onSuccess(ProgramDetails programDetails) {
                tvDetails.setText(mGson.toJson(programDetails));
            }

            @Override
            public void onError(ApiException exception) {
                tvDetails.setText(mGson.toJson(exception));
            }
        });

    }

    /**
     * 获取在线广播当前节目详情
     */
    public void getBroadcastCurrentProgramDetails() {
        long id = 1600000000198L;
        new BroadcastRequest().getBroadcastCurrentProgramDetails(id, new HttpCallback<ProgramDetails>() {
            @Override
            public void onSuccess(ProgramDetails programDetails) {
                tvDetails.setText(mGson.toJson(programDetails));
            }

            @Override
            public void onError(ApiException exception) {
                tvDetails.setText(mGson.toJson(exception));
            }
        });

    }

    /*****************************************************************************************************
     * 获取在线广播分类
     */
    public void getBroadcastCategory() {
        new OperationRequest().getCategoryList("0", true, 2, new HttpCallback<List<Category>>() {
            @Override
            public void onSuccess(List<Category> categories) {
                tvDetails.setText(mGson.toJson(categories));
            }

            @Override
            public void onError(ApiException exception) {
                tvDetails.setText(mGson.toJson(exception));
            }
        });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        PlayerManager.getInstance().removePlayControlStateCallback(mIPlayerStateListener);
        PlayerManager.getInstance().removePlayListControlStateCallback(this);
    }

    @Override
    public void onPlayListChange(List<PlayItem> playItemList) {
        PlayItem curPlayItem = PlayerManager.getInstance().getCurPlayItem();
        int curPosition = PlayerManager.getInstance().getPlayListCurrentPosition();
        Log.e("DetailActivity", "onPlayerListChanged: " + curPosition + "=" + curPlayItem);
    }

    @Override
    public void onPlayListChangeError(PlayItem playItem, int i, int i1) {

    }

}
