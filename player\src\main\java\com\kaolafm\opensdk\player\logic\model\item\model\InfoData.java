package com.kaolafm.opensdk.player.logic.model.item.model;

/**
 * 播放信息-数据类
 */
public class InfoData {

    /**
     * 单曲名称
     */
    private String title;

    /**
     * 专辑id
     */
    private long albumId;

    /**
     * 单曲图片
     */
    private String audioPic;

    /**
     * 当前播单对象来源
     */
    private int dataSrc;

    /**
     * 来源url
     *
     */
    private String icon;

    /**
     * 单曲描述
     */
    private String audioDes;

    /**
     * 专辑图片
     */
    private String albumPic;

    /**
     * 专辑名称
     */
    private String albumName;

    /**
     * 期数
     */
    private int orderNum;

    /**
     * 主播
     */
    private String hosts;

    /**
     * 是否订阅
     */
    private int isLiked;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 创建时间
     */
    private long createTime;

    /**
     * 第三方来源logo
     */
    private String sourceLogo;

    /**
     * 第三方来源名称
     */
    private String sourceName;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public long getAlbumId() {
        return albumId;
    }

    public void setAlbumId(long albumId) {
        this.albumId = albumId;
    }

    public String getAudioPic() {
        return audioPic;
    }

    public void setAudioPic(String audioPic) {
        this.audioPic = audioPic;
    }

    public int getDataSrc() {
        return dataSrc;
    }

    public void setDataSrc(int dataSrc) {
        this.dataSrc = dataSrc;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getAudioDes() {
        return audioDes;
    }

    public void setAudioDes(String audioDes) {
        this.audioDes = audioDes;
    }

    public String getAlbumPic() {
        return albumPic;
    }

    public void setAlbumPic(String albumPic) {
        this.albumPic = albumPic;
    }

    public String getAlbumName() {
        return albumName;
    }

    public void setAlbumName(String albumName) {
        this.albumName = albumName;
    }

    public int getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(int orderNum) {
        this.orderNum = orderNum;
    }

    public String getHosts() {
        return hosts;
    }

    public void setHosts(String hosts) {
        this.hosts = hosts;
    }

    public int getIsLiked() {
        return isLiked;
    }

    public void setIsLiked(int isLiked) {
        this.isLiked = isLiked;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public String getSourceLogo() {
        return sourceLogo;
    }

    public void setSourceLogo(String sourceLogo) {
        this.sourceLogo = sourceLogo;
    }

    public String getSourceName() {
        return sourceName;
    }

    public void setSourceName(String sourceName) {
        this.sourceName = sourceName;
    }
}
