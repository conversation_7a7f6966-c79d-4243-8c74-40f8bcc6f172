package com.kaolafm.opensdk.player.logic.playlist;

import com.google.gson.Gson;
import com.kaolafm.base.utils.DateUtil;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.api.broadcast.BroadcastDetails;
import com.kaolafm.opensdk.api.broadcast.BroadcastRequest;
import com.kaolafm.opensdk.api.broadcast.ProgramDetails;
import com.kaolafm.opensdk.api.media.model.AudioFileInfo;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.listener.ICheckCopyrightListener;
import com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener;
import com.kaolafm.opensdk.player.logic.model.BroadcastPlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.PlayItemConstants;
import com.kaolafm.opensdk.player.logic.model.PlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.TimeDiscontinuousBroadcastPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.BroadcastPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.InvalidPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.playlist.innerinterface.IDataListCallback;
import com.kaolafm.opensdk.player.logic.playlist.innerinterface.ISonPlayList;
import com.kaolafm.opensdk.player.logic.playlist.util.PlayListUtils;
import com.kaolafm.opensdk.player.logic.util.PlayItemUtil;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;
import com.kaolafm.opensdk.player.logic.util.PlayerPreconditions;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.ListIterator;

/**
 * <AUTHOR> on 2019/3/19.
 * do 广播 此类没有用 startTimer() 方式
 */

public class BroadcastPlayListControl extends BasePlayListControl implements ISonPlayList {
    private int LOAD_PRE_PAGE = 1;
    private int LOAD_NEXT_PAGE = 2;
    private ArrayList<PlayItem> mSongPlayItemArrayList;
    private boolean isPlaySongList = false;
    private int mSongListPosition = -1;

    private BroadcastRequest mBroadcastRequest;

    private BroadcastDetails mBroadcastDetails; //用以保存当前收听电台的详情
    private BroadcastDetails mTempBroadcastDetails; //用以保存检查版权时获得的数据，可能不是当前正在收听的电台
    private BroadcastPlayItem mTempPlayItem;    //检查版权到初始化播单的过程中用来保存临时数据的。一旦获取播单成功，将不再有用

    public BroadcastPlayListControl() {
        mSongPlayItemArrayList = new ArrayList<>();
        mBroadcastRequest = new BroadcastRequest();
    }

    @Override
    public void initPlayList(PlayerBuilder playerBuilder, IPlayListGetListener iPlayListGetListener) {
        super.initPlayList(playerBuilder, iPlayListGetListener);
        initBroadcastInfo(iPlayListGetListener);
    }


    @Override
    public void loadPrePage(IPlayListGetListener iPlayListGetListener) {
    }


    @Override
    public void loadNextPage(IPlayListGetListener iPlayListGetListener) {
    }

    /**
     * 校验版权（通常用于在播放前校验是否已经授权，未授权时不可播）
     */
    public void checkCopyright(PlayerBuilder playerBuilder, ICheckCopyrightListener iCheckCopyrightListener) {
        long albumId = Long.parseLong(playerBuilder.getId());
        boolean isIgnoreLocalRadio = (playerBuilder instanceof BroadcastPlayerBuilder) && ((BroadcastPlayerBuilder) playerBuilder).isIgnoreLocalRadio();
        PlayerLogUtil.log(getClass().getSimpleName(), "checkCopyright", "broadcast id =" + albumId);

        BroadcastPlayItem playItem = new BroadcastPlayItem();
        playItem.getInfoData().setAlbumId(albumId);

        getBroadcastDetails(albumId, new HttpCallback<BroadcastDetails>() {
            @Override
            public void onSuccess(BroadcastDetails broadcastDetails) {
                PlayerLogUtil.log(getClass().getSimpleName(), "checkCopyright", "success");
                if (PlayerPreconditions.checkNull(broadcastDetails) || broadcastDetails.getBroadcastId() != albumId) {
                    PlayerLogUtil.log(getClass().getSimpleName(), "checkCopyright", "success, broadcastDetails is empty");
                    if (iCheckCopyrightListener != null)
                        iCheckCopyrightListener.onError(playItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_ID_NULL, -1);
                    return;
                }
                mTempBroadcastDetails = broadcastDetails;
                boolean needResetParam = !isIgnoreLocalRadio && broadcastDetails.getCurrentAreaInfo() != null && broadcastDetails.getCurrentAreaInfo().getBroadcastId() != albumId;
                if (needResetParam) {
                    broadcastDetails.getCurrentAreaBroadcastDetails().setBroadcastMultiAreaList(broadcastDetails.getBroadcastMultiAreaList());
                    broadcastDetails.getCurrentAreaBroadcastDetails().setCurrentAreaInfo(broadcastDetails.getCurrentAreaInfo());
                    broadcastDetails = broadcastDetails.getCurrentAreaBroadcastDetails();
                    if (PlayerPreconditions.checkNull(broadcastDetails)) {
                        PlayerLogUtil.log(getClass().getSimpleName(), "checkCopyright", "success, broadcastDetails is empty");
                        if (iCheckCopyrightListener != null)
                            iCheckCopyrightListener.onError(playItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_ID_NULL, -1);
                        return;
                    }
                }
                boolean isLite = broadcastDetails.getIsLite() == BroadcastDetails.COPYRIGHT_LITE;
                PlayerLogUtil.log(getClass().getSimpleName(), "checkCopyright", "success, broadcast is lite?:" + isLite);
                if (isLite) {
                    if (iCheckCopyrightListener != null)
                        iCheckCopyrightListener.onError(playItem, PlayerConstants.ERROR_CODE_RADIO_COPYRIGHT_LITE, -1);
                    return;
                }

                if (iCheckCopyrightListener != null) iCheckCopyrightListener.onGranted();
            }

            @Override
            public void onError(ApiException e) {
                PlayerLogUtil.log(getClass().getSimpleName(), "checkCopyright", "error");
                if (iCheckCopyrightListener != null)
                    iCheckCopyrightListener.onError(playItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_ID_NULL, -1);
            }
        });
    }

    private void getBroadcastDetails(long albumId, HttpCallback<BroadcastDetails> callback) {
        mBroadcastRequest.getBroadcastDetails(albumId, callback);
    }


    private void initBroadcastInfo(IPlayListGetListener iPlayListGetListener) {
        long albumId = string2Long(mPlayerBuilder.getId()); //拿到本次initBroadcastInfo想要播放的广播id
        PlayerLogUtil.log(getClass().getSimpleName(), "initBroadcastInfo", "broadcast id =" + albumId);

        if (mTempBroadcastDetails == null || mTempBroadcastDetails.getBroadcastId() != albumId) {
            //没有获取详情
            checkCopyright(mPlayerBuilder, new ICheckCopyrightListener() {
                @Override
                public void onGranted() {
                    initBroadcastInfo(iPlayListGetListener);
                }

                @Override
                public void onError(PlayItem playItem, int errorCode, int errorExtra) {
                    PlayerLogUtil.log(getClass().getSimpleName(), "initBroadcastInfo", "error");
                    notifyPlayListGetError(iPlayListGetListener, playItem, errorCode, errorExtra);
                    notifyPlayListChangeError(playItem, errorCode, errorExtra);
                }
            });
            return;
        }

        //走到这里就说明要播放的电台已经获得版权
        //已经对要播放的广播进行了checkCopyright操作
        mTempPlayItem = new BroadcastPlayItem();
        mTempPlayItem.getInfoData().setAlbumId(albumId);

        boolean isIgnoreLocalRadio = (mPlayerBuilder instanceof BroadcastPlayerBuilder) && ((BroadcastPlayerBuilder) mPlayerBuilder).isIgnoreLocalRadio();
        boolean needResetParam = !isIgnoreLocalRadio && mTempBroadcastDetails.getCurrentAreaInfo() != null && mTempBroadcastDetails.getCurrentAreaInfo().getBroadcastId() != albumId;

        if (needResetParam) {
            //需要重置mPlayerBuilder和mPlaylistInfo的数据
            //广播详情接口内层的详情字段CurrentAreaInfo为空，因此要在重新给mTempBroadcastDetails赋值前设置areaCode和areaName
            if (mTempBroadcastDetails.getCurrentAreaInfo() != null) {
                mTempPlayItem.getInfoData().setAreaCode(mTempBroadcastDetails.getCurrentAreaInfo().getAreaCode());
                mTempPlayItem.getInfoData().setAreaName(mTempBroadcastDetails.getCurrentAreaInfo().getAreaName());
            }
            mTempBroadcastDetails = mTempBroadcastDetails.getCurrentAreaBroadcastDetails();
            mPlayerBuilder.setId(String.valueOf(mTempBroadcastDetails.getBroadcastId()));
            mPlaylistInfo.setId(mPlayerBuilder.getId());
            mTempPlayItem.getInfoData().setAlbumId(mTempBroadcastDetails.getBroadcastId());
        } else {
            mTempPlayItem.getInfoData().setAreaCode(mTempBroadcastDetails.getProvinceId());
            mTempPlayItem.getInfoData().setAreaName(mTempBroadcastDetails.getProvinceName());
        }
        mTempPlayItem.setListenCount(mTempBroadcastDetails.getOnLineNum());
        mTempPlayItem.getInfoData().setAlbumName(mTempBroadcastDetails.getName());
        mTempPlayItem.getInfoData().setAlbumPic(mTempBroadcastDetails.getImg());
        mTempPlayItem.setFrequencyChannel(mTempBroadcastDetails.getFreq());
        mTempPlayItem.setBroadcastSort(mTempBroadcastDetails.getType());
        mTempPlayItem.setClassifyId(mTempBroadcastDetails.getClassifyId());

        mBroadcastDetails = mTempBroadcastDetails;
        mTempBroadcastDetails = null;

        albumId = string2Long(mPlayerBuilder.getId());
        mPlaylistInfo.setBroadcastClassifyId(mBroadcastDetails.getClassifyId());
        mPlaylistInfo.setBroadcastChannel(mBroadcastDetails.getFreq());
        mPlaylistInfo.setBroadcastSort(mBroadcastDetails.getType());
        mPlaylistInfo.setListenNum(mBroadcastDetails.getOnLineNum());
        mPlaylistInfo.setProgramEnable(mBroadcastDetails.getProgramEnable());
        mPlaylistInfo.setBroadcastMultiAreaList(mBroadcastDetails.getBroadcastMultiAreaList());
        if (!isIgnoreLocalRadio && mBroadcastDetails.getCurrentAreaInfo() != null) {
            mPlaylistInfo.setAreaCode(mBroadcastDetails.getCurrentAreaInfo().getAreaCode());
            mPlaylistInfo.setAreaName(mBroadcastDetails.getCurrentAreaInfo().getAreaName());
        } else {
            mPlaylistInfo.setAreaCode(mBroadcastDetails.getProvinceId());
            mPlaylistInfo.setAreaName(mBroadcastDetails.getProvinceName());
        }

        loadPlayList(albumId, null, new IDataListCallback<List<ProgramDetails>>() {
            @Override
            public void success(List<ProgramDetails> programDetails) {
                PlayerLogUtil.log(getClass().getSimpleName(), "initBroadcastInfo", "get play list success");
                ArrayList<PlayItem> playItemArrayList = PlayListUtils.programDetailsToPlayItem(programDetails, mPlaylistInfo.getBroadcastChannel(), mPlaylistInfo.getBroadcastSort(), mPlaylistInfo.getBroadcastClassifyId(), mPlaylistInfo.getListenNum(), mPlaylistInfo.getProgramEnable(), mPlaylistInfo.getAreaCode(), mPlaylistInfo.getAreaName());
                if (ListUtil.isEmpty(playItemArrayList)) {
                    PlayerLogUtil.log(getClass().getSimpleName(), "initBroadcastInfo", "get play list success, list is empty");
                    //按照谷老师要求，对播单进行判断，如果没有数据，则需要创造一个数据
                    if (ListUtil.isEmpty(playItemArrayList)) {
                        playItemArrayList = new ArrayList<>();
                        playItemArrayList.add(PlayItemUtil.createTimeDiscontinuousBroadcastPlayItem(mBroadcastDetails));
                    }
//                  notifyPlayListGetError(iPlayListGetListener, broadcastPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_PAGE_NULL, -1);
//                  notifyPlayListChangeError(broadcastPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_PAGE_NULL, -1);
//                  return;
                }
                release();
                complementedPlayList(playItemArrayList, mBroadcastDetails);
                updatePlayListContent(playItemArrayList, iPlayListGetListener);
                updatePlayListInfo((BroadcastPlayItem) playItemArrayList.get(mPosition));
            }

            @Override
            public void error(ApiException e) {
                PlayerLogUtil.log(getClass().getSimpleName(), "initBroadcastInfo", "get play list error");

                notifyPlayListGetError(iPlayListGetListener, mTempPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_ID_NULL, e.getCode());
                notifyPlayListChangeError(mTempPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_ID_NULL, e.getCode());
            }
        });
    }

    /**
     * 加载信息
     *
     * @param albumId
     * @param data
     * @param iDataListCallback
     */
    private void loadPlayList(long albumId, String data, IDataListCallback<List<ProgramDetails>> iDataListCallback) {
        mBroadcastRequest.getBroadcastProgramList(albumId, data, new HttpCallback<List<ProgramDetails>>() {
            @Override
            public void onSuccess(List<ProgramDetails> programDetails) {
//                if (ListUtil.isEmpty(programDetails)) {
//                    if (iDataListCallback != null) {
//                        iDataListCallback.error();
//                    }
//                    return;
//                }
                if (iDataListCallback != null) {
                    iDataListCallback.success(programDetails);
                }
            }

            @Override
            public void onError(ApiException e) {
                if (iDataListCallback != null) {
                    iDataListCallback.error(e);
                }
            }
        });
    }

    @Override
    public void getPrePlayItem(IPlayListGetListener iPlayListGetListener) {
        if (isPlaySongList) {
            if (mSongListPosition > 0) {
                PlayerLogUtil.log(getClass().getSimpleName(), "getPrePlayItem", "is son play list");
                notifyPlayListGet(iPlayListGetListener, mSongPlayItemArrayList.get(--mSongListPosition), null);
            }
        } else {
            if (PlayerPreconditions.checkNull(mPlayItemArrayList)) {
                notifyPlayListGetError(iPlayListGetListener, new InvalidPlayItem(), PlayerConstants.ERROR_CODE_PLAY_LIST_ONE_KEY_TO_TOP_ALBUM_PRE_ITEM_PAGE_NULL, -1);
                return;
            }
            if (mPosition - 1 >= mPlayItemArrayList.size()) {
                notifyPlayListGetError(iPlayListGetListener, new InvalidPlayItem(), PlayerConstants.ERROR_CODE_PLAY_LIST_PRE_INDEX_OUT_OF_BOUNDS, -1);
                return;
            }
            if (mPosition - 1 < 0) {
                PlayerLogUtil.log(getClass().getSimpleName(), "getPrePlayItem", "current page is start");
                if (hasPrePage()) {
                    PlayerLogUtil.log(getClass().getSimpleName(), "getPrePlayItem", "has pre page");
                    loadPrePage(iPlayListGetListener);
                } else {
                    notifyPlayListGetError(iPlayListGetListener, new InvalidPlayItem(), PlayerConstants.ERROR_CODE_PLAY_LIST_IS_FIRST_ONE, -1);
                }
                return;
            }
            PlayerLogUtil.log(getClass().getSimpleName(), "getPrePlayItem", "position = " + mPosition);
            BroadcastPlayItem playItem = (BroadcastPlayItem) (mPlayItemArrayList.get(mPosition - 1));
            if (PlayerPreconditions.checkNull(playItem)) {
                notifyPlayListGetError(iPlayListGetListener, new InvalidPlayItem(), PlayerConstants.ERROR_CODE_PLAY_LIST_PRE_ITEM_NULL, -1);
                return;
            }
            if (playItem.getStatus() == PlayerConstants.BROADCAST_STATUS_PLAYBACK) {
                PlayerLogUtil.log(getClass().getSimpleName(), "getPrePlayItem", "is play back");
                notifyPlayListGet(iPlayListGetListener, playItem, null);
            } else {
                if (playItem.getTimeInfoData().getStartTime() > DateUtil.getServerTime()) {
//                    InvalidPlayItem invalidPlayItem = PlayListUtils.translateBroadcastToInvalidPlayItem(playItem);
                    notifyPlayListGetError(iPlayListGetListener, playItem, PlayerConstants.ERROR_CODE_PLAY_LIST_PRE_ITEM_TIME, -1);
                    return;
                }
                setAutoPlay(mPlayItemArrayList.get(mPosition), playItem);
                initLiving(LOAD_NEXT_PAGE, playItem, iPlayListGetListener);
            }
        }
    }

    @Override
    public void getNextPlayItem(IPlayListGetListener iPlayListGetListener) {
        if (isPlaySongList) {
            if (mSongListPosition < mSongPlayItemArrayList.size() - 1) {
                PlayerLogUtil.log(getClass().getSimpleName(), "getNextPlayItem", "is son play list");
                notifyPlayListGet(iPlayListGetListener, mSongPlayItemArrayList.get(++mSongListPosition), null);
            }
        } else {
            if (PlayerPreconditions.checkNull(mPlayItemArrayList)) {
                notifyPlayListGetError(iPlayListGetListener, new InvalidPlayItem(), PlayerConstants.ERROR_CODE_PLAY_LIST_ONE_KEY_TO_TOP_ALBUM_NEXT_ITEM_PAGE_NULL, -1);
                return;
            }
            if (mPosition + 1 < 0) {
                notifyPlayListGetError(iPlayListGetListener, new InvalidPlayItem(), PlayerConstants.ERROR_CODE_PLAY_LIST_NEXT_INDEX_OUT_OF_BOUNDS, -1);
                return;
            }
            if (mPosition + 1 >= mPlayItemArrayList.size()) {
                PlayerLogUtil.log(getClass().getSimpleName(), "getNextPlayItem", "current page is end");
                if (hasNextPage()) {
                    PlayerLogUtil.log(getClass().getSimpleName(), "getNextPlayItem", "has next page");
                    loadNextPage(iPlayListGetListener);
                } else {
                    notifyPlayListGetError(iPlayListGetListener, new InvalidPlayItem(), PlayerConstants.ERROR_CODE_PLAY_LIST_IS_LAST_ONE, -1);
                }
                return;
            }
            PlayerLogUtil.log(getClass().getSimpleName(), "getNextPlayItem", "position = " + mPosition);
            Gson gson = new Gson();
            if (mPosition >= 0)
                PlayerLogUtil.log(getClass().getSimpleName(), "getNextPlayItem", "当前播放playItem = " + gson.toJson(mPlayItemArrayList.get(mPosition)));
            BroadcastPlayItem playItem = (BroadcastPlayItem) (mPlayItemArrayList.get(mPosition + 1));
            PlayerLogUtil.log(getClass().getSimpleName(), "getNextPlayItem", "下一个播放playItem = " + gson.toJson(playItem));
            if (PlayerPreconditions.checkNull(playItem)) {
                PlayerLogUtil.log(getClass().getSimpleName(), "getNextPlayItem", "playItem is null");
                notifyPlayListGetError(iPlayListGetListener, new InvalidPlayItem(), PlayerConstants.ERROR_CODE_PLAY_LIST_NEXT_ITEM_NULL, -1);
                return;
            }
            if (playItem.getStatus() == PlayerConstants.BROADCAST_STATUS_PLAYBACK) {
                PlayerLogUtil.log(getClass().getSimpleName(), "getNextPlayItem", "is play back");
                notifyPlayListGet(iPlayListGetListener, playItem, null);
            } else {
                long serverTime = DateUtil.getServerTime();
                //因广播直播自动切换过程中可能会出现startTime大于serverTime，导致无法正常切换到下一节目的问题，
                // 现将原先 playItem.getTimeInfoData().getStartTime() > serverTime 的判断
                // 修改为按照分钟进行对比，如果相差不足1分钟，则认为可以播放下一节目
                if (playItem.getTimeInfoData().getStartTime() - serverTime > 60 * 1000) {
                    PlayerLogUtil.log(getClass().getSimpleName(), "getNextPlayItem", "start time error ,playItem startTime=" + playItem.getTimeInfoData().getStartTime() + " ,serverTime=" + serverTime);
//                    InvalidPlayItem invalidPlayItem = PlayListUtils.translateBroadcastToInvalidPlayItem(playItem);
                    notifyPlayListGetError(iPlayListGetListener, playItem, PlayerConstants.ERROR_CODE_PLAY_LIST_NEXT_ITEM_TIME, -1);
                    return;
                }

                PlayItem prePlayItem = null;
                if (mPosition >= 0) {
                    prePlayItem = mPlayItemArrayList.get(mPosition);
                }
                setAutoPlay(prePlayItem, playItem);
                initLiving(LOAD_NEXT_PAGE, playItem, iPlayListGetListener);
            }
        }
    }

    private void setAutoPlay(PlayItem prePlayItem, PlayItem playItem) {
        if (prePlayItem == null || playItem == null) {
            return;
        }

        PlayItem resItem = prePlayItem;
        //如果自动播放，设置一个标记：播放器自动切换。
        String autoPlayToNext = prePlayItem.getMapCacheData(PlayItemConstants.ITEM_KEY_BROADCAST_AUTO_PLAY_NEXT_ITEM);
        //需要额外判断mCurPlayItem的数据，因为如果上层自组播单，
        // 可能造成PlayControl中正播放的PlayItem与当前播单中的PlayItem不是同一个对象的情况发生
        //一旦发生这种情况，自动切换到下一时段节目时设置的MapCacheData数据将会无法在prePlayItem中获取到
        if (StringUtil.isEmpty(autoPlayToNext) && mCurPlayItem != null) {
            autoPlayToNext = mCurPlayItem.getMapCacheData(PlayItemConstants.ITEM_KEY_BROADCAST_AUTO_PLAY_NEXT_ITEM);
            resItem = mCurPlayItem;
        }
        if (!StringUtil.isEmpty(autoPlayToNext)) {
            resItem.removeMapCacheData(PlayItemConstants.ITEM_KEY_BROADCAST_AUTO_PLAY_NEXT_ITEM);
            playItem.addMapCacheData(PlayItemConstants.ITEM_KEY_BROADCAST_AUTO_PLAY_NEXT_ITEM, autoPlayToNext);
        }

        resItem = prePlayItem;
        String autoPlay = prePlayItem.getMapCacheData(PlayItemConstants.ITEM_KEY_BROADCAST_NO_PLAY);
        if (StringUtil.isEmpty(autoPlay) && mCurPlayItem != null) {
            autoPlay = mCurPlayItem.getMapCacheData(PlayItemConstants.ITEM_KEY_BROADCAST_NO_PLAY);
            resItem = mCurPlayItem;
        }
        if (!StringUtil.isEmpty(autoPlay)) {
            resItem.removeMapCacheData(PlayItemConstants.ITEM_KEY_BROADCAST_NO_PLAY);
            resItem.setPosition(0);
            playItem.addMapCacheData(PlayItemConstants.ITEM_KEY_BROADCAST_NO_PLAY, autoPlay);
        }
    }

    /**
     * 刷新直播节目的播放地址，仅对从播单接口中获取到的节目有效，因为播单补齐节目的audioId为0
     *
     * @param type
     * @param playItem
     * @param iPlayListGetListener
     */
    private void initLiving(int type, PlayItem playItem, IPlayListGetListener iPlayListGetListener) {
        PlayerLogUtil.log(getClass().getSimpleName(), "initLiving", "get living info id: " + playItem.getAudioId());
        int errorCode = -1;
        if (type == LOAD_PRE_PAGE) {
            errorCode = PlayerConstants.ERROR_CODE_PLAY_LIST_PRE_PAGE_SERVER;
        } else if (type == LOAD_NEXT_PAGE) {
            errorCode = PlayerConstants.ERROR_CODE_PLAY_LIST_NEXT_PAGE_SERVER;
        }
        int finalErrorCode = errorCode;

        if (playItem instanceof TimeDiscontinuousBroadcastPlayItem) {
            //sdk添加的补全PlayItem，播放地址取自广播详情，因此调用广播详情接口刷新播放地址
            boolean isIgnoreLocalRadio = (mPlayerBuilder instanceof BroadcastPlayerBuilder) && ((BroadcastPlayerBuilder) mPlayerBuilder).isIgnoreLocalRadio();
            long albumId = ((TimeDiscontinuousBroadcastPlayItem) playItem).getInfoData().getAlbumId();
            getBroadcastDetails(albumId, new HttpCallback<BroadcastDetails>() {
                @Override
                public void onSuccess(BroadcastDetails broadcastDetails) {
                    PlayerLogUtil.log(getClass().getSimpleName(), "initLiving", "success");
                    if (PlayerPreconditions.checkNull(broadcastDetails) || broadcastDetails.getBroadcastId() != albumId) {
                        PlayerLogUtil.log(getClass().getSimpleName(), "initLiving", "success, broadcastDetails is empty");
                        notifyLivingPlayItemGet(playItem, null, null, iPlayListGetListener);
                        return;
                    }
                    PlayerLogUtil.log(getClass().getSimpleName(), "initLiving", "get detal success status: " + broadcastDetails.getStatus());
                    boolean needResetParam = !isIgnoreLocalRadio && broadcastDetails.getCurrentAreaInfo() != null && broadcastDetails.getCurrentAreaInfo().getBroadcastId() != albumId;
                    if (needResetParam) {
                        broadcastDetails = broadcastDetails.getCurrentAreaBroadcastDetails();
                        if (PlayerPreconditions.checkNull(broadcastDetails)) {
                            PlayerLogUtil.log(getClass().getSimpleName(), "initLiving", "success, broadcastDetails is empty");
                            notifyLivingPlayItemGet(playItem, null, null, iPlayListGetListener);
                            return;
                        }
                    }
                    notifyLivingPlayItemGet(playItem, broadcastDetails.getPlayUrl(), broadcastDetails.getPlayInfoList(), iPlayListGetListener);
                }

                @Override
                public void onError(ApiException exception) {
                    PlayerLogUtil.log(getClass().getSimpleName(), "initLiving", "error");
                    notifyPlayListGetError(iPlayListGetListener, new InvalidPlayItem(), finalErrorCode, -1);
                }
            });
            return;
        }

        mBroadcastRequest.getBroadcastProgramDetails(playItem.getAudioId(), new HttpCallback<ProgramDetails>() {
            @Override
            public void onSuccess(ProgramDetails programDetails) {
                PlayerLogUtil.log(getClass().getSimpleName(), "initLiving", "get detail success");
                if (PlayerPreconditions.checkNull(programDetails)) {
                    return;
                }
                PlayerLogUtil.log(getClass().getSimpleName(), "initLiving", "get detal success status: " + programDetails.getStatus());
                BroadcastPlayItem broadcastPlayItem = (BroadcastPlayItem) playItem;
                broadcastPlayItem.setStatus(PlayerConstants.BROADCAST_STATUS_LIVING);
                broadcastPlayItem.getTimeInfoData().setStartTime(programDetails.getStartTime());
                broadcastPlayItem.getTimeInfoData().setFinishTime(programDetails.getFinishTime());
                broadcastPlayItem.getTimeInfoData().setBeginTime(programDetails.getBeginTime());
                broadcastPlayItem.getTimeInfoData().setEndTime(programDetails.getEndTime());
                broadcastPlayItem.getTimeInfoData().setCurSystemTime(DateUtil.getServerTime());
                PlayerLogUtil.log(getClass().getSimpleName(), "initLiving", "success, old url = " + playItem.getPlayUrl() + " , new url= " + programDetails.getPlayUrl());
                broadcastPlayItem.setPlayUrl(programDetails.getPlayUrl());
                broadcastPlayItem.setPlayInfoList(programDetails.getPlayInfoList());
                broadcastPlayItem.setBackPlayInfoList(programDetails.getBackPlayInfoList());
                notifyPlayListGet(iPlayListGetListener, playItem, null);
            }

            @Override
            public void onError(ApiException exception) {
                PlayerLogUtil.log(getClass().getSimpleName(), "initLiving", "error");
                notifyPlayListGetError(iPlayListGetListener, new InvalidPlayItem(), finalErrorCode, -1);
            }
        });
    }

    /**
     * 通知直播PlayItem已经重置播放地址完成
     *
     * @param playItem
     * @param url                  为null时不重置
     * @param playInfos            为null时不重置
     * @param iPlayListGetListener
     */
    private void notifyLivingPlayItemGet(PlayItem playItem, String url, List<AudioFileInfo> playInfos, IPlayListGetListener iPlayListGetListener) {
        BroadcastPlayItem broadcastPlayItem = (BroadcastPlayItem) playItem;
        broadcastPlayItem.setStatus(PlayerConstants.BROADCAST_STATUS_LIVING);
        broadcastPlayItem.getTimeInfoData().setCurSystemTime(DateUtil.getServerTime());
        if (!StringUtil.isEmpty(url)) {
            PlayerLogUtil.log(getClass().getSimpleName(), "notifyLivingPlayItemGet", "success, old url = " + playItem.getPlayUrl() + " , new url= " + url);
            broadcastPlayItem.setPlayUrl(url);
        }
        if (!ListUtil.isEmpty(playInfos))
            broadcastPlayItem.setPlayInfoList(playInfos);
        notifyPlayListGet(iPlayListGetListener, playItem, null);
    }

    private long string2Long(String strValue) {
        long longValue = 0;
        if (!StringUtil.isEmpty(strValue)) {
            try {
                longValue = Long.parseLong(strValue);
            } catch (Exception e) {

            }
        }
        return longValue;
    }

    private void updatePlayListContent(ArrayList<PlayItem> playItemArrayList, IPlayListGetListener iPlayListGetListener) {


        mPlayItemArrayList.addAll(playItemArrayList);
        int index = PlayListUtils.getLivingBroadcastPlayItem(playItemArrayList);
        if (index >= playItemArrayList.size()) {
            index = 0;
        }
        mPosition = index;
        PlayerLogUtil.log(getClass().getSimpleName(), "updatePlayListContent", "修改mPosition = " + mPosition);
        notifyPlayListGet(iPlayListGetListener, playItemArrayList.get(index), playItemArrayList);
        notifyPlayListChange(playItemArrayList);
    }

    /**
     * 补全播单
     * 只从正在播放的节目开始补全，回放节目忽略
     *
     * @param playItemArrayList
     * @param broadcastDetails
     */
    private void complementedPlayList(ArrayList<PlayItem> playItemArrayList, BroadcastDetails broadcastDetails) {
        long serverTime = DateUtil.getServerTime();
        PlayerLogUtil.log(getClass().getSimpleName(), "complementedPlayList", "serverTime=" + serverTime);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date(serverTime));
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        long start = calendar.getTime().getTime();//从00：00：00
        long end = start + 24 * 60 * 60 * 1000 - 1000;  //到23：59：59

        long startTime = 0, endTime = 0;
        //进行播单补全，只需要补全正在播放或者还未开始的节目，对于已经播放结束的，补全没有任何意义
        ListIterator<PlayItem> iterator = playItemArrayList.listIterator();
        while (iterator.hasNext()) {
            PlayItem playItem = iterator.next();
            if (PlayerPreconditions.checkNull(playItem)) {
                //删除null对象
                PlayerLogUtil.log(getClass().getSimpleName(), "complementedPlayList", "playItem is null ,will remove from list");
                iterator.remove();
                continue;
            }
            if (playItem.getFinishTime() < serverTime) continue;    //不处理回放节目

            if (startTime == 0) {
                //此处：playItem.getFinishTime()第一次大于或等于serverTime
                //应继续判断playItem.getStartTime()
                if (playItem.getStartTime() - serverTime > 60_000) {
                    //当前时间点播放的playitem正好处在没有节目单的位置（处在两个playItem之间的空白时间段内），应补充这段时间的item
                    //先移动回上一个
                    endTime = playItem.getStartTime();
                    iterator.previous();
                    if (iterator.hasPrevious()) {
                        PlayItem previous = iterator.previous();
                        if (previous != null) {
                            startTime = previous.getFinishTime();
                            if (startTime < start) startTime = start;   //从00：00：00开始
                            iterator.next();
                            iterator.add(PlayItemUtil.createTimeDiscontinuousBroadcastPlayItem(startTime, endTime, playItem.getPlayUrl(), broadcastDetails.getPlayInfoList(), broadcastDetails.getClassifyId(), broadcastDetails.getBroadcastId(), broadcastDetails.getImg(), broadcastDetails.getName(), broadcastDetails.getFreq(), broadcastDetails.getType(), broadcastDetails.getOnLineNum(), broadcastDetails.getCurrentAreaInfo() == null ? null : broadcastDetails.getCurrentAreaInfo().getAreaCode(), broadcastDetails.getCurrentAreaInfo() == null ? null : broadcastDetails.getCurrentAreaInfo().getAreaName(), broadcastDetails.getProgramEnable()));
                            continue;
                        }
                    }
                    startTime = start;
                    iterator.add(PlayItemUtil.createTimeDiscontinuousBroadcastPlayItem(startTime, endTime, playItem.getPlayUrl(), broadcastDetails.getPlayInfoList(), broadcastDetails.getClassifyId(), broadcastDetails.getBroadcastId(), broadcastDetails.getImg(), broadcastDetails.getName(), broadcastDetails.getFreq(), broadcastDetails.getType(), broadcastDetails.getOnLineNum(), broadcastDetails.getCurrentAreaInfo() == null ? null : broadcastDetails.getCurrentAreaInfo().getAreaCode(), broadcastDetails.getCurrentAreaInfo() == null ? null : broadcastDetails.getCurrentAreaInfo().getAreaName(), broadcastDetails.getProgramEnable()));
                } else {
                    //当前正在播放的就是playItem对象
                    startTime = playItem.getStartTime();
                    endTime = playItem.getFinishTime();
                }
                continue;
            }

            //如果playItem的开始时间与上一个playItem结束时间差距大于1分钟（1分钟是原有时间差距的误差值），则认为播单中断，需要补全
            if (playItem.getStartTime() - endTime > 60_000) {
                //间隔大于1分钟
                startTime = endTime;
                endTime = playItem.getStartTime();
                iterator.previous();
                iterator.add(PlayItemUtil.createTimeDiscontinuousBroadcastPlayItem(startTime, endTime, playItem.getPlayUrl(), broadcastDetails.getPlayInfoList(), broadcastDetails.getClassifyId(), broadcastDetails.getBroadcastId(), broadcastDetails.getImg(), broadcastDetails.getName(), broadcastDetails.getFreq(), broadcastDetails.getType(), broadcastDetails.getOnLineNum(), broadcastDetails.getCurrentAreaInfo() == null ? null : broadcastDetails.getCurrentAreaInfo().getAreaCode(), broadcastDetails.getCurrentAreaInfo() == null ? null : broadcastDetails.getCurrentAreaInfo().getAreaName(), broadcastDetails.getProgramEnable()));
            } else {
                startTime = playItem.getStartTime();
                endTime = playItem.getFinishTime();
            }
        }

        //最后，判断最后一个节目是否到23：59：59
        PlayItem playItem = playItemArrayList.get(playItemArrayList.size() - 1);
        if (end - playItem.getFinishTime() > 60_000) {
            startTime = playItem.getFinishTime();
            endTime = end;
            playItemArrayList.add(PlayItemUtil.createTimeDiscontinuousBroadcastPlayItem(startTime, endTime, playItem.getPlayUrl(), broadcastDetails.getPlayInfoList(), broadcastDetails.getClassifyId(), broadcastDetails.getBroadcastId(), broadcastDetails.getImg(), broadcastDetails.getName(), broadcastDetails.getFreq(), broadcastDetails.getType(), broadcastDetails.getOnLineNum(), broadcastDetails.getCurrentAreaInfo() == null ? null : broadcastDetails.getCurrentAreaInfo().getAreaCode(), broadcastDetails.getCurrentAreaInfo() == null ? null : broadcastDetails.getCurrentAreaInfo().getAreaName(), broadcastDetails.getProgramEnable()));
        }
    }

    private void updatePlayListInfo(BroadcastPlayItem broadcastPlayItem) {
        if (broadcastPlayItem != null) {
            mPlaylistInfo.setAlbumName(broadcastPlayItem.getInfoData().getAlbumName());
            mPlaylistInfo.setAlbumPic(broadcastPlayItem.getInfoData().getAlbumPic());
        }
    }

    @Override
    public PlayItem getPlayItem(PlayerBuilder playerBuilder) {
        long tempId = string2Long(playerBuilder.getId());
        if (isPlaySongList) {
            for (int i = 0; i < mSongPlayItemArrayList.size(); i++) {
                PlayItem playItem = mSongPlayItemArrayList.get(i);
                if (PlayerPreconditions.checkNull(playItem)) {
                    continue;
                }
                if (playItem.getAudioId() == tempId) {
                    PlayerLogUtil.log(getClass().getSimpleName(), "getPlayItem", "son play list has id");
                    mSongListPosition = i;
                    isPlaySongList = true;
                    PlayItem playItem1 = mSongPlayItemArrayList.get(mSongListPosition);
                    if (playItem1 != null) {
                        playItem1.setPosition(0);
                    }
                    return playItem1;
                }
            }
        }
        isPlaySongList = false;
        mSongPlayItemArrayList.clear();

        BroadcastPlayItem playItem = (BroadcastPlayItem) super.getPlayItem(playerBuilder);
        if (PlayerPreconditions.checkNull(playItem)) {
            return null;
        }
        if (playItem.getTimeInfoData().getFinishTime() < DateUtil.getServerTime()) {
            playItem.setStatus(PlayerConstants.BROADCAST_STATUS_PLAYBACK);
        }
        playItem.getTimeInfoData().setCurSystemTime(DateUtil.getServerTime());
        playItem.setPosition(0);
        return playItem;
    }


    @Override
    public ArrayList getSongPlayList() {
        return mSongPlayItemArrayList;
    }

    @Override
    public void addSongPlayItem(Object o) {
        mSongPlayItemArrayList.addAll((ArrayList<PlayItem>) o);
    }

    @Override
    public void removeSongPlayItem(Object o) {
        mSongPlayItemArrayList.clear();
    }

    @Override
    public boolean isPlayingSonList() {
        return isPlaySongList;
    }

    /**
     * 当前播单是否存在audioId为指定值的节目
     * 已废弃，请使用{@link BroadcastPlayListControl#isExistPlayItem(PlayItem)}代替
     *
     * @param id 给定节目的audioId
     * @return
     */
    @Deprecated
    @Override
    public boolean isExistPlayItem(long id) {
        boolean isExist = super.isExistPlayItem(id);
        if (isExist) {
            return true;
        }
        PlayerLogUtil.log(getClass().getSimpleName(), "isExistPlayItem", "father play list not has");
        if (PlayerPreconditions.checkNull(mSongPlayItemArrayList)) {
            return false;
        }
        for (int i = 0; i < mSongPlayItemArrayList.size(); i++) {
            PlayItem playItem = mSongPlayItemArrayList.get(i);
            if (PlayerPreconditions.checkNull(playItem)) {
                continue;
            }
            if (playItem.getAudioId() == id) {
                PlayerLogUtil.log(getClass().getSimpleName(), "isExistPlayItem", "son play list, position = " + i);
                mSongListPosition = i;
                isPlaySongList = true;
                return true;
            }
        }
        PlayerLogUtil.log(getClass().getSimpleName(), "isExistPlayItem", "son play list not has");
        return false;
    }

    /**
     * 当前播单是否存在audioId为指定值的节目
     * 如果想要查询的PlayItem继承自{@link TimeDiscontinuousBroadcastPlayItem},则需要通过节目开始时间进行辅助查询，因为播单补全节目的audioId为0
     *
     * @param playItem 给定节目
     * @return
     */
    @Override
    public boolean isExistPlayItem(PlayItem playItem) {
        PlayerLogUtil.log(getClass().getSimpleName(), "isExistPlayItem", "father play list not has");
        if (playItem == null) return false;
        if (playItem instanceof TimeDiscontinuousBroadcastPlayItem) {
            //时间补全的节目，需要额外判断开始时间
            if (isExistPlayItemInPlayItemArrayList(playItem.getAudioId(), playItem.getStartTime()))
                return true;
            if (PlayerPreconditions.checkNull(mSongPlayItemArrayList)) {
                return false;
            }
            for (int i = 0; i < mSongPlayItemArrayList.size(); i++) {
                PlayItem temp = mSongPlayItemArrayList.get(i);
                if (PlayerPreconditions.checkNull(temp)) {
                    continue;
                }
                if (temp.getAudioId() == playItem.getAudioId() && temp.getStartTime() == playItem.getStartTime()) {
                    PlayerLogUtil.log(getClass().getSimpleName(), "isExistPlayItem", "son play list, position = " + i);
                    mSongListPosition = i;
                    isPlaySongList = true;
                    return true;
                }
            }
        } else {
            boolean isExist = super.isExistPlayItem(playItem.getAudioId());
            if (isExist) {
                return true;
            }
            if (PlayerPreconditions.checkNull(mSongPlayItemArrayList)) {
                return false;
            }
            for (int i = 0; i < mSongPlayItemArrayList.size(); i++) {
                PlayItem temp = mSongPlayItemArrayList.get(i);
                if (PlayerPreconditions.checkNull(temp)) {
                    continue;
                }
                if (temp.getAudioId() == playItem.getAudioId()) {
                    PlayerLogUtil.log(getClass().getSimpleName(), "isExistPlayItem", "son play list, position = " + i);
                    mSongListPosition = i;
                    isPlaySongList = true;
                    return true;
                }
            }
        }
        PlayerLogUtil.log(getClass().getSimpleName(), "isExistPlayItem", "son play list not has");
        return false;
    }

    /**
     * 在mPlayItemArrayList中是否存在audioId为{id}且startTime为{startTime}的节目
     *
     * @param id
     * @param startTime
     * @return
     */
    private boolean isExistPlayItemInPlayItemArrayList(long id, long startTime) {
        if (PlayerPreconditions.checkNull(mPlayItemArrayList)) {
            return false;
        }
        for (int i = 0; i < mPlayItemArrayList.size(); i++) {
            PlayItem playItem = mPlayItemArrayList.get(i);
            if (PlayerPreconditions.checkNull(playItem)) {
                continue;
            }
            if (playItem.getAudioId() == id && playItem.getStartTime() == startTime) {
                addPlayItemParameter(i, playItem);
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean hasNext() {
        if (isPlaySongList) {
            if (mSongListPosition + 1 < 0) {
                return false;
            }
            if (mSongListPosition + 1 < mPlayItemArrayList.size()) {
                return true;
            }
            return false;
        }
        return super.hasNext();
    }

    @Override
    public void release() {
        super.release();
        if (mSongPlayItemArrayList != null) {
            mSongPlayItemArrayList.clear();
            isPlaySongList = false;
            mSongListPosition = -1;
        }
    }

    @Override
    public void setCurPosition(PlayItem playItem) {
        if (isPlaySongList) {
            for (int i = 0; i < mPlayItemArrayList.size(); i++) {
                PlayItem playItemTemp = mPlayItemArrayList.get(i);
                if (PlayerPreconditions.checkNull(playItemTemp)) {
                    continue;
                }
                if (playItemTemp.getAudioId() == playItem.getAudioId() && playItemTemp.getStartTime() == playItem.getStartTime()) {
                    playItem.setPosition(0);
                    mSongListPosition = i;
                    PlayerLogUtil.log(getClass().getSimpleName(), "setCurPosition", "position: " + mSongListPosition);
                    return;
                }
            }
        } else {
//            BroadcastPlayItem broadcastPlayItem = ((BroadcastPlayItem)playItem);
//            if(broadcastPlayItem.getTimeInfoData().getStartTime() > DateUtil.getServerTime()){
////                InvalidPlayItem invalidPlayItem = PlayListUtils.translateBroadcastToInvalidPlayItem(broadcastPlayItem);
////                notifyPlayListChangeError(invalidPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_SET_ITEM_TIME, -1);
//                return;
//            }
            setCurBroadcastPosition(playItem);

        }
    }

    private void setCurBroadcastPosition(PlayItem playItem) {
        if (PlayerPreconditions.checkNull(playItem)) {
            return;
        }
        if (playItem instanceof InvalidPlayItem) {
            mPosition = 0;
            PlayerLogUtil.log(getClass().getSimpleName(), "setCurPosition(O)", "修改mPosition = " + mPosition);
            mCurPlayItem = playItem;
            return;
        }
        if (PlayerPreconditions.checkNull(mPlayItemArrayList)) {
            return;
        }

        for (int i = 0; i < mPlayItemArrayList.size(); i++) {
            PlayItem playItemTemp = mPlayItemArrayList.get(i);
            if (PlayerPreconditions.checkNull(playItemTemp)) {
                continue;
            }
            if (playItemTemp.getAudioId() == playItem.getAudioId() && playItemTemp.getStartTime() == playItem.getStartTime()) {
                mPosition = i;
                PlayerLogUtil.log(getClass().getSimpleName(), "setCurPosition(O)", "修改mPosition = " + mPosition);
                mCurPlayItem = playItem;
                PlayerLogUtil.log(getClass().getSimpleName(), "setCurPosition", " position = " + mPosition);
                return;
            }
        }
        mPosition = -1;
        PlayerLogUtil.log(getClass().getSimpleName(), "setCurPosition(O)", "修改mPosition = " + mPosition);
        mCurPlayItem = null;
    }

    @Override
    public int getCurPosition() {
        if (isPlaySongList) {
            return mSongListPosition;
        }
        return super.getCurPosition();
    }
}
