package com.kaolafm.report.api.report;


import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.log.Logging;
import com.kaolafm.report.BuildConfig;
import com.kaolafm.report.api.ReportHostConstant;
import com.kaolafm.report.util.ReportConstants;

import io.reactivex.functions.Function;
import retrofit2.Response;

/**
 * <AUTHOR> on 2019/3/1.
 */

public class ReportBigDataRequest extends BaseRequest {
    private String ReportBigDataApiUrl = "https://reportv2.radio.cn/"+
            (BuildConfig.BUILD_TYPE.equals("release")?"collection":"test")+
            "?";
    private ReportBigDataApiService reportApiService;

    public ReportBigDataRequest() {
        mUrlManager.putDomain(ReportHostConstant.REPORT_DOMAIN_NAME, ReportHostConstant.REPORT_BASE_URL);
        reportApiService = mRepositoryManager.obtainRetrofitService(ReportBigDataApiService.class);
    }

    private Function<Response<Void>, Boolean> map() {
        return retrofit2.Response::isSuccessful;
    }

    public void getReport(String json, HttpCallback<Boolean> callback) {
        Logging.i(ReportConstants.REPORT_BIGDATA_TAG, "发送数据 = " + json);

        doHttpDeal(reportApiService.report(ReportBigDataApiUrl+json),map(),callback);


//        Logging.i(ReportConstants.REPORT_BIGDATA_TAG, "发送数据 = " + json);
//        doHttpDeal(reportApiService.report(ReportBigDataApiUrl + json), map(), new HttpCallback<Boolean>() {
//            @Override
//            public void onSuccess(Boolean aBoolean) {
//                Logging.i(ReportConstants.REPORT_TAG, "发送完数据: isSuccessful = ");
//                return aBoolean;
//            }
//
//            @Override
//            public void onError(ApiException exception) {
//                Logging.i(ReportConstants.REPORT_TAG, "发送完数据: isSuccessful = ");
//                return false;
//         }
//        });
//
//        Response<Void> response = doHttpDeal(reportApiService.report(ReportBigDataApiUrl + json),
//                map(), callback);
//
//        if (response != null) {
//            Logging.i(ReportConstants.REPORT_TAG, "发送完数据: isSuccessful = " + response.isSuccessful() + " message = " + response.message() + " code = " + response.code() + " body = " + response.body());
//        } else {
//            Logging.i(ReportConstants.REPORT_TAG, "发送数据 完成 result 为空");
//        }
//
//        return response != null && response.isSuccessful();
    }

}
