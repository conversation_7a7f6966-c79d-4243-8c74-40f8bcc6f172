# 云听车载版 Android SDK 接入文档  V1.9.0

[TOC]

* * *

# 1 概述及名词解释
--

## 1.1 概述
**云听车载版 Andriod SDK 文档面向车厂车机应用开发者。用于指导开发者快速接入云听提供的功能服务。包含获取栏目、分类、语音搜索、专辑、AI电台、在线广播、听电视内容等功能。**

## 1.2 名词解释
- **AI电台：** 通过智能化的编排规则，将不同主播的节目串成的一个节目流。AI电台支持个性化，同一个电台不同的用户听到的内容会有差异。

- **专辑：** 某一个主播或编辑的某一档节目，也可能是一部有声读物。比如《云听资讯》。

- **单曲：** 指专辑或者AI电台下面的每一条音频。是内容构成的最小粒度。比如《云听资讯》的第一期节目。

- **在线广播：** 云听整合了千余个在线广播，用户可以回听，点播广播节目。

- **听电视：** 云听集纳总台CCTV1-5、CCTV5＋、CCTV13共7个频道的直播流和精品节目，通过音频化运营，实现电视内容从“看”到“听”的转化，为车主增加更多元的收听选择。

示意图如下：
```mermaid
graph TB
  subgraph 听电视
    回听节目2
    直播节目2
    未开始节目2
  end
  subgraph 在线广播
    回听节目1
    直播节目1
    未开始节目1
  end
  subgraph AI电台
    单曲3
    单曲4
    ...
  end
  subgraph 专辑
    单曲1
    单曲2
    ....
  end
```

# 2 接入准备
## 2.1 获取AppId、AppKey和Channel
**需要获取到云听车载版平台的AppId、AppKey和Channel，请与项目经理联系。**

## 2.2 环境搭建
云听车载版 SDK 是以远程仓库的方式接入，不支持其他方式(如添加jar包的方式)。   
故只支持Android Studio接入，不支持Eclipse接入。

添加依赖
**在项目根目录的build.gradle中设置中央仓库**

```gradle
maven { 
	url "http://pub.nexus.kaolafm.com:8082/repository/github-self" 
}
```

**在需要引入SDK的module目录的build.gradle中引入open-sdk依赖**
==注意==：请从项目经理获取最新的版本号
```gradle
implementation('com.kaolafm:open-sdk:版本号'){
	changing=true
}

```

SDK中使用到了第三方的依赖，如果与接入方的项目的依赖库产生冲突可以使用exclude排除， 比如：

```gradle
implementation('com.kaolafm:open-sdk:版本号'){
        exclude group: 'com.android.volley'
        exclude module: 'gson'
    }

```

## 2.3 配置和权限

### 2.3.1 添加权限
``` xml
<uses-permission android:name="android.permission.INTERNET" /><!-- 通过网络访问音频内容 -->
<uses-permission android:name="android.permission.ACCESS_WIFI_STATE" /><!-- 网络状态的判断 -->
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /><!-- 网络状态的判断 -->
<uses-permission android:name="android.permission.READ_PHONE_STATE" /><!-- 用于生成设备标识符  -->
<uses-permission android:name="android.permission.WAKE_LOCK" /><!-- 使设备保持唤醒状态 
 -->
```

### 2.3.2 配置播放器
**需要使用sdk播放器，其核心Service需要在AndroidManifest.xml中配置**
``` xml
<service android:name="com.kaolafm.opensdk.player.core.PlayerService"  />

```

### 2.3.3 其他相关配置
``` xml      
<meta-data
     android:name="com.kaolafm.open.sdk.AppKey"
     android:value="8829ba31c625290324b9494c7c20b9e9" /> <!--此为举例，请填写申请开发所需APP_KEY配置，由我方项目经理提供-->
<meta-data
	 android:name="com.kaolafm.open.sdk.AppId"
	 android:value="wl6113" /> <!--此为举例，请填写申请开发所需APP_ID配置，由我方项目经理提供-->
<meta-data
	 android:name="com.kaolafm.open.sdk.Channel"
	 android:value="kradioyunyingdemo4" /> <!--此为举例，请填写开发者所需要发布的渠道名称，由双方来确定-->

<!-- 选填配置 -->	 
<meta-data
	 android:name="com.kaolafm.open.sdk.CarType"
	 android:value="SUV" /> <!-- 此为举例，请填写接入SDK的车型 --> 
```

## 2.4 混淆
```
-dontwarn com.kaolafm.**
-keep class com.kaolafm.** { *; }
-keepclasseswithmembers class tv.danmaku.ijk.media.player.IjkMediaPlayer {
    <fields>;
    <methods>;
}
```

# 3 接入详情说明
所有的API都已经进行了处理，可以直接在主线程调用。

接口都可以通过绑定生命周期或设置tag取消网络请求。

示例

```java
//第一种方式，绑定生命周期
LoginRequest request = new LoginRequest().bindLifecycle(lifecycleTransformer);
//第二种方式，设置tag
LoginRequest request = new LoginRequest().setTag("LoginRequest");
request.cancel("LoginRequest");
```

## 3.1 初始化
 初始化主要包括SDK的初始化和设备激活。所在类`OpenSDK`。

**初始化流程图**

```flow
st=>start: 获取AppId、AppKey和Channel
en=>operation: 环境搭建（从项目经理获取最新版本号）
init=>operation: 初始化(每次启动都要初始化)
activite=>operation: 激活(只需初始化一次)
isActivite=>condition: 是否激活
e=>end: 使用接口

st->en->init->isActivite
isActivite(yes)->e
isActivite(no)->activite->e
```
### 3.1.1 应用初始化
**接口名称**

`
OpenSDK#initSDK(Application application,HttpCallback<Boolean> callback)
`

**接口说明**

用于初始化SDK，在使用SDK接口之前必须调用一次该方法，否则SDK处于未初始化状态，无法使用其他接口。
备注：需要保证public void onSuccess(Boolean isSuccess)  函数返Boolean值为true

**参数说明**

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
|application|Application|是|应用程序上下文|
|callback|HttpCallback|否|初始化结果回调（失败则返回错误原因，可参见[5.1 ErrorCode](#5.1 ErrorCode)）|

**代码示例**

```java
OpenSDK.getInstance().initSDK(getApplication(), new HttpCallback<Boolean>() {
                        @Override
                        public void onSuccess(Boolean isSuccess) {
                            showToast(isSuccess ? "初始化SDK成功" : "初始化SDK失败");
                        }

                        @Override
                        public void onError(ApiException exception) {
                            showToast("初始化SDK失败，错误码=" + exception.getCode() + ",错误信息=" + exception.getMessage());
                        }
                    });
```

### 3.1.2 应用激活
**接口名称**

`OpenSDK#activate(HttpCallback<Boolean> callback)` 

**接口说明**

设备第一次启动应用时需要调用该接口，否则其他所有接口都无法使用。

**参数说明**

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
|callback|HttpCallback|否|激活结果回调|

**代码示例**

``` java
OpenSDK.getInstance().activate(new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean isSuccess) {
                showToast(isSuccess ? "激活成功" : "激活失败");
            }

            @Override
            public void onError(ApiException exception) {
                showToast("激活失败，错误码：" + exception.getCode() + ", 错误信息：" + exception.getMessage());
            }
        });
```

### 3.1.3 是否激活
**接口名称**

`OpenSDK.getInstance().isActivate()`

**接口说明**

该接口用于判断应用是否已经激活。

**返回值说明**

返回true表示已经激活；false表示未激活，需要调用激活接口激活。

**代码示例**

```java
OpenSDK openSDK = OpenSDK.getInstance();
if (!openSDK.isActivate()) {
    openSDK.activate(new HttpCallback<Boolean>() {
        @Override
        public void onSuccess(Boolean aBoolean) {
            showToast(aBoolean ? "激活成功=" + autoId : "激活失败");
            isActivated = true;
        }

        @Override
        public void onError(ApiException exception) {
            showToast("激活失败，错误码：" + exception.getCode() + ", 错误信息：" + exception.getMessage());
        }
    });
}
```

### 3.1.4 初始化并激活
为了简化接入流程，将初始化和激活封装成一个接口。

**接口名称**

`OpenSDK#initAndActivate(Application application, HttpCallback<Boolean> callback)`

**接口说明**

该接口合并了初始化和激活接口，接入时，只需要在调用SDK的接口前调用一次该接口就行。应用每次启动都需要调用一次。

**参数说明**

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
|application|Application|是|应用的Application|
|callback|HttpCallback|否|激活结果回调|

**代码示例**

```java
openSDK.initAndActivate(getApplication(), new HttpCallback<Boolean>() {
                        @Override
                        public void onSuccess(Boolean aBoolean) {
                            String openId = AccessTokenManager.getInstance().getKaolaAccessToken().getOpenId();
                            showToast(aBoolean ? "初始化、激活成功=" + openId : "初始化、激活失败");
                            isInitialized = aBoolean;
                            isActivated = aBoolean;
                        }

                        @Override
                        public void onError(ApiException exception) {

                        }
                    });
```

### 3.1.5 获取设备ID

**接口名称**
`OpenSDK.getInstance().getDeviceId()`

**接口说明**
该接口用于获取SDK内部生成的设备ID。如果先调用[设置设备ID](#3.1.6 设置设备ID)，则返回设置的设备ID。

**参数说明**
无

**代码示例**

```java
String deviceId = OpenSDK.getInstance().getDeviceId();
```

### 3.1.6 设置设备ID

**接口名称**
`OpenSDK.getInstance().setDeviceId(String deviceId)`

**接口说明**
该接口用于向SDK设置设备ID。注：使用时，请在激活前调用，否则激活使用的是SDK内部生成的设备ID。

**参数说明**

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
|deviceId|String|是|设备ID|

**代码示例**

```java
OpenSDK.getInstance().setDeviceId("xxxxxxxx");
```

## 3.2 账号 

用于实现手机云听App扫码登录的功能。所在类`LoginRequest`。

**扫描登录流程**
 ```flow
st=>start: 登录云听手机版
end=>end: 登录成功
getQRCode=>operation: 车载端获取登录二维码
scanQRCode=>operation: 云听手机版扫描二维码
check=>operation: 车载端循环查询二维码状态
judge=>condition: 判断二维码状态
fetchCode=>operation: 获取code值
bind=>operation: 使用code值登录云听

st->getQRCode->scanQRCode->check->judge
judge(no)->check
judge(yes)->fetchCode
fetchCode->bind->end
 ```

==注意==：为了简化对接流程，在获取二维码后，可以直接调用 [获取二维码后自动登录](#3.2.5 获取二维码后自动登录) 接口

### 3.2.1 获取云听登录二维码
**接口名称**

`LoginRequest#getQRCode(HttpCallback<QRCodeInfo> callback)`

**接口说明**

用于获取云听账号登录的二维码，用云听手机版扫描该二维码可以实现登录。
返回数据中包含二维码地址和其他接口需要用到的UUID，UUID与展示的二维码一一对应。

**参数说明**

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
|callback|HttpCallback|否|结果回调，返回[QRCodeInfo](#9.41 QRCodeInfo)对象，其中包含二维码地址|

**示例代码**

```java
mLoginRequest.getQRCode(new HttpCallback<QRCodeInfo>() {
            @Override
            public void onSuccess(QRCodeInfo qrCodeInfo) {
                mUuid = qrCodeInfo.getUuid();
                Glide.with(KaolaLoginActivity.this)
                        .load(qrCodeInfo.getQRCodePath())
                        .into(mIvKaolaQrCode);
            }

            @Override
            public void onError(ApiException exception) {
                showStatus("获取二维码错误，错误码：" + exception.getCode() + ", 错误信息：" + exception.getMessage());
            }
        });
```

### 3.2.2 检查云听登录二维码状态
**接口名称**

`LoginRequest#checkQRCodeStatus(String uuid, HttpCallback<Integer> callback)`

**接口说明**

用于检查登录二维码的状态，会返回四个状态：等待扫描、二维码过期、已授权、已被扫描还未登录。分别对应`QRCodeInfo.STATUS_NORMAL`、`QRCodeInfo.STATUS_LOSE_EFFICACY`、`QRCodeInfo.STATUS_AUTHORIZATION`、`QRCodeInfo.STATUS_SCANED`

一般是用一个循环不断的请求获取二维码状态，建议间隔时间为1秒

**参数说明**

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
| uuid |String|是|二维码唯一标识，获取二维码时会同时给到该标识。|
|callback|HttpCallback|否|结果回调，返回二维码状态。<br>四个状态：等待扫描、二维码过期、已授权、已被扫描还未登录。<br>分别对应<br>`QRCodeInfo.STATUS_NORMAL`、<br/>`QRCodeInfo.STATUS_LOSE_EFFICACY`、<br/>`QRCodeInfo.STATUS_AUTHORIZATION`、<br/>`QRCodeInfo.STATUS_SCANED`|

**示例代码**

```java
if (!TextUtils.isEmpty(mUuid)) {
            mLoginRequest.checkQRCodeStatus(mUuid, new HttpCallback<Integer>() {
                @Override
                public void onSuccess(Integer integer) {
                    switch (integer) {
                        case QRCodeInfo.STATUS_NORMAL:
                            showStatus("等待扫码");
                            break;
                        case QRCodeInfo.STATUS_LOSE_EFFICACY:
                            showStatus("二维码过期. 需要重新请求二维码");
                            dispose();
                            break;
                        case QRCodeInfo.STATUS_AUTHORIZATION:
                            showStatus("已授权");
                            dispose();
                            break;
                        case QRCodeInfo.STATUS_SCANED:
                            showStatus("已被扫描还未登录");
                            dispose();
                            break;
                        default:
                    }
                }

                @Override
                public void onError(ApiException exception) {

                }
            });
        }
```

### 3.2.3 获取登录需要的Code值
**接口名称**

`LoginRequest#fetchCode(String uuid, HttpCallback<String> callback)`

**接口说明**

根据uuid获取用于登录的code。

**参数说明**

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
|uuid|String|是|二维码唯一标识，获取二维码时会同时给到该标识。|
|callback|HttpCallback|否|结果回调，返回Code值|

**示例代码**

```java
if (!TextUtils.isEmpty(mUuid)) {
            mLoginRequest.fetchCode(mUuid, new HttpCallback<String>() {
                @Override
                public void onSuccess(String s) {
                    mCode = s;
                    showStatus("获取code：" + s);
                }

                @Override
                public void onError(ApiException exception) {
                    showStatus("获取二维码错误，错误码：" + exception.getCode() + ", 错误信息：" + exception.getMessage());
                }
            });
        } else {
            showToast("uuid为空");
        }

```

### 3.2.4 设备登录云听账号
**接口名称**

通过code登录云听账号。

`LoginRequest#authorizedByCode(String code, HttpCallback<UserInfo> callback)`

直接通过uuid登录云听账号。

`LoginRequest#authorizedByUuid(String uuid, HttpCallback<UserInfo> callback) `

**接口说明**

接口`authorizedByCode()`可以通过接口`fetchCode()`获取的code进行云听登录，返回用户信息。

接口`authorizedByUuid()`可以通过获取二维码返回的uuid直接进行云听登录，该方法不需要自己手动获取code。结果回调会返回用户信息。

**参数说明**

|参数名|类型|是否必须| 描述                                                     |
|:---|:---|:---|:-------------------------------------------------------|
|callback|HttpCallback|否| 结果回调，如果已经登录会返回用户信息[UserInfo](#9.35 UserInfo)，包括用户昵称和头像 |
|code|String|是| 用于登录云听的code值，通过fetchCode(String, HttpCallback)获得       |
|uuid|String|是| 二维码唯一标识，获取二维码时会同时给到该标识。                                |

**示例代码**

```java
//通过code登录云听账号。
mLoginRequest.authorizedByCode(mCode, new HttpCallback<UserInfo>() {
                @Override
                public void onSuccess(UserInfo userInfo) {
                    Log.e("KaolaLoginActivity", "onSuccess: "+userInfo);
                }

                @Override
                public void onError(ApiException exception) {

                }
            });
//直接通过uuid登录云听账号。
mLoginRequest.authorizedByUuid(mUuid, new HttpCallback<UserInfo>() {
                @Override
                public void onSuccess(UserInfo userInfo) {
                    Log.e("KaolaLoginActivity", "onSuccess: "+userInfo);
                }

                @Override
                public void onError(ApiException exception) {

                }
            });
```

### 3.2.5 获取二维码后自动登录
**接口名称**

`LoginRequest#loginWhenAuthorized(String uuid, long time, HttpCallback<UserInfo> callback)`

`LoginRequest#loginWhenAuthorized(String uuid, HttpCallback<UserInfo> callback)`

**接口说明**

当扫描二维码，授权成功时，自动进行登录。二维码过期时会回调onError()。

重载方法 默认1000毫秒轮询一下查询二维码状态。

==注意==：使用该接口要在适当的时候（如页面销毁）进行取消操作cancel(Object)

**参数说明**

| 参数名   | 类型         | 是否必须 | 描述                                     |
| -------- | ------------ | -------- |----------------------------------------|
| uuid     | String       | 是       | 二维码唯一标识，获取二维码时会同时给到该标识                 |
| time     | long       | 否       | 轮询查询二维码状态的间隔时间，建议1000毫秒。               |
| callback | HttpCallback | 否       | 结果回调，登录成功返回用户信息[UserInfo](#9.35 UserInfo)，包括用户昵称和头像 |

**示例代码**

```java
mLoginRequest.loginWhenAuthorized(mUuid, new HttpCallback<UserInfo>() {
                @Override
                public void onSuccess(UserInfo userInfo) {
                    Log.e("KaolaLoginActivity", "onSuccess: "+userInfo);
                    dispose();
                }

                @Override
                public void onError(ApiException exception) {
                    Log.e("KaolaLoginActivity", "onError: "+exception);
                }
            });
```

### 3.2.6 退出云听
**接口名称**

`LoginRequest#logoutYunting(HttpCallback<Boolean> callback)`

**接口说明**

退出云听登录。

**参数说明**

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
|callback|HttpCallback|否|结果回调，返回true表示退出成功。|

**示例代码**

```java
mLoginRequest.logoutTingban(new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean aBoolean) {
                Log.e("KaolaLoginActivity", "onSuccess: "+aBoolean);
            }

            @Override
            public void onError(ApiException exception) {

            }
        });
```

### 3.2.7 账号管理
该接口用于检查账号登录或退出登录，所在类AccessTokenManager。

检查账号登录  

```java
//true表示云听账号已经登录，false表示未登录
boolean login = AccessTokenManager.getInstance().getKaolaAccessToken().isLogin();
```
退出登录见[退出云听](#3.2.6 退出云听)

### 3.2.8 云听账号收听时长
**接口名称**

`LoginRequest#getCumPlaytime(HttpCallback<CumPlaytimeInfo> callback)`

**接口说明**

获取云听账号收听时长（仅记录车载端）
此接口登录云听账号才可以使用。

**参数说明**

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
|callback|HttpCallback|是|结果回调，返回用户收听时长信息[CumPlaytimeInfo](#9.52 CumPlaytimeInfo) |

**示例代码**

```java
 new LoginRequest().getCumPlaytime(new HttpCallback<CumPlaytimeInfo>() {
                    @SuppressLint("SetTextI18n")
                    @Override
                    public void onSuccess(CumPlaytimeInfo cumPlaytimeInfo) {
                    }

                    @Override
                    public void onError(ApiException e) {
                    }
                });

```
## 3.3 运营分类

分类是云听车载提供的对同种内容类型内容的多级树形组织结构，支持多种内容类型独立展示，包括：
专辑及其分类：小说，音乐，儿童，娱乐，文史，汽车，情感，曲艺，健康等等；
AI电台：场景、时刻、新闻、音乐、财经、搞笑等等；
在线广播：本地广播、国家台、省市台（30多个省或直辖市等）、分类广播（例如综合台、音乐台等）
综合：专辑、听电视、AI电台、在线广播。
听电视：正在直播、央视新闻、综艺、影视剧、文史、生活。
分类以树形结构组织数据，分类相关接口之间存在依赖关系，下级接口获取数据时依赖上级接口返回的数据。


所在类`OperationRequest`。

==注意：==所有分类接口返回的数据中的code只能用于请求分类相关接口，不是节目的Id，不能用于获取节目详细信息或播单；并且后台会随时调整分类及其内容，code和name值不是固定的。获取节目详情或播单需要使用对应的节目Id，如[AlbumCategoryMember](#9.7.1 AlbumCategoryMember)中的albumId，可以使用工具类[OperationAssister](#7.1 OperationAssister)获取Id。

* #### 分类整体框架图

```mermaid
graph TD
    subgraph 运营分类
    A1((根分类A1))---| |B1((分类B1))
    A1((根分类A1))---| |B2((分类B2))
    B1---| |C1((叶子分类C1))
    B1---| |C2((叶子分类C2))
    B1---| |C3((叶子分类C3))
    B2---| |C4((叶子分类C4))
    B2---| |C5((叶子分类C5))
    B2---| |C6((叶子分类C6))
    C1---| |分类成员D1
    C1---| |分类成员D2
    C2---| |分类成员D3
    C2---| |分类成员D4
    C3---| |分类成员D5
    C3---| |分类成员D6
    C4---| |分类成员D7
    C4---| |分类成员D8
    C5---| |分类成员D9
    C5---| |分类成员D10
    C6---| |分类成员D11
    C6---| |分类成员D12
    end
```

### 3.3.1 获取指定内容类型的整棵分类树
**接口名称**

全参数接口   

`OperationRequest#getCategoryTree(int contentType, String zone, Map<String, String> extInfo,
            HttpCallback<List<Category>> callback)`    

重载接口，只有必填参数

`OperationRequest#getCategoryTree(int contentType, HttpCallback<List<Category>> callback)`

重载接口

`OperationRequest#getCategoryTree(int contentType,  String zone, HttpCallback<List<Category>> callback)`

**接口说明**

获取某一内容类型的整棵分类树。该接口获取的分类树包括根分类及其子分类，不包括分类成员。分类([Category](#9.6 Category))下才会有子分类；叶子分类([LeafCategory](#9.6.1 LeafCategory))下才会有分类成员([CategoryMember](#9.7 CategoryMember))。

**参数说明**

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
| contentType |int|是|要获取分类树的内容类型[ResType](#9.31 ResType)，<br>综合-`ResType.TYPE_ALL`<br>专辑-`ResType.TYPE_ALBUM`<br>在线广播-`ResType.TYPE_BROADCAST`<br>直播-`ResType.TYPE_LIVE`<br>AI电台-`ResType.TYPE_RADIO`<br>新闻模式-`ResType.TYPE_NEWS`<br>听电视-`ResType.TYPE_TV`<br>==目前仅支持上述7种内容类型==|
| zone |String|是|运营区域，默认传入mainPage|
| extraInfo |Map&lt;String,String&gt;|否|额外信息，暂时无用|
|callback|HttpCallback|否|结果回调，成功返回[Category](#9.6 Category)对象的List集合|

**代码示例**

```java
new OperationRequest().getCategoryTree(contentType, zone, hashMap, new HttpCallback<List<Category>>() {
            @Override
            public void onSuccess(List<Category> categories) {
                if (!ListUtil.isEmpty(categories)) {
                    if (!TextUtils.equals("0", mParentCode)) {
                        categories = categories.get(0).getChildCategories();
                    }
                }else {
                    showToast("数据为空");
                }
                mCategoryAdapter.setDataList(categories);
                mTrfCategoryRefresh.finishLoadmore();
                mTrfCategoryRefresh.finishRefreshing();
            }

            @Override
            public void onError(ApiException exception) {
                showToast("网络请求错误，错误码：" + exception.getCode() + ", 错误信息：" + exception.getMessage());
                if (mTrfCategoryRefresh != null) {
                    mTrfCategoryRefresh.finishLoadmore();
                    mTrfCategoryRefresh.finishRefreshing();
                }
            }
        });
```

**接口使用说明**

根据 [分类整体框架图](#分类整体框架图)，使用```getCategoryTree```接口，返回的数据包含： A1、B1、B2、C1、C2、C3、C4、C5、C6。如图：

```mermaid
graph TD
    subgraph 运营分类
    A1((根A1))---| |B1((分类B1))
    A1((根A1))---| |B2((分类B2))
    B1---| |C1((叶子分类C1))
    B1---| |C2((叶子分类C2))
    B1---| |C3((叶子分类C3))
    B2---| |C4((叶子分类C4))
    B2---| |C5((叶子分类C5))
    B2---| |C6((叶子分类C6))
    C1---| |分类成员D1
    C1---| |分类成员D2
    C2---| |分类成员D3
    C2---| |分类成员D4
    C3---| |分类成员D5
    C3---| |分类成员D6
    C4---| |分类成员D7
    C4---| |分类成员D8
    C5---| |分类成员D9
    C5---| |分类成员D10
    C6---| |分类成员D11
    C6---| |分类成员D12
    style A1 fill:#f9f
    style B1 fill:#f9f
    style B2 fill:#f9f
    style C1 fill:#f9f
    style C2 fill:#f9f
    style C3 fill:#f9f
    style C4 fill:#f9f
    style C5 fill:#f9f
    style C6 fill:#f9f
    end
```

### 3.3.2 获取指定内容类型的根分类列表
**接口名称**

全参数接口   
`OperationRequest#getCategoryRoot(int contentType, String zone, Map<String, String> extInfo, HttpCallback<List<Category>> callback)`

重载接口
`OperationRequest#getCategoryRoot(int contentType, String zone, HttpCallback<List<Category>> callback)`

重载接口
`OperationRequest#getCategoryRoot(int contentType, HttpCallback<List<Category>> callback)`



**接口说明**

获取某一内容类型的根分类列表，只获取最上层的的分类。分类([Category](#9.6 Category))下才会有子分类；叶子分类([LeafCategory](#9.6.1 LeafCategory))下才会有分类成员([CategoryMember](#9.7 CategoryMember))

**参数说明**

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
| contentType |int|是|要获取分类树的内容类型[ResType](#9.31 ResType)，<br>综合-`ResType.TYPE_ALL`<br>专辑-`ResType.TYPE_ALBUM`<br>在线广播-`ResType.TYPE_BROADCAST`<br>直播-`ResType.TYPE_LIVE`<br>AI电台-`ResType.TYPE_RADIO`<br>新闻模式-`ResType.TYPE_NEWS`<br>听电视-`ResType.TYPE_TV`<br>==目前仅支持上述7种内容类型==|
|zone|String|是|运营区域，默认传入mainPage|
|extraInfo|Map&lt;String, String&gt;|否|额外信息，暂时无用|
|callback|HttpCallback|否|结果回调，<br>成功返回[Category](#9.6 Category)对象的List集合|

**代码示例**

```java
new OperationRequest().getCategoryRoot(contentType, zone, hashMap, new HttpCallback<List<Category>>() {
            @Override
            public void onSuccess(List<Category> categories) {
                mCategoryAdapter.setDataList(categories);
                if (categories == null || categories.isEmpty()) {
                    showToast("数据为空");
                }
            }

            @Override
            public void onError(ApiException exception) {
                showToast("网络请求错误，错误码：" + exception.getCode() + ", 错误信息：" + exception.getMessage());
            }
        });
```

**接口使用说明**

根据 [分类整体框架图](#分类整体框架图)，使用```getCategoryRoot```接口，返回的数据包含：A1、或A1同级别的数据。如图：

```mermaid
graph TD
    subgraph 运营分类
    A1((根A1))---| |B1((分类B1))
    A1((根A1))---| |B2((分类B2))
    B1---| |C1((叶子分类C1))
    B1---| |C2((叶子分类C2))
    B1---| |C3((叶子分类C3))
    B2---| |C4((叶子分类C4))
    B2---| |C5((叶子分类C5))
    B2---| |C6((叶子分类C6))
    C1---| |分类成员D1
    C1---| |分类成员D2
    C2---| |分类成员D3
    C2---| |分类成员D4
    C3---| |分类成员D5
    C3---| |分类成员D6
    C4---| |分类成员D7
    C4---| |分类成员D8
    C5---| |分类成员D9
    C5---| |分类成员D10
    C6---| |分类成员D11
    C6---| |分类成员D12
    style A1 fill:#f9f
    end
```

### 3.3.3 获取子分类列表
**接口名称**

`OperationRequest#getSubcategoryList(String parentCode, Map<String, String> extInfo, HttpCallback<List<LeafCategory>> callback)`

重载接口

`OperationRequest#getSubcategoryList(String parentCode, HttpCallback<List<LeafCategory>> callback)`

**接口说明**

根据父分类code获取直接子分类信息

**参数说明**

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
| parentCode |String|是|需要获取子分类的code|
|callback|HttpCallback|否|结果回调，成功返回[LeafCategory](#9.6.1 LeafCategory)对象List集合|

**代码示例**

```java
new OperationRequest().getSubcategoryList(mParentCode, null, new HttpCallback<List<LeafCategory>>() {
            @Override
            public void onSuccess(List<LeafCategory> leafCategories) {
                if (!ListUtil.isEmpty(leafCategories)) {
                    mCategoryAdapter.setDataList(leafCategories);
                    showToast("显示子分类");
                    setTitle("子分类列表");
                }else {
                    showToast(mContentName + "的子分类为空");
                }
            }

            @Override
            public void onError(ApiException exception) {
                showError("获取子分类错误", exception);
            }
        });
```
**接口使用说明**

根据 [分类整体框架图](#分类整体框架图)，使用```getSubcategoryList```接口，返回的数据包含：

* 如果参数parentCode为（A1的Code），那么得到的数据是B1、B2。如图

```mermaid
graph TD
    subgraph 运营分类
    A1((根A1))---| |B1((分类B1))
    A1((根A1))---| |B2((分类B2))
    B1---| |C1((叶子分类C1))
    B1---| |C2((叶子分类C2))
    B1---| |C3((叶子分类C3))
    B2---| |C4((叶子分类C4))
    B2---| |C5((叶子分类C5))
    B2---| |C6((叶子分类C6))
    C1---| |分类成员D1
    C1---| |分类成员D2
    C2---| |分类成员D3
    C2---| |分类成员D4
    C3---| |分类成员D5
    C3---| |分类成员D6
    C4---| |分类成员D7
    C4---| |分类成员D8
    C5---| |分类成员D9
    C5---| |分类成员D10
    C6---| |分类成员D11
    C6---| |分类成员D12
    style B1 fill:#f9f
    style B2 fill:#f9f
    end
```

* 如果参数parentCode为（B1的Code），那么得到的数据为C1、C2、C3。如图；

```mermaid
graph TD
    subgraph 运营分类
    A1((根A1))---| |B1((分类B1))
    A1((根A1))---| |B2((分类B2))
    B1---| |C1((叶子分类C1))
    B1---| |C2((叶子分类C2))
    B1---| |C3((叶子分类C3))
    B2---| |C4((叶子分类C4))
    B2---| |C5((叶子分类C5))
    B2---| |C6((叶子分类C6))
    C1---| |分类成员D1
    C1---| |分类成员D2
    C2---| |分类成员D3
    C2---| |分类成员D4
    C3---| |分类成员D5
    C3---| |分类成员D6
    C4---| |分类成员D7
    C4---| |分类成员D8
    C5---| |分类成员D9
    C5---| |分类成员D10
    C6---| |分类成员D11
    C6---| |分类成员D12
    style C1 fill:#f9f
    style C2 fill:#f9f
    style C3 fill:#f9f
    end
```
* 如果参数parentCode为(C1的Code)，那么得到的数据为空。

### 3.3.4 获取分类成员列表
**接口名称**

分页获取   
`OperationRequest#getCategoryMemberList(String code, int pageNum, int pageSize, HttpCallback<BasePageResult<List<CategoryMember>>> callback)`

**接口说明**

根据分类的code获取分类成员列表，需要分页获取。只有当[Category](#9.6 Category) instanceOf [LeafCategory](#9.6.1 LeafCategory)的才有分类成员数据。

**参数说明**

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
| code |String|是|分类的code|
| pageNum |int|是|请求页码 1，2，3，...|
| pageSize |int|是|每页个数|
|callback|HttpCallback|否|结果回调，成功返回[CategoryMember](#9.7 CategoryMember)对象的List集合|

**代码示例**

```java
new OperationRequest().getCategoryMemberList(mCode, 1, 20, new HttpCallback<List<CategoryMember>>() {
            @Override
            public void onSuccess(List<CategoryMember> categoryMembers) {
                if (categoryMembers != null && !categoryMembers.isEmpty()) {
					mCategoryMemberAdapter.setDataList(categoryMembers);
                } else {
                    showToast("数据为空");
                }
            }

            @Override
            public void onError(ApiException exception) {
                showToast("网络请求错误， 错误码：" + exception.getCode() + ", 错误信息：" + exception.getMessage());
            }
        });
```

**接口使用说明**

根据 [分类整体框架图](#分类整体框架图). 使用```getCategoryMemberList```接口, 返回的数据包含：

* 如果参数code 为（C1的code），那么获取的数据为 D1、D2。如图：

```mermaid
graph TD
    subgraph 运营分类
    A1((根A1))---| |B1((分类B1))
    A1((根A1))---| |B2((分类B2))
    B1---| |C1((叶子分类C1))
    B1---| |C2((叶子分类C2))
    B1---| |C3((叶子分类C3))
    B2---| |C4((叶子分类C4))
    B2---| |C5((叶子分类C5))
    B2---| |C6((叶子分类C6))
    C1---| |分类成员D1
    C1---| |分类成员D2
    C2---| |分类成员D3
    C2---| |分类成员D4
    C3---| |分类成员D5
    C3---| |分类成员D6
    C4---| |分类成员D7
    C4---| |分类成员D8
    C5---| |分类成员D9
    C5---| |分类成员D10
    C6---| |分类成员D11
    C6---| |分类成员D12
    style 分类成员D1 fill:#f9f
    style 分类成员D2 fill:#f9f
    end
```

* 如果参数code 为（C2的code），那么获取的数据为 D3、D4。如图：

```mermaid
graph TD
    subgraph 运营分类
    A1((根A1))---| |B1((分类B1))
    A1((根A1))---| |B2((分类B2))
    B1---| |C1((叶子分类C1))
    B1---| |C2((叶子分类C2))
    B1---| |C3((叶子分类C3))
    B2---| |C4((叶子分类C4))
    B2---| |C5((叶子分类C5))
    B2---| |C6((叶子分类C6))
    C1---| |分类成员D1
    C1---| |分类成员D2
    C2---| |分类成员D3
    C2---| |分类成员D4
    C3---| |分类成员D5
    C3---| |分类成员D6
    C4---| |分类成员D7
    C4---| |分类成员D8
    C5---| |分类成员D9
    C5---| |分类成员D10
    C6---| |分类成员D11
    C6---| |分类成员D12
    style 分类成员D3 fill:#f9f
    style 分类成员D4 fill:#f9f
    end
```


### 3.3.5 获取分类成员数量
**接口名称**

根据分类编码获取分类成员数量

`OperationRequest#getCategoryMemberNum(String code, Map<String, String> extInfo, HttpCallback<Integer> callback)`

重载接口

`OperationRequest#getCategoryMemberNum(String code, HttpCallback<Integer> callback)`

**接口说明**

根据分类的code获取分类成员数量。

**参数说明**

| 参数名   | 类型         | 是否必须 | 描述                           |
| :------- | :----------- | :------- | :----------------------------- |
| code     | String       | 是       | 分类的code                     |
| callback | HttpCallback | 否       | 结果回调，成功返回分类成员个数 |

**代码示例**

```java
operationRequest.getCategoryMemberNum(mCode, new HttpCallback<Integer>() {
            @Override
            public void onSuccess(Integer integer) {
                mTvCategoryMemberNum.setText(integer + "个分类成员");
            }

            @Override
            public void onError(ApiException exception) {
                showError("获取分类成员个数错误", exception);
            }
        });
```

**接口使用说明**

根据 [分类整体框架图](#分类整体框架图)，使用```getCategoryMemberNum```接口，返回的数据包含：

* 如果参数code为（C1的code），返回值为2。如图：

```mermaid
graph TD
    subgraph 运营分类
    A1((根A1))---| |B1((分类B1))
    A1((根A1))---| |B2((分类B2))
    B1---| |C1((叶子分类C1))
    B1---| |C2((叶子分类C2))
    B1---| |C3((叶子分类C3))
    B2---| |C4((叶子分类C4))
    B2---| |C5((叶子分类C5))
    B2---| |C6((叶子分类C6))
    C1---| |分类成员D1
    C1---| |分类成员D2
    C2---| |分类成员D3
    C2---| |分类成员D4
    C3---| |分类成员D5
    C3---| |分类成员D6
    C4---| |分类成员D7
    C4---| |分类成员D8
    C5---| |分类成员D9
    C5---| |分类成员D10
    C6---| |分类成员D11
    C6---| |分类成员D12
    style 分类成员D1 fill:#f9f
    style 分类成员D2 fill:#f9f
    end
```

## 3.4 运营栏目
栏目有别于分类，是不同种类内容类型内容的多级树形组织结构，包括内容类型及内容推出方式两个维度。
1.	内容类型层面：栏目支持多种内容类型混编，包括专辑及其分类（如小说，音乐，儿童，娱乐，文史，汽车，情感，曲艺，健康等等）、AI电台、在线广播、听电视等；
2.	内容推出方式层面：栏目支持场景推荐、个性化内容推荐、分类推荐、本地在线广播推荐、人工运营栏目等多种内容推出方式。
所在类`OperationRequest`。
* #### 栏目整体框架图
```mermaid
graph TD
    subgraph 运营栏目
    A1((根栏目组A1))---| |B1((栏目组B1))
    A1((根栏目组A1))---| |B2((栏目组B2))
    B1---| |C1((栏目C1))
    B1---| |C2((栏目C2))
    B1---| |C3((栏目C3))
    B2---| |C4((栏目C4))
    B2---| |C5((栏目C5))
    B2---| |C6((栏目C6))
    C1---| |成员专辑D1
    C1---| |成员AI电台D2
    C2---| |成员在线广播D3
    C2---| |成员专辑D4
    C3---| |成员在线广播D5
    C3---| |成员AI电台D6
    C4---| |成员在线广播D7
    C4---| |成员在线广播D8
    C5---| |成员专辑D9
    C5---| |成员专辑D10
    C6---| |成员AI电台D11
    C6---| |成员AI电台D12
    end
```
### 3.4.1 获取整棵栏目树
**接口名称**

获取整个栏目树，包括根栏目组、栏目组和栏目
`OperationRequest#getColumnTree(boolean isWithMembers, String zone, Map<String, String> extInfo,
HttpCallback<List<ColumnGrp>> callback)`

重载接口

`OperationRequest#getColumnTree(boolean isWithMembers, String zone, HttpCallback<List<ColumnGrp>> callback)`

重载接口

`OperationRequest#getColumnTree(boolean isWithMembers, HttpCallback<List<ColumnGrp>> callback)`

重载接口，不包含栏目成员

`OperationRequest#getColumnTree(HttpCallback<List<ColumnGrp>> callback)`

**接口说明**

获取整个栏目树，包括根栏目组、栏目和栏目成员。根据参数`isWithMembers`是否获取栏目成员。

**参数说明**

| 参数名        | 类型                      | 是否必须 | 描述                                              |
| :------------ | :------------------------ | :------- |:------------------------------------------------|
| isWithMembers | Boolean                   | 是       | 是否要成员(false表示不要成员)，不填表示不获取成员                    |
| zone          | String                    | 是       | 运营区域，默认传入mainPage；传入Publicservices，是公共服务节目      |
| extraInfo     | Map&lt;String, String&gt; | 否       | 额外信息，暂时无用                                       |
| callback      | HttpCallback              | 否       | 结果回调，成功返回栏目组[ColumnGrp](#9.3 ColumnGrp)的List集合。 |

**代码示例**

```java
//获取栏目组的树结构，包括所有根栏目和子栏目
boolean isWithMember = mSwitchWithMember.isChecked();
mOperationRequest.getColumnTree(isWithMember, zone, new HttpCallback<List<ColumnGrp>>(){
            @Override
            public void onSuccess(List<ColumnGrp> columnGrps) {
                setTitle("根栏目组列表");
                if (columnGrps != null && columnGrps.size() > 0) {
                    mColumnGrpAdapter.setDataList(columnGrps);
                } else {
                    showToast("栏目数为空");
                }
            }

            @Override
            public void onError(ApiException exception) {
                showToast("网络请求错误，错误码：" + exception.getCode() + ", 错误信息：" + exception.getMessage());

            }
        });
```

**接口使用说明**

根据 [栏目整体框架图](#栏目整体框架图)，使用```getColumnTree```接口，返回的数据包含：

* 如果传入的isWithMembers为false，那么获取到的数据为：A1、B1、B2、C1、C2、C3、C4、C5、C6。如图：

```mermaid
graph TD
    subgraph 运营栏目
    A1((根栏目组A1))---| |B1((栏目组B1))
    A1((根栏目组A1))---| |B2((栏目组B2))
    B1---| |C1((栏目C1))
    B1---| |C2((栏目C2))
    B1---| |C3((栏目C3))
    B2---| |C4((栏目C4))
    B2---| |C5((栏目C5))
    B2---| |C6((栏目C6))
    C1---| |成员专辑D1
    C1---| |成员AI电台D2
    C2---| |成员在线广播D3
    C2---| |成员专辑D4
    C3---| |成员在线广播D5
    C3---| |成员AI电台D6
    C4---| |成员在线广播D7
    C4---| |成员在线广播D8
    C5---| |成员专辑D9
    C5---| |成员专辑D10
    C6---| |成员AI电台D11
    C6---| |成员AI电台D12
    style A1 fill:#f9f
    style B1 fill:#f9f
    style B2 fill:#f9f
    style C1 fill:#f9f
    style C2 fill:#f9f
    style C3 fill:#f9f
    style C4 fill:#f9f
    style C5 fill:#f9f
    style C6 fill:#f9f
    end
```

* 如果传入的isWithMembers为true, 那么获取到的数据为： A1、B1、B2、C1、C2、C3、C4、C5、C6、D1、D2、D3、D4、D5、D6、D7、D8、D9、D10、D11、D12。

```mermaid
graph TD
    subgraph 运营栏目
    A1((根栏目组A1))---| |B1((栏目组B1))
    A1((根栏目组A1))---| |B2((栏目组B2))
    B1---| |C1((栏目C1))
    B1---| |C2((栏目C2))
    B1---| |C3((栏目C3))
    B2---| |C4((栏目C4))
    B2---| |C5((栏目C5))
    B2---| |C6((栏目C6))
    C1---| |成员专辑D1
    C1---| |成员AI电台D2
    C2---| |成员在线广播D3
    C2---| |成员专辑D4
    C3---| |成员在线广播D5
    C3---| |成员AI电台D6
    C4---| |成员在线广播D7
    C4---| |成员在线广播D8
    C5---| |成员专辑D9
    C5---| |成员专辑D10
    C6---| |成员AI电台D11
    C6---| |成员AI电台D12
    style A1 fill:#f9f
    style B1 fill:#f9f
    style B2 fill:#f9f
    style C1 fill:#f9f
    style C2 fill:#f9f
    style C3 fill:#f9f
    style C4 fill:#f9f
    style C5 fill:#f9f
    style C6 fill:#f9f
    style 成员专辑D1 fill:#f9f
    style 成员AI电台D2 fill:#f9f
    style 成员在线广播D3 fill:#f9f
    style 成员专辑D4 fill:#f9f
    style 成员在线广播D5 fill:#f9f
    style 成员AI电台D6 fill:#f9f
    style 成员在线广播D7 fill:#f9f
    style 成员在线广播D8 fill:#f9f
    style 成员专辑D9 fill:#f9f
    style 成员专辑D10 fill:#f9f
    style 成员AI电台D11 fill:#f9f
    style 成员AI电台D12 fill:#f9f
    end
```

### 3.4.2 获取子栏目列表
**接口名称**

根据父栏目编码获取下级栏目列表
`OperationRequest#getSubcolumnList(String parentCode, String zone, Map<String, String> extInfo,
HttpCallback<List<Column>> callback)`

重载接口

`OperationRequest#getSubcolumnList(String parentCode, String zone,HttpCallback<List<Column>> callback)`

重载接口

`OperationRequest#getSubcolumnList(String parentCode, HttpCallback<List<Column>> callback)`

**接口说明**

根据父栏目编码获取下级栏目列表。

**参数说明**

| 参数名     | 类型                      | 是否必须 | 描述                                              |
| :--------- | :------------------------ | :------- | :------------------------------------------------ |
| parentCode | String                    | 是       | 父栏目编码。查根栏目列表，parentCode传0                                        |
| zone       | String                    | 是       | 运营区域，默认传入mainPage                                                                        |
| extraInfo  | Map&lt;String, String&gt; | 否       | 额外信息，暂时无用                                |
| callback   | HttpCallback              | 否       | 结果回调，成功栏目[Column](#9.4 Column)的List集合。 |

**代码示例**

```java
mOperationRequest.getSubcolumnList(mCode, zone, new HttpCallback<List<Column>>() {
                    @Override
                    public void onSuccess(List<Column> columns) {
                        setTitle("子栏目列表");
                        if (!ListUtil.isEmpty(columns)) {
                            mColumnGrpAdapter.setDataList(columns);
                        } else {
                            showToast("子栏目为空");
                        }
                    }

                    @Override
                    public void onError(ApiException exception) {
                        showError("获取子栏目错误", exception);
                    }
                });
```

**接口使用说明**

根据 [栏目整体框架图](#栏目整体框架图)，使用```getSubcolumnList```接口，返回的数据包含：

*  如果参数parentCode为（A1的Code），那么得到的数据是B1、B2。

```mermaid
graph TD
    subgraph 运营栏目
    A1((根栏目组A1))---| |B1((栏目组B1))
    A1((根栏目组A1))---| |B2((栏目组B2))
    B1---| |C1((栏目C1))
    B1---| |C2((栏目C2))
    B1---| |C3((栏目C3))
    B2---| |C4((栏目C4))
    B2---| |C5((栏目C5))
    B2---| |C6((栏目C6))
    C1---| |成员专辑D1
    C1---| |成员AI电台D2
    C2---| |成员在线广播D3
    C2---| |成员专辑D4
    C3---| |成员在线广播D5
    C3---| |成员AI电台D6
    C4---| |成员在线广播D7
    C4---| |成员在线广播D8
    C5---| |成员专辑D9
    C5---| |成员专辑D10
    C6---| |成员AI电台D11
    C6---| |成员AI电台D12
    style B1 fill:#f9f
    style B2 fill:#f9f
    end
```

*  如果参数parentCode为（B1的Code），那么得到的数据为C1、C2、C3。

```mermaid
graph TD
    subgraph 运营栏目
    A1((根栏目组A1))---| |B1((栏目组B1))
    A1((根栏目组A1))---| |B2((栏目组B2))
    B1---| |C1((栏目C1))
    B1---| |C2((栏目C2))
    B1---| |C3((栏目C3))
    B2---| |C4((栏目C4))
    B2---| |C5((栏目C5))
    B2---| |C6((栏目C6))
    C1---| |成员专辑D1
    C1---| |成员AI电台D2
    C2---| |成员在线广播D3
    C2---| |成员专辑D4
    C3---| |成员在线广播D5
    C3---| |成员AI电台D6
    C4---| |成员在线广播D7
    C4---| |成员在线广播D8
    C5---| |成员专辑D9
    C5---| |成员专辑D10
    C6---| |成员AI电台D11
    C6---| |成员AI电台D12
    style C1 fill:#f9f
    style C2 fill:#f9f
    style C3 fill:#f9f
    end
```

*  如果参数parentCode为（C1的Code），那么得到的数据为空。

### 3.4.3 获取栏目成员列表
**接口名称**

根据栏目编码获取栏目成员列表

`OperationRequest#getColumnMemberList(String code, Map<String, String> extInfo,
HttpCallback<List<ColumnMember>> callback)`

重载接口

`OperationRequest#getColumnMemberList(String code, HttpCallback<List<ColumnMember>> callback)`

**接口说明**

根据栏目编码获取栏目成员列表。

**参数说明**

| 参数名    | 类型                      | 是否必须 | 描述                                                         |
| :-------- | :------------------------ | :------- | :----------------------------------------------------------- |
| code      | String                    | 是       | 栏目编码                                                     |
| extraInfo | Map&lt;String, String&gt; | 否       | 额外信息，暂时无用                                           |
| callback  | HttpCallback              | 否       | 结果回调，成功栏目成员[ColumnMember](#9.5 ColumnMember)的List集合。 |

**代码示例**

```java
mOperationRequest.getColumnMemberList(mCode, new HttpCallback<List<ColumnMember>>() {
                    @Override
                    public void onSuccess(List<ColumnMember> columnMembers) {
                        setTitle("栏目成员列表");
                        if (!ListUtil.isEmpty(columnMembers)) {
                            mColumnGrpAdapter.setDataList(columnMembers);
                        } else {
                            showToast("栏目成员为空");
                        }
                    }

                    @Override
                    public void onError(ApiException exception) {
                        showError("获取栏目成员错误", exception);
                    }
                });
```

**接口使用说明**

根据 [栏目整体框架图](#栏目整体框架图)，使用```getColumnMemberList```接口，返回的数据包含：
* 如果参数code 为（C1的code），那么获取的数据为 D1、D2。如图：

```mermaid
graph TD
    subgraph 运营栏目
    A1((根栏目组A1))---| |B1((栏目组B1))
    A1((根栏目组A1))---| |B2((栏目组B2))
    B1---| |C1((栏目C1))
    B1---| |C2((栏目C2))
    B1---| |C3((栏目C3))
    B2---| |C4((栏目C4))
    B2---| |C5((栏目C5))
    B2---| |C6((栏目C6))
    C1---| |成员专辑D1
    C1---| |成员AI电台D2
    C2---| |成员在线广播D3
    C2---| |成员专辑D4
    C3---| |成员在线广播D5
    C3---| |成员AI电台D6
    C4---| |成员在线广播D7
    C4---| |成员在线广播D8
    C5---| |成员专辑D9
    C5---| |成员专辑D10
    C6---| |成员AI电台D11
    C6---| |成员AI电台D12
    style 成员专辑D1 fill:#f9f
    style 成员AI电台D2 fill:#f9f
    end
```

* 如果参数code 为（C2的code），那么获取的数据为 D3、D4。如图：

```mermaid
graph TD
    subgraph 运营栏目
    A1((根栏目组A1))---| |B1((栏目组B1))
    A1((根栏目组A1))---| |B2((栏目组B2))
    B1---| |C1((栏目C1))
    B1---| |C2((栏目C2))
    B1---| |C3((栏目C3))
    B2---| |C4((栏目C4))
    B2---| |C5((栏目C5))
    B2---| |C6((栏目C6))
    C1---| |成员专辑D1
    C1---| |成员AI电台D2
    C2---| |成员在线广播D3
    C2---| |成员专辑D4
    C3---| |成员在线广播D5
    C3---| |成员AI电台D6
    C4---| |成员在线广播D7
    C4---| |成员在线广播D8
    C5---| |成员专辑D9
    C5---| |成员专辑D10
    C6---| |成员AI电台D11
    C6---| |成员AI电台D12
    style 成员在线广播D3 fill:#f9f
    style 成员专辑D4 fill:#f9f
    end
```

## 3.5 搜索
云听具备强大的语音搜索能力。用户可通过标题、描述、关键字、标签、分类等所有相关信息，全面的搜索专辑、单曲、AI电台、在线广播、听电视等各种内容类型，并根据多种因素排序返回结果。
所在类`SearchRequest`。

### 3.5.1 语音搜索

**接口名称**

`SearchRequest#searchBySemantics(String voiceSource,
​            int qualityType,
​            String origJson,
​            int field,
​            int tag,
​            String artist,
​            String audioName,
​            String albumName,
​            String category,
​            String keyword,
​            String text,
​            String language,
​            String freq,
​            String area,
​            HttpCallback<VoiceSearchResult> callback)
`

重载接口，去除暂不支持的参数

`
SearchRequest#searchBySemantics(String voiceSource,
​            int qualityType,
​            String origJson,
​            int field,
​            int tag,
​            String artist,
​            String audioName,
​            String albumName,
​            String category,
​            String keyword,
​            String text,
​            HttpCallback<VoiceSearchResult> callback)
`

**接口说明**

根据语音商解析后的json串可以获取不同类型的音频资源。

**参数说明**

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
|voiceSource|String|是|语音来源,语音提供商的缩写，用于特殊处理。默认为0。<br>同行者：txzing；思必驰：sibichi；问问：wenwen；<br>蓦然：moran；科大讯飞：kedaxunfei；<br>其它：other|
|qualityType |int|否|音频质量要求，暂不支持，默认传0|
|origJson |String|否|语音商解析后返回的原始json串|
|field |int|是|场景类别 1：音乐，2：综合，6： 在线广播|
|tag |int|否|参数可信标识，暂不支持，默认传0|
|artist |String|否|艺术家，暂不支持|
|audioName|String|否|单曲名称|
|albumName|String|否|专辑名称|
| category |String|否|分类|
|keyword|String|是|关键词 多个关键词以英文逗号“,”分隔|
|text|String|是|用户声控的原始串|
|language|String|否|语言，暂不支持|
|freq|String|否|电台频率，在线广播场景下使用，暂不支持|
|area|String|否|搜索text中的地点，在线广播场景下使用，暂不支持|
|callback|HttpCallback|否|结果回调，成功返回[VoiceSearchResult](#9.9 VoiceSearchResult)对象|

**代码示例**

```java
String mVoiceSource = "kedaxunfei";
int mVoiceQuality = 1;
String mOriginJson = "
{
    "save_history":true,
    "rc":3,
    "semantic":[
        {
            "intent":"PLAY",
            "slots":[
                {
                    "name":"lang",
                    "value":"粤语"
                }
            ]
        }
    ],
    "service":"musicX",
    "uuid":"atn0210245b@dx00070ecedaf7a11001",
    "text":"我要听粤语",
    "state":{
        "fg::musicX::default::default":{
            "lang":"1",
            "state":"default"
        }
    },
    "used_state":{
        "lang":"1",
        "state_key":"fg::musicX::default::default",
        "state":"default"
    },
    "answer":{
        "text":"可能刚刚开小差了，我去瞧一瞧"
    },
    "dialog_stat":"dataInvalid",
    "sid":"atn0210245b@dx00070ecedaf7a11001"
}";
int mField  = 1;
int mCredibility = 1;
String mArtist = null;
String mAudioName = null;
String mAlbumName = null;
String mCategory = "音乐";
String mKeyword = "粤语";
String mVoiceText = "我要听粤语";

new SearchRequest().searchBySemantics(mVoiceSource, mVoiceQuality, mOriginJson, mField,
                                mCredibility, mArtist, mAudioName, mAlbumName, mCategory, mKeyword, mVoiceText, null,
                                null, null,
                                new HttpCallback<VoiceSearchResult>() {
                                    @Override
                                    public void onSuccess(VoiceSearchResult voiceSearchResult) {
                                        List<SearchProgramBean> programList = voiceSearchResult.getProgramList();
                                        if (programList != null && programList.size() > 0) {
                                            mSearchResultNewAdapter.setDataList(programList);
                                        } else {
                                            showToast("数据为空");
                                        }
                                    }

                                    @Override
                                    public void onError(ApiException exception) {
                                        showToast("网络请求错误，错误码=" + exception.getCode() + ", 错误信息=" + exception
                                                .getMessage());
                                    }
                                });
```

### 3.5.2 关键词搜索
**接口名称**

`SearchRequest#searchAll(String keyword, HttpCallback<List<SearchProgramBean>> callback)`

**接口说明**

根据关键词搜索所有资源。

**参数说明**

| 参数名   | 类型| 是否必须 | 描述                                                     |
| :----| :---- | :------- | :---- |
| keyword  | String       | 是       | 关键词                                                   |
| callback | HttpCallback | 否       | 结果回调，成功返回[SearchProgramBean](#9.10 SearchProgramBean)对象List列表 |

**代码示例**

```java
new SearchRequest().searchAll(keyword, new HttpCallback<List<SearchProgramBean>>() {
            @Override
            public void onSuccess(List<SearchProgramBean> searchProgramBeans) {
                mRvSearchSuggestedWord.setAdapter(mSearchAdapter);
                mSearchAdapter.setDataList(searchProgramBeans);
            }

            @Override
            public void onError(ApiException exception) {
                showError("搜索所有资源错误", exception);
            }
        });
```
### 3.5.3 指定内容类型搜索
**接口名称**

`SearchRequest#searchByType(String keyword, int resType, int pageNum, int pageSize, HttpCallback<BasePageResult<List<SearchProgramBean>>> callback)`

**接口说明**

根据关键词分页搜索指定内容类型资源。

**参数说明**

| 参数名   | 类型| 是否必须 | 描述                                                                                                                                                                                                      |
| :----| :---- | :------- |:--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| keyword  | String       | 是       | 关键词                                                                                                                                                                                                     |
| resType  | int       | 是       | 要搜索的内容类型[ResType](#9.31 ResType)，<br>综合-`ResType.TYPE_ALL`<br>专辑-`ResType.TYPE_ALBUM`<br>AI电台-`ResType.TYPE_RADIO`<br>在线广播-`ResType.TYPE_BROADCAST`<br>单曲-`ResType.TYPE_AUDIO`<br>听电视-`ResType.TYPE_TV` |
| pageNum  | int       | 否       | 页码，默认1                                                                                                                                                                                                  |
| pageSize | int       | 否       | 每页个数，默认10                                                                                                                                                                                               |
| callback | HttpCallback | 否       | 结果回调，成功返回[SearchProgramBean](#9.10 SearchProgramBean)对象List列表                                                                                                                                           |

**代码示例**

```java
new SearchRequest().searchByType(keyword, mResourceType, 1, 20,
    new HttpCallback<BasePageResult<List<SearchProgramBean>>>() {
    @Override
    public void onSuccess(BasePageResult<List<SearchProgramBean>> basePageResult) {
        mRvSearchSuggestedWord.setAdapter(mSearchAdapter);
        mSearchAdapter.setDataList(basePageResult.getDataList());
    }
        @Override
        public void onError(ApiException exception) {
        showError("指定类型搜索错误", exception);
        }
});
```

### 3.5.4 获取联想词
**接口名称**

`SearchRequest#getSuggestedWords(String word, HttpCallback<List<String>> callback)`

**接口说明**

根据给定的词获取联想词。

**参数说明**

| 参数名   | 类型         | 是否必须 | 描述                               |
| :------- | :----------- | :------- | :--------|
| word     | String       | 是       | 关键词                             |
| callback | HttpCallback | 否       | 结果回调，成功返回联想词的List列表 |

**代码示例**

```java
new SearchRequest().getSuggestedWords(keyword, new HttpCallback<List<String>>() {
    @Override
    public void onSuccess(List<String> strings) {
    	mRvSearchSuggestedWord.setAdapter(mKeywordAdapter);
      if (mKeywordAdapter != null) {
      	mKeywordAdapter.setDataList(strings);
      }
    }
		@Override
		public void onError(ApiException exception) {
			showError("搜索联想词错误", exception);
		}
});
```

### 3.5.5 获取热词
**接口名称**

`SearchRequest#getHotWords(HttpCallback<List<String>> callback)`

**接口说明**

获取热门搜索词列表

**参数说明**

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
|callback|HttpCallback|否|结果回调，成功返回String列表|

**代码示例**

```java
new SearchRequest().getHotWords(new HttpCallback<List<String>>() {
            @Override
            public void onSuccess(List<String> strings) {
                mRvSearchSuggestedWord.setAdapter(mKeywordAdapter);
                if (mKeywordAdapter != null) {
                    mKeywordAdapter.setDataList(strings);
                }
            }

            @Override
            public void onError(ApiException exception) {

            }
        });
```

## 3.6 专辑
包括获取专辑详情和专辑的单曲列表功能。专辑相关请求所在类`AlbumRequest`。

### 3.6.1 获取专辑详情
**接口名称**

获取单个专辑详情

`AlbumRequest#getAlbumDetails(long albumId, HttpCallback<AlbumDetails> callback)`

一次获取多个专辑详情

`AlbumRequest#getAlbumDetails(long[] albumIds, HttpCallback<List<AlbumDetails>> callback)`

**接口说明**

根据专辑Id获取专辑的信息详情。

**参数说明**

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
|albumId|long|是|专辑Id|
| albumIds|Long[]|是|专辑id数组|
|callback|HttpCallback|否|结果回调，成功返回[AlbumDetails](#9.11 AlbumDetails)对象或列表|

**代码示例**

```java
//获取单个专辑详情
new AlbumRequest().getAlbumDetails(albumId, new HttpCallback<AlbumDetails>() {
            @Override
            public void onSuccess(AlbumDetails result) {
                tvDetails.setText(mGson.toJson(result));
                Glide.with(DetailActivity.this).load(result.getImg()).into(ivCover);
            }

            @Override
            public void onError(ApiException exception) {
                tvDetails.setText(mGson.toJson(exception));
            }
        });
//一次获取多个专辑详情
new AlbumRequest().getAlbumDetails(new Long[]{1100000000078L, 1100000000416L}, new HttpCallback<List<AlbumDetails>>() {
            @Override
            public void onSuccess(List<AlbumDetails> result) {
                tvDetails.setText(mGson.toJson(result));
            }

            @Override
            public void onError(ApiException exception) {
                tvDetails.setText(mGson.toJson(exception));
            }
        });
```

### 3.6.2 获取专辑的单曲列表
**接口名称**

`AlbumRequest#getPlaylist(long albumId, long audioId, @Sort int sort, int pageSize, int pageNum,
​            HttpCallback<BasePageResult<List<AudioDetails>>> callback)`
重载方法。单曲id默认为0
`AlbumRequest#getPlaylist(long albumId, @Sort int sort, int pageSize, int pageNum,
​            HttpCallback<BasePageResult<List<AudioDetails>>> callback) `


**接口说明**

根据专辑id获取专辑的单曲列表，分页请求；可以根据期数倒序或者正序，也可以根据单曲的id直接返回该单曲所在页的单曲列表。

**参数说明**

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
| albumId |long|是|专辑id|
| audioId |long|否|单曲id，如果传入单曲id，接口将会返回此单曲id所在页数据；不与pageNum一起使用，如都传了值优先按audioId查询|
| sort |int|否|1正序 -1 倒序|
| pageSize |int|否|每页专辑个数，默认是10|
| pageNum |int|否|请求页码 1, 2, 3...，默认是1|
|callback|HttpCallback|否|结果回调，成功返回[AudioDetails](#9.13 AudioDetails)列表|

**代码示例**

```java
new AlbumRequest().getPlaylist(id, AlbumRequest.SORT_ACS, 20, 1, new HttpCallback<BasePageResult<List<AudioDetails>>>() {
            @Override
            public void onSuccess(BasePageResult<List<AudioDetails>> result) {
                mAdapter.replaceData(result);
            }

            @Override
            public void onError(ApiException exception) {
                tvDetails.setText(mGson.toJson(exception));
            }
        });
```

## 3.7 AI电台
接口所在类`RadioRequest`

### 3.7.1 获取AI电台详情
**接口名称**

`RadioRequest#getRadioDetails(long radioId, HttpCallback<RadioDetails> callback)`

**接口说明**

根据AI电台的id获取信息详情。

**参数说明**

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
| radioId |long|是|AI电台Id|
|callback|HttpCallback|否|结果回调，成功返回[RadioDetails](#9.12 RadioDetails)对象|

**代码示例**

```java
new RadioRequest().getRadioDetails(id, new HttpCallback<RadioDetails>() {
            @Override
            public void onSuccess(RadioDetails result) {
                tvDetails.setText(mGson.toJson(result));
                Glide.with(DetailActivity.this).load(result.getImg()).into(ivCover);
            }

            @Override
            public void onError(ApiException exception) {
                tvDetails.setText(mGson.toJson(exception));
            }
        });
```

### 3.7.2 获取AI电台单曲列表
**接口名称**

`RadioRequest#getPlaylist(long radioId, String clockId, HttpCallback<List<AudioDetails>> callback)`

**接口说明**

根据AI电台id获取单曲列表

**参数说明**

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
| radioId |long|是|AI电台id|
| clockId |String|是|分页参数。请求第一页播单时传0或者为空，请求下一页播单时传1，只能传0或1或者为空|
|callback|HttpCallback|否|结果回调，成功返回[AudioDetails](#9.13 AudioDetails)集合|

**代码示例**

```java
String clockId = "";
new RadioRequest().getPlaylist(radioId, clockId, new HttpCallback<List<AudioDetails>>() {
            @Override
            public void onSuccess(List<AudioDetails> result) {
                mAdapter.replaceData(result);
            }

            @Override
            public void onError(ApiException exception) {
                tvDetails.setText(mGson.toJson(exception));
            }
        });
```

### 3.7.3 根据城市名称或编码获取AI电台单曲列表
**接口名称**

`RadioRequest#getPlaylist(long radioId, String clockId, String areaCode, String cityName,
​            HttpCallback<List<AudioDetails>> callback)`

**接口说明**

根据城市的名称或城市地区编码，见[城市编码表](#11.1 城市编码表)，获取AI电台的单曲列表。

**参数说明**

|类型|参数名|是否必须|描述|
|:----|:---|:----|:----|
|long|radioId|是|AI电台Id|
|String|clockId|是|分页参数。请求第一页播单时传0或者为空，请求下一页播单时传1，只能传0或1或者为空|
|String|areaCode|否|城市编码|
|String|cityName|否|城市名称|
|HttpCallback&lt;List&lt;AudioDetails&gt;&gt;|callback|否|接口回调，成功返回单曲[AudioDetails](#9.13 AudioDetails)集合|

**代码示例**

```java
new RadioRequest().getPlaylist(radioId, clockId, null, "北京市",new HttpCallback<List<AudioDetails>>() {
            @Override
            public void onSuccess(List<AudioDetails> result) {
                List<StringAdapter.Item> datas = new ArrayList<>();

                if (result != null) {
                    List<AudioDetails> dataList = result;
                    if (dataList != null) {
                        for (int i = 0; i < dataList.size(); i++) {
                            AudioDetails item = dataList.get(i);

                            StringAdapter.Item sai = new StringAdapter.Item();
                            sai.id = item.getAudioId();
                            sai.type = TYPE_AUDIO;
                            sai.title = item.getAudioName();
                            sai.details = item.getAlbumName();//mGson.toJson(item);

                            datas.add(sai);
                        }
                    }
                }

                if (datas.isEmpty()) {
                    Toast.makeText(DetailActivity.this, "列表为空", Toast.LENGTH_SHORT).show();
                }

                mAdapter.replaceData(datas);
            }

            @Override
            public void onError(ApiException exception) {
                tvDetails.setText(mGson.toJson(exception));
            }
        });
```

## 3.8 单曲
单曲相关接口所在类`AudioRequest`。

### 3.8.1 获取单曲详情
**接口名称**

获取单个单曲的详情

`AudioRequest#getAudioDetails(long audioId, HttpCallback<AudioDetails> callback)`

一次获取多个单曲的详情

`AudioRequest#getAudioDetails(Long[] audioIds, HttpCallback<List<AudioDetails>> callback)`

**接口说明**

获取一个或多个单曲的信息详情。

**参数说明**

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
|audioId|long|是|单曲id|
|audioIds|Long[]|是|单曲id的Long数组|
|callback|HttpCallback|否|结果回调，成功返回[AudioDetails](#9.13 AudioDetails)对象或列表|

**代码示例**

```java
//获取单个单曲详情
new AudioRequest().getAudioDetails(audioId, new HttpCallback<AudioDetails>() {
            @Override
            public void onSuccess(AudioDetails result) {
                tvDetails.setText(mGson.toJson(result));
                Glide.with(DetailActivity.this).load(result.getAudioPic()).into(ivCover);
            }

            @Override
            public void onError(ApiException exception) {
                tvDetails.setText(mGson.toJson(exception));
            }
        });
//获取多个单曲详情
Long[] audioIds = new Long[]{1000000394424L, 1000000394424L};
new AudioRequest().getAudioDetails(audioIds, new HttpCallback<List<AudioDetails>>() {
            @Override
            public void onSuccess(List<AudioDetails> audioDetails) {
                tvDetails.setText(mGson.toJson(audioDetails));
            }

            @Override
            public void onError(ApiException exception) {
                tvDetails.setText(mGson.toJson(exception));
            }
        });
```

### 3.8.2 获取报时单曲
**接口名称**

`AudioRequest#getCurrentClockAudio(HttpCallback<AudioDetails> callback)`

**接口说明**

获取当前时间点的报时声音单曲。

**参数说明**

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
|callback|HttpCallback|否|结果回调，成功返回[AudioDetails](#9.13 AudioDetails)对象|

**代码示例**

```java
new AudioRequest().getCurrentClockAudio(new HttpCallback<AudioDetails>() {
            @Override
            public void onSuccess(AudioDetails audioDetails) {
                Log.e("DetailActivity", "onSuccess: "+audioDetails);
            }

            @Override
            public void onError(ApiException exception) {

            }
        });
```

### 3.8.3 根据单曲播放id请求播放的url地址
结合[3.16 音质相关](#3.16 音质相关)可以实现音质选择功能。==注意==：使用SDK播放器，可以忽略此接口。
**接口名称**

`AudioRequest#getAudioPlayInfo(String playUrlId, HttpCallback<AudioPlayInfo> callback)`

**接口说明**

根据单曲播放id请求播放的url地址，付费专辑获取加密url。

**参数说明**

|参数名|类型|是否必须| 描述                                                                                                  |
|:---|:---|:---|:----------------------------------------------------------------------------------------------------|
| playUrlId |String|是| 单曲播放id，[AudioDetails](#9.13 AudioDetails)中的playUrlId字段，接口[3.6.2 获取专辑的单曲列表](#3.6.2 获取专辑的单曲列表)和接口[3.8.1 获取单曲详情](#3.8.1 获取单曲详情)可以获取此对象 |
|callback|HttpCallback|否| 结果回调，成功返回[AudioPlayInfo](#9.47 AudioPlayInfo)                                                       |

**代码示例**

```java
new AudioRequest().getAudioPlayInfo(audioDetails.getPlayUrlId(), new HttpCallback<AudioPlayInfo>() {
            @Override
            public void onSuccess(AudioPlayInfo audioPlayInfo) {
                List<AudioFileInfo> playList = audioPlayInfo.getPlayInfoList();
                if (playList == null) {
                    onError(new ApiException("playList is null"));
                    return;
                }
                for (AudioFileInfo info : playList) {
                    String fileType = info.getFileType();
                    if (fileType.startsWith("mp3")) {
                        switch (info.getBitrate()) {
                            case 32:
                                playUrlData.setMp3PlayUrl32(info.getPlayUrl());
                                break;
                            case 64:
                                playUrlData.setMp3PlayUrl64(info.getPlayUrl());
                                break;
                            case 128:
                                playUrlData.setMp3PlayUrl128(info.getPlayUrl());
                                break;
                            case 320:
                                playUrlData.setMp3PlayUrl320(info.getPlayUrl());
                                break;
                        }
                    } else if (fileType.startsWith("aac")) {
                        switch (info.getBitrate()) {
                            case 32:
                                playUrlData.setAacPlayUrl32(info.getPlayUrl());
                                break;
                            case 64:
                                playUrlData.setAacPlayUrl64(info.getPlayUrl());
                                break;
                            case 128:
                                playUrlData.setAacPlayUrl128(info.getPlayUrl());
                                break;
                            case 320:
                                playUrlData.setAacPlayUrl320(info.getPlayUrl());
                                break;
                        }
                    }
                }
                //设置并回调播放地址
                setPlayUrl(albumPlayItem, playUrlData);
                callback.onDataGet(albumPlayItem.getPlayUrl());
            }

            @Override
            public void onError(ApiException exception) {
                callback.onDataError(exception);
            }
        });
```

## 3.9 在线广播
在线广播相关的接口，所在类`BroadcastRequest`

### 3.9.1 获取在线广播详情
**接口名称**

`BroadcastRequest#getBroadcastDetails(long broadcastId, HttpCallback<BroadcastDetails> callback)`

**接口说明**

根据在线广播id获取在线广播的信息详情

**参数说明**

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
|broadcastId|long|是|在线广播的id|
|callback|HttpCallback|否|结果回调，成功返回[BroadcastDetails](#9.15 BroadcastDetails)对象|

**代码示例**

```java
new BroadcastRequest().getBroadcastDetails(id, new HttpCallback<BroadcastDetails>() {
            @Override
            public void onSuccess(BroadcastDetails result) {
                tvDetails.setText(mGson.toJson(result));
                Glide.with(DetailActivity.this).load(result.getImg()).into(ivCover);
            }

            @Override
            public void onError(ApiException exception) {
                tvDetails.setText(mGson.toJson(exception));
            }
        });
```

### 3.9.2 获取在线广播节目单列表
**接口名称**

`BroadcastRequest#getBroadcastProgramList(long broadcastId, @Nullable String date, HttpCallback<List<ProgramDetails>> callback)`

**接口说明**

获取指定在线广播的指定日期的节目单列表

**参数说明**

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
| broadcastId |long|是|在线广播id|
| date |String|否|节目单的日期，默认是当天。格式“2018-09-26”|
|callback|HttpCallback|否|结果回调，成功返回[ProgramDetails](#9.16 ProgramDetails)列表|

**代码示例**

```java
String date = "2018-07-26";
new BroadcastRequest().getBroadcastProgramList(id, date, new HttpCallback<List<ProgramDetails>>() {
            @Override
            public void onSuccess(List<ProgramDetails> result) {
                mAdapter.replaceData(result);
            }

            @Override
            public void onError(ApiException exception) {
                tvDetails.setText(mGson.toJson(exception));
            }
        });
```

### 3.9.3 获取在线广播单个节目详情
**接口名称**

`BroadcastRequest#getBroadcastProgramDetails(long programId, HttpCallback<ProgramDetails> callback)`

**接口说明**

获取在线广播的某个节目的信息详情。

**参数说明**

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
|programId|long|是|在线广播节目Id|
|callback|HttpCallback|否|结果回调，成功返回[ProgramDetails](#9.16 ProgramDetails)对象|

**代码示例**

```java
long programId = 19196008;
new BroadcastRequest().getBroadcastProgramDetails(programId, new HttpCallback<ProgramDetails>() {

            @Override
            public void onSuccess(ProgramDetails programDetails) {
                tvDetails.setText(mGson.toJson(programDetails));
            }

            @Override
            public void onError(ApiException exception) {
                tvDetails.setText(mGson.toJson(exception));
            }
        });
```

### 3.9.4 获取在线广播当前正在播放的节目
**接口名称**

`BroadcastRequest#getBroadcastCurrentProgramDetails(long broadcastId, HttpCallback<ProgramDetails> callback)`

**接口说明**

获取某个在线广播正在播放的节目信息。

**参数说明**

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
| broadcastId |long|是|在线广播Id|
|callback|HttpCallback|否|结果回调，成功返回[ProgramDetails](#9.16 ProgramDetails)对象|

**代码示例**

```java
long id = 1600000000198L;
new BroadcastRequest().getBroadcastCurrentProgramDetails(id, new HttpCallback<ProgramDetails>() {
            @Override
            public void onSuccess(ProgramDetails programDetails) {
                tvDetails.setText(mGson.toJson(programDetails));
            }

            @Override
            public void onError(ApiException exception) {
                tvDetails.setText(mGson.toJson(exception));
            }
        });
```

### 3.9.5 根据在线广播id返回所在地方台或国家台在线广播列表
**接口名称**

`BroadcastRequest#getBroadcastNeighborList(long broadcastId,int pagenum,int pagesize, HttpCallback<BasePageResult<List<BroadcastDetails>>> callback)`

**接口说明**

根据在线广播id返回地方台或国家台在线广播列表。

**参数说明**

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
| broadcastId |long|是|在线广播Id|
| pagenum     |int |是|页码|
| pagesize    |int |是|每页请求数量|
|callback|HttpCallback|否|结果回调，成功返回[BroadcastDetails](#9.15 BroadcastDetails)列表|

**代码示例**

```java
new BroadcastRequest().getBroadcastNeighborList(mId, 1, 100, new HttpCallback<BasePageResult<List<BroadcastDetails>>>() {
            @Override
            public void onSuccess(BasePageResult<List<BroadcastDetails>> listBasePageResult) {
                if (!ListUtil.isEmpty(listBasePageResult.getDataList())) {
                    List<Item> datas = new ArrayList<>();
                    ArrayList<PlayItem> playItemList = new ArrayList<>();
                    for (int i = 0, size = listBasePageResult.getDataList().size(); i < size; i++) {
                        BroadcastDetails item = listBasePageResult.getDataList().get(i);
                        Item sai = new Item();
                        sai.id = item.getBroadcastId();
                        sai.type = ResType.TYPE_BROADCAST;
                        sai.title = item.getName();
                        sai.details = item.getFreq();
                        sai.item = item;
                        datas.add(sai);
                        playItemList.add(BeanUtil.transToPlayItem(listBasePageResult.getDataList().get(i)));
                    }
                    BroadcastRadioListManager.getInstance().setPlayList(playItemList);
                    mAdapter.setDataList(datas);
                }
            }

            @Override
            public void onError(ApiException exception) {

            }
        });
```
## 3.10 订阅
订阅相关接口所在类SubscribeRequest。订阅支持专辑、AI电台、在线广播、单曲、听电视的订阅。
订阅功能必须登录云听账号才可以使用。
==注意==：`HttpCallback#onError`的处理，没有登录会返回错误码"40000"，错误信息"参数有误"。

### 3.10.1 获取用户订阅列表
**接口名称**

`SubscribeRequest#getSubscribeList(int pageNum, int pageSize, HttpCallback<BasePageResult<List<SubscribeInfo>>> callback)`

**接口说明**

获取当前云听用户的订阅列表，可分页获取。

**参数说明**

| 参数名| 类型 | 是否必须 | 描述|
|:------|:-----|:-----|:----|
|pageNum|int|是|第几页|
|pageSize|int|是|每页个数|
|callback|HttpCallback|否|回调，成功返回[BasePageResult](#9.1 BasePageResult&lt;T&gt;)&lt;List&lt;[SubscribeInfo](#9.32 SubscribeInfo)&gt;&gt;|

**代码示例**

```java
int pageNum = 1;
int pageSize = 20;
new SubscribeRequest().getSubscribeList(pageNum, pageSize, new HttpCallback<BasePageResult<List<SubscribeInfo>>>() {
           @Override
            public void onSuccess(BasePageResult<List<SubscribeInfo>> result) {
                mTvInfo.setText(mGson.toJson(result));
            }

            @Override
            public void onError(ApiException exception) {
                mTvInfo.setText(mGson.toJson(exception));
            }
        });
```

### 3.10.2 订阅
**接口名称**

`SubscribeRequest#subscribe(long id, HttpCallback<SubscribeStatus> callback)`

**接口说明**

根据专辑、AI电台、在线广播、单曲、听电视的id订阅。传入id不需要区分专辑、AI电台、在线广播、单曲、听电视类型。

**参数说明**

| 参数名| 类型 | 是否必须 | 描述                                                                                                          |
|:------|:-----|:-----|:------------------------------------------------------------------------------------------------------------|
|id|long|是| 专辑、AI电台、在线广播、单曲、听电视的id                                                                                      |
|callback|HttpCallback|否| 回调，成功时返回订阅状态`SubscribeStatus`<br>订阅成功`SubscribeStatus.STATE_SUCCESS`<br>订阅失败`SubscribeStatus.STATE_FAILURE` |

**代码示例**

```java
long id = 0L;
        new SubscribeRequest().subscribe(id, new HttpCallback<SubscribeStatus>() {
            @Override
            public void onSuccess(SubscribeStatus result) {
                mTvInfo.setText(mGson.toJson(result));
            }

            @Override
            public void onError(ApiException exception) {
                mTvInfo.setText(mGson.toJson(exception));
            }
        });
```

### 3.10.3 取消订阅
**接口名称**

`SubscribeRequest#unsubscribe(long id, HttpCallback<SubscribeStatus> callback)`

**接口说明**

根据专辑、AI电台、在线广播、单曲、听电视的id取消订阅。传入id不需要区分专辑、AI电台、在线广播、单曲、听电视。

**参数说明**

| 参数名| 类型 | 是否必须 | 描述                                          |
|:------|:-----|:-----|:--------------------------------------------|
|id|long|是| 专辑、AI电台、在线广播、单曲、听电视的id                      |
|callback|HttpCallback|否| 回调，成功时返回取消订阅是否成功，<br>true表示取消订阅成功，false表示失败 |

**代码示例**

```java
new SubscribeRequest().unsubscribe(id, new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean result) {
                mTvInfo.setText(mGson.toJson(result));
            }

            @Override
            public void onError(ApiException exception) {
                mTvInfo.setText(mGson.toJson(exception));
            }
        });
```

### 3.10.4 检查是否订阅
**接口名称**

`SubscribeRequest#isSubscribed(long id, HttpCallback<SubscribeStatus> callback)`

**接口说明**

根据专辑、AI电台、在线广播、单曲、听电视的id检查该专辑、AI电台、在线广播、单曲、听电视是否已经订阅。传入id不需要区分专辑、AI电台、在线广播、单曲、听电视。

**参数说明**

| 参数名| 类型 | 是否必须 | 描述|
|:------|:-----|:-----|:----|
|id|long|是|专辑、AI电台、在线广播、单曲、听电视的id|
|callback|HttpCallback|否|回调，成功时返回是否订阅，<br>true表示已订阅，false表示未订阅|

**代码示例**

```java
new SubscribeRequest().isSubscribed(id, new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean result) {
                mTvInfo.setText(mGson.toJson(result));
            }

            @Override
            public void onError(ApiException exception) {
                mTvInfo.setText(mGson.toJson(exception));
            }
        });
```

## 3.11 收听历史
收听历史所有相关接口所在类`HistoryRequest`。收听历史只可保存专辑、AI电台、在线广播和听电视。
收听历史功能登录云听账号才可以和手机历史记录打通，否则只是车机端本地保存。
==注意==：`HttpCallback#onError`的处理，没有登录会返回错误码"40000"，错误信息"参数有误"。

### 3.11.1 获取收听历史列表
**接口名称**

`HistoryRequest#getHistoryList(HttpCallback<List<ListeningHistory>> callback)`

**接口说明**

获取收听历史列表，一次获取所有的，最多100条。

**参数说明**

| 参数名   | 类型 | 是否必须 | 描述|
| -------- | ------ | -------- | --------------- |
| callback | HttpCallback&lt;List&lt;ListeningHistory&gt;&gt; | 否       | 成功，<br>返回收听历史[ListeningHistory](#9.34 ListeningHistory)集合 |

**代码示例**

```java
new HistoryRequest().getHistoryList(new HttpCallback<List<ListeningHistory>>() {
            @Override
            public void onSuccess(List<ListeningHistory> listeningHistories) {
                if (mHistoryAdapter != null) {
                    mHistoryAdapter.setDataList(listeningHistories);
                }
            }

            @Override
            public void onError(ApiException exception) {
                showError("获取收听历史列表失败", exception);
            }
        });
```



### 3.11.2 清除收听历史
**接口名称**

`HistoryRequest#clearListeningHistory(HttpCallback<Boolean> callback)`

**接口说明**

清除收听历史。登录时，会清除云端的收听历史。

**参数说明**

| 参数名   | 类型 | 是否必须 | 描述 |
| -------- | ----------| -------- | ---------- |
| callback | HttpCallback&lt;Boolean&gt; | 否| 返回true表示清除成功，false失败。 |

**代码示例**

```java
new HistoryRequest().clearListeningHistory(new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean aBoolean) {
                if (aBoolean) {
                    showToast("清空历史成功");
                    mHistoryAdapter.clear();
                }else {
                    showToast("清空历史失败");
                }
            }
            @Override
            public void onError(ApiException exception) {
                showError("清空历史错误", exception);
            }
        });
```

### 3.11.3 保存收听历史
**接口名称**

`HistoryRequest#saveListeningHistory(String type, long id, long audioId, long playedTime, long duration, long timestamp, HttpCallback<Boolean> callback)`

**接口说明**

保存收听历史到服务器，需手动调用该接口，收听专辑时，每收听一个单曲时都需要上报一次，如果轮询调用该接口，可以记录最新收听时间位置，便于下次进行断点续听；收听AI电台、在线广播和听电视时，仅需播放开始调用该接口记录收听历史即可，AI电台、在线广播和听电视不支持断点续听

**参数说明**

| 参数名     | 类型 | 是否必须 | 描述                                                                                                                  |
| ---- | ---------- | -------- |---------------------------------------------------------------------------------------------------------------------|
| type       | String                      | 是       | 单曲所在集合类型，<br>专辑ResType.TYPE_ALBUM、<br>AI电台 ResType.TYPE_RADIO<br>在线广播 ResType.TYPE_BROADCAST<br>听电视 ResType.TYPE_TV |
| id         | long                        | 是       | 单曲所在集合id，专辑id，AI电台id，在线广播id，听电视id                                                                                   |
| audioId    | long                        | 否       | 单曲id，AI电台节目id，在线广播节目单id，听电视节目单id                                                                                    |
| playedTime | long                        | 是       | 本次播放位置，单位秒。此参数可以用于专辑的断点续听功能，定时轮询上报最新的播放位置，AI电台、在线广播和听电视传0就可以                                                           |
| duration   | long                        | 否       | 单曲时长，单位秒。                                                                                                           |
| timestamp  | long                        | 是       | 产生历史记录的时间戳（毫秒级）                                                                                                     |
| callback   | HttpCallback&lt;Boolean&gt; | 否       | 回调，返回true表示保存成功，false失败                                                                                             |

**代码示例**

```java
new HistoryRequest().saveListeningHistory(type, radioId, audioId, playedTime, duration, timestamp, new HttpCallback<Boolean>() {
  public void onSuccess(Boolean aBoolean) {
    Log.d("PlayerManager", "saveHistory保存历史: " + aBoolean);
  }

  public void onError(ApiException e) {
    Log.e("PlayerManager", "saveHistory保存历史异常=" + e);
  }
});
```

## 3.12 品牌信息
品牌信息相关接口所在类`BrandRequest`

### 3.12.1 获取品牌信息
**接口名称**

`BrandRequest#getBrandInfo(HttpCallback<BrandDetails> callback)`

**接口说明**

获取品牌信息，包括名称，logo，用户须知

**参数说明**

| 参数名   | 类型  | 是否必须 | 描述 |
| -------- | ----------- | -------- |--------------- |
| callback | HttpCallback&lt;BrandDetails&gt; | 否       | 成功，返回品牌信息[BrandDetails](#9.37 BrandDetails)。 |

**代码示例**

```java
new BrandRequest().getBrandInfo(new HttpCallback<BrandDetails>() {
            @Override
            public void onSuccess(BrandDetails brandDetails) {
                if (brandDetails != null) {
                    mTvBrandName.setText(brandDetails.getBrand());
                    Glide.with(BrandInfoActivity.this)
                      .load(brandDetails.getLogo())
                      .into(mIvBrandLogo);
                    mTvBrandAgreement.setOnClickListener(v -> {
                        Intent intent=new  Intent();
                        intent.setAction(Intent.ACTION_VIEW);
                        intent.setData(Uri.parse(brandDetails.getUserAgreement()));
                        startActivity(intent);
                    });
                }
            }

            @Override
            public void onError(ApiException exception) {

            }
        });
```

## 3.13 会员功能
会员功能需要先登录，相关接口所在类`PurchaseRequest`。
==注意==：`HttpCallback#onError`的处理，没有登录会返回错误码"40000"，错误信息"参数有误"。

### 3.13.1 获取VIP会员套餐列表
**接口名称**

`PurchaseRequest#getVipMeats(HttpCallback<List<VipMeals>> callback)`

**接口说明**

获取vip套餐信息列表，包括套餐id，套餐名称，套餐原价，套餐折扣价，VIP天数，商品说明，折扣说明

**参数说明**

| 参数名   | 类型  | 是否必须 | 描述 |
| -------- | ----------- | -------- |--------------- |
| callback | HttpCallback&lt;List&lt;VipMeals&gt;&gt; | 否       | 成功，返回VIP会员套餐[VipMeals](#9.40 VipMeals)列表信息。 |

**代码示例**

```java
new PurchaseRequest().getVipMeats(new HttpCallback<List<VipMeals>>() {
            @Override
            public void onSuccess(List<VipMeals> vipMeats) {
                if (adapter != null) {
                    List<Object> list = new ArrayList<>();
                    for (VipMeals i : vipMeats) {
                        list.add(i);
                    }
                    adapter.setDataList(list);
                    adapter.setOnItemClickListener((view, viewType, o, position) -> {
                        Intent intent = new Intent(VipMealsActivity.this, VipQRCodeActivity.class);
                        VipMeals meals = (VipMeals)o;
                        intent.putExtra("mealId", meals.getMealId());
                        intent.putExtra("price", meals.getDiscountPrice());
                        startActivity(intent);
                    });
                }
            }

            @Override
            public void onError(ApiException exception) {
                showError("获取vip套餐列表失败", exception);
            }
        });
```

### 3.13.2 获取VIP会员购买的二维码
**接口名称**

`PurchaseRequest#getVipQRCode(Long mealId, Long mealMoney, HttpCallback<QRCodeInfo> callback)`

**接口说明**

获取VIP会员购买的二维码

**参数说明**

| 参数名   | 类型  | 是否必须 | 描述                                              |
| -------- | ----------- | -------- |-------------------------------------------------|
| mealId    | Long | 是       | vip套餐id。                                        |
| mealMoney | Long | 是       | vip套餐价格。                                        |
| callback  | HttpCallback&lt;QRCodeInfo&gt; | 否       | 成功，返回VIP会员的购买二维码[QRCodeInfo](#9.36 QRCodeInfo)。 |

**代码示例**

```java
Long mealId = Long.valueOf(vipMealIdET.getText().toString().trim());
Long price = Long.valueOf(vipPriceET.getText().toString().trim());
mPurchaseRequest.getVipQRCode(mealId, price, new HttpCallback<QRCodeInfo>() {
            @Override
            public void onSuccess(QRCodeInfo qrCodeInfo) {
                mQrCodeId = qrCodeInfo.getQrCodeId();
                infoView.setText(qrCodeInfo.toString());
                Glide.with(VipQRCodeActivity.this)
                        .load(qrCodeInfo.getQrCodeImg())
                        .into(qrImageView);
            }

            @Override
            public void onError(ApiException exception) {
                showError("获取失败", exception);
            }
        });

```

### 3.13.3 获取专辑购买的二维码
**接口名称**

`PurchaseRequest#getAlbumQRCode(Long albumId, Long money, HttpCallback<QRCodeInfo> callback)`

**接口说明**

获取专辑购买的二维码

**参数说明**

| 参数名   | 类型  | 是否必须 | 描述                                           |
| -------- | ----------- | -------- |----------------------------------------------|
| albumId   | Long | 是       | 专辑id。                                        |
| money     | Long | 是       | 专辑价格。参照[AlbumDetails](#9.11 AlbumDetails)字段payMethod的类型[AlbumDetailsPayMethod](#9.46 AlbumDetailsPayMethod)的字段currentPrice                                        |
| callback  | HttpCallback&lt;QRCodeInfo&gt; | 否       | 成功，返回专辑的购买二维码[QRCodeInfo](#9.36 QRCodeInfo)。 |

**代码示例**

```java
new PurchaseRequest().getAlbumQRCode(Long.valueOf(albumIdEt.getText().toString().trim()),
                Long.valueOf(albumMoneyEt.getText().toString().trim()),
                new HttpCallback<QRCodeInfo>() {
                    @Override
                    public void onSuccess(QRCodeInfo qrCodeInfo) {
                        infoViewTv.setText(qrCodeInfo.toString());
                        Glide.with(AlbumQRCodeActivity.this)
                                .load(qrCodeInfo.getQrCodeImg())
                                .into(albumQrCodeImg);
                    }

                    @Override
                    public void onError(ApiException exception) {
                        showError("获取失败", exception);
                    }
                });

```

### 3.13.4 获取单曲购买的二维码
**接口名称**

`PurchaseRequest#getAudioQRCode(String audioIds, Long albumId, Long money,HttpCallback<QRCodeInfo> callback)`

**接口说明**

获取单曲购买的二维码

**参数说明**

| 参数名   | 类型  | 是否必须 | 描述                                           |
| -------- | ----------- | -------- |----------------------------------------------|
| audioIds  | String | 是       | 选择的所有单曲id，逗号分隔的id字符串。                        |
| albumId   | Long   | 是       | 专辑id。                                        |
| money     | Long   | 是       | 所有选中单曲的总价。参照[AudioDetails](#9.13 AudioDetails)字段payMethod的类型[AlbumDetailsPayMethod](#9.46 AlbumDetailsPayMethod)的字段currentPrice                                   |
| callback  | HttpCallback&lt;QRCodeInfo&gt; | 否       | 成功，返回单曲的购买二维码[QRCodeInfo](#9.36 QRCodeInfo)。 |

**代码示例**

```java
new PurchaseRequest().getAudioQRCode(audiosIdsEt.getText().toString().trim(),
                Long.valueOf(audiosIdEt.getText().toString().trim()),
                Long.valueOf(audiosMoneyEt.getText().toString().trim()),
                new HttpCallback<QRCodeInfo>() {
            @Override
            public void onSuccess(QRCodeInfo qrCodeInfo) {
                infoViewTv.setText(qrCodeInfo.toString());
                Glide.with(AudiosQRCodeActivity.this)
                        .load(qrCodeInfo.getQrCodeImg())
                        .into(audiosQrCodeImg);
            }

            @Override
            public void onError(ApiException exception) {
                showError("获取失败", exception);
            }
        });

```

### 3.13.5 支付二维码扫码结果查询
**接口名称**

`PurchaseRequest#qrCodeStatus(String qrCodeId, HttpCallback<PurchaseSucess> callback`

**接口说明**

查询支付二维码扫码结果

**参数说明**

| 参数名   | 类型  | 是否必须 | 描述 |
| -------- | ----------- | -------- |--------------- |
| qrCodeId  | String | 是       | 二维码id。  |
| callback  | HttpCallback&lt;PurchaseSucess&gt; | 否       | 成功，返回二维码的扫码支付结果[PurchaseSucess](#9.42 PurchaseSucess)。 |

**代码示例**

```java
new PurchaseRequest().qrCodeStatus(qrCodeIdEt.getText().toString().trim(),  new HttpCallback<PurchaseSucess>() {
            @Override
            public void onSuccess(PurchaseSucess status) {
                qrStatusHistoryTv.setText(status.toString());
            }

            @Override
            public void onError(ApiException exception) {
                showError("获取失败", exception);
            }
        });

```

### 3.13.6 查询已购列表
**接口名称**

`PurchaseRequest#getPurchasedList(int pageNum, int pageSize, HttpCallback<BasePageResult<List<PurchasedItem>>> callback)`

**接口说明**

查询已购的内容列表

**参数说明**

| 参数名   | 类型  | 是否必须 | 描述 |
| -------- | ----------- | -------- |--------------- |
| pageNum  | int | 是       | 页码。  |
| pageSize | int | 是       | 每页请求数量。  |
| callback  | HttpCallback&lt;BasePageResult&lt;List&lt;PurchasedItem&gt;&gt;&gt; | 否       | 成功，返回已购的内容[PurchasedItem](#9.43 PurchasedItem)列表。 |

**代码示例**

```java
new PurchaseRequest().qrCodeStatus(qrCodeIdEt.getText().toString().trim(),  new HttpCallback<PurchaseSucess>() {
            @Override
            public void onSuccess(PurchaseSucess status) {
                qrStatusHistoryTv.setText(status.toString());
            }

            @Override
            public void onError(ApiException exception) {
                showError("获取失败", exception);
            }
        });

```
### 3.13.7 查询订单列表
**接口名称**

`PurchaseRequest#getOrderList(int pageNum, int pageSize, HttpCallback<BasePageResult<List<Order>>> callback)`

**接口说明**

查询购买的订单列表

**参数说明**

| 参数名   | 类型  | 是否必须 | 描述 |
| -------- | ----------- | -------- |--------------- |
| pageNum  | int | 是       | 页码。  |
| pageSize | int | 是       | 每页请求数量。  |
| callback  | HttpCallback&lt;BasePageResult&lt;List&lt; Order&gt;&gt;&gt; | 否       | 成功，返回订单[Order](#9.44 Order)列表。 |

**代码示例**

```java
new PurchaseRequest().getOrderList(1, 10, new HttpCallback<BasePageResult<List<Order>>>() {
            @Override
            public void onSuccess(BasePageResult<List<Order>> result) {
                if (adapter != null) {
                    List<Object> list = new ArrayList<>();
                    for (Order i : result.getDataList()) {
                        list.add(i);
                    }
                    adapter.setDataList(list);
                }
            }

            @Override
            public void onError(ApiException exception) {
                showError("获取v列表失败", exception);
            }
        });

```

## 3.14 活动专区
活动专区相关接口所在类`ActivityRequest`

### 3.14.1 获取活动内容列表
**接口名称**

`ActivityRequest#getInfoList(HttpCallback<BasePageResult<List<Activity>>> callback)`

**接口说明**

活动内容列表

**参数说明**

| 参数名   | 类型  | 是否必须 | 描述 |
| -------- | ----------- | -------- |--------------- |
| callback | HttpCallback&lt;BasePageResult&lt;List&lt;Activity&gt;&gt;&gt; | 否       | 成功，返回活动内容[Activity](#9.45 Activity)列表。 |

**代码示例**

```java
new ActivityRequest().getInfoList(new HttpCallback<BasePageResult<List<Activity>>>() {
            @Override
            public void onSuccess(BasePageResult<List<Activity>> listBasePageResult) {
                mActivityAdapter.setDataList(listBasePageResult.getDataList());
            }

            @Override
            public void onError(ApiException exception) {

            }
        });
```
## 3.15 听电视
听电视相关接口在`TVRequest`

### 3.15.1 获取听电视详情
**接口名称**

`TVRequest#getTVDetails(long tvId, HttpCallback<TVDetails> callback)`

**接口说明**

 获取听电视详情

**参数说明**

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
| tvId |long|是|听电视Id|
|callback|HttpCallback&lt;TVDetails&gt;|否|结果回调，成功返回[TVDetails](#9.49 TVDetails)|

**代码示例**

```java
new TVRequest().getTVDetails(mId, new HttpCallback<TVDetails>() {
            @Override
            public void onSuccess(TVDetails programDetails) {
            }

            @Override
            public void onError(ApiException exception) {
            }
        });
```
### 3.15.2 获取听电视节目列表
**接口名称**

`TVRequest#getTVProgramList(long tvId, @Nullable String date, HttpCallback<List<TVProgramDetails>> callback) `

**接口说明**

获取听电视节目列表

**参数说明**

|参数名|类型|是否必须| 描述                                                          |
|:---|:---|:---|:------------------------------------------------------------|
| tvId |long|是| 听电视Id                                                       |
| date |String|是| 日期，格式“yyyy-MM-dd”                                           |
|callback|HttpCallback&lt;List&lt;TVProgramDetails&gt;&gt;|否|结果回调，成功返回[TVProgramDetails](#9.51 TVProgramDetails)列表|

**代码示例**

```java
  new TVRequest().getTVProgramList(mId, mDate, new HttpCallback<List<TVProgramDetails>>() {
            @Override
            public void onSuccess(List<TVProgramDetails> programDetails) {
          
            }

            @Override
            public void onError(ApiException exception) {
            }
        });
```

### 3.15.3 获取听电视节目详情
**接口名称**

`TVRequest#getTVProgramDetails(long programId, HttpCallback<TVProgramDetails> callback) `

**接口说明**

获取听电视节目详情

**参数说明**

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
| programId |long|是|电视节目ID|
|callback|HttpCallback&lt;TVProgramDetails&gt;|否|结果回调，成功返回[TVProgramDetails](#9.51 TVProgramDetails)列表|

**代码示例**

```java
  new TVRequest().getTVProgramDetails(mId,  new HttpCallback<TVProgramDetails>() {
            @Override
            public void onSuccess(TVProgramDetails programDetails) {
          
            }

            @Override
            public void onError(ApiException exception) {
            }
        });
```

## 3.16 音质设置
音质列表相关接口在`ToneQualityRequest`
此功能可以让用户选择不同音质，来收听内容。目前为高低两种码率，高码率192k，低码率48k。选择完音质，SDK会保存到本地，SDK播放器会以此选择对应的播放地址，进行播放。
通过getSoundQualities（HttpCallback<ToneQualityResponse> callback）获取音质列表数据，
通过 ToneQualityHelper.getInstance().setToneQuality(item.quality)设置音质，由SDK本地存储，
通过ToneQualityHelper.getInstance().getToneQuality()获取当前设置的音质，回显选中状态。

### 3.16.1  获取音质列表
**接口名称**

`ToneQualityRequest#getSoundQualities(HttpCallback<ToneQualityResponse> callback) `

**接口说明**

获取音质列表。
**参数说明**

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
|callback|HttpCallback&lt;ToneQualityResponse&gt;|否|结果回调，成功返回[ToneQualityResponse](#9.54 ToneQualityResponse)|

**代码示例**

```java
  new ToneQualityRequest().getSoundQualities(new HttpCallback<ToneQualityResponse>() {
              @Override
              public void onSuccess(ToneQualityResponse soundQualityResponse) {
              
              }
  
              @Override
              public void onError(ApiException e) {
              }
          });
```
### 3.16.2  设置音质
**接口名称**

` ToneQualityHelper.getInstance().setToneQuality(toneQuality) `

**接口说明**

设置音质，由SDK本地存储，
**参数说明**

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
|toneQuality|ToneQuality|是|音质对象|

**代码示例**

```java
  ToneQualityHelper.getInstance().setToneQuality(toneQuality)
```
### 3.16.3  获取当前设置的音质
**接口名称**

` ToneQualityHelper.getInstance().getToneQuality() `

**接口说明**

获取当前设置的音质。
**参数说明**

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|

**代码示例**

```java
ToneQualityHelper.getInstance().getToneQuality()
```

## 3.17 场景推荐
场景推荐相关接口在`RecRequest`
云听支持多种推荐能力，满足车载驾驶行为的各个细分场景的个性化体验。可以根据用户所属场景给用户推荐场景下的优质内容。

### 3.17.1  基于车主当前开车场景（车型、时间、地点、车速、年龄、性别、天气等）的场景推荐
**接口名称**

`RecRequest#getPresentRadioList(String carTypeCode
, String timeCode
, String sexCode
, String ageCode
, String weatherCode
, String speedCode
, String specialScenesCode
, Map<String, String> extraParams 
, HttpCallback<BaseSceneListData<List<SceneDataList>>> callback) `

**接口说明**
基于车主当前开车场景（车型、时间、地点、车速、年龄、性别、天气等）的场景推荐。请在结果展示和点击的时候进行数据上报，以便更好的优化推荐效果，参照[推荐展示数据上报](###8.5.3 推荐展示数据上报)和[推荐点击数据上报](###8.5.4 推荐点击数据上报)。
**参数说明**

|参数名|类型|是否必须| 描述                                                                                                                  |
|:---|:---|:---|:--------------------------------------------------------------------------------------------------------------------|
|carTypeCode|String|否| [车型场景代码](###11.2.3 车型)                                                                                              |
|timeCode|String|否| [时间场景代码](###11.2.1 时间)，不传取系统当前时间                                                                                    |
|sexCode|String|否| [性别场景代码](###11.2.4 性别)                                                                                              |
|ageCode|String|否| [年龄场景代码](###11.2.2 年龄)                                                                                              |
|weatherCode|String|否| [天气场景代码](###11.2.5 天气)                                                                                              |
|speedCode|String|否| [车速场景代码](###11.2.6 车速)                                                                                              |
|specialScenesCode|String|否| [特殊场景代码](###11.2.7 特定事件场景)                                                                                          |
| extraParams |Map&lt;String,String&gt;|否| 额外参数，暂时无用                                                                                                           |
|callback|HttpCallback&lt;BaseSceneListData&lt;List&lt;SceneDataList&gt;&gt;&gt;|否| 结果回调，成功返回[BaseSceneListData](##9.55 BaseSceneListData)&lt;List&lt;[SceneDataList](#9.56 SceneDataList)&gt;&gt; |
**代码示例**

```java
  new RecRequest().getPresentRadioList("302","103","401","204","505","601",null,null,new HttpCallback<BaseSceneListData<List<SceneDataList>>>() {
              @Override
              public void onSuccess(BaseSceneListData<List<SceneDataList>> result) {
              
              }
  
              @Override
              public void onError(ApiException e) {
              }
          });
```

### 3.17.2  基于场景代码的场景推荐
**接口名称**

`RecRequest#getSceneRadioList(String code
, Map<String, String> extraParams
, HttpCallback<BaseSceneListData<List<SceneDataList>>> callback) `

**接口说明**
基于场景代码的场景推荐。请在结果展示和点击的时候进行数据上报，以便更好的优化推荐效果，参照[推荐展示数据上报](###8.5.3 推荐展示数据上报)和[推荐点击数据上报](###8.5.4 推荐点击数据上报)。
**参数说明**

| 参数名        |类型| 是否必须 | 描述                                                                                                                |
|:-----------|:---|:-----|:------------------------------------------------------------------------------------------------------------------|
| code|String| 是    | [场景代码](###11.2.8 场景代码)                                                                                              |
| extraParams|Map&lt;String,String&gt;| 否    | 额外参数，暂时无用                                                                                                         |
| callback   |HttpCallback&lt;BaseSceneListData&lt;List&lt;SceneDataList&gt;&gt;&gt;| 否    | 结果回调，成功返回[BaseSceneListData](##9.55 BaseSceneListData)&lt;List&lt;[SceneDataList](#9.56 SceneDataList)&gt;&gt;    |
**代码示例**

```java
  new RecRequest().getSceneRadioList("1051",null,new HttpCallback<BaseSceneListData<List<SceneDataList>>>() {
              @Override
              public void onSuccess(BaseSceneListData<List<SceneDataList>> result) {
              
              }
  
              @Override
              public void onError(ApiException e) {
              }
          });
```

## 3.18 应急广播
国家应急广播预警信息，包括县级以上人民政府或其指定的应急信息发布部门发布的应急信息，如事故灾害风险预警预报、气象预警预报、突发事件、防灾减灾救灾、人员转移安置、应急科普等应急信息。
SDK提供三种对接方案，优先建议：长连接推送应急广播>轮询获取应急广播>单一时机获取应急广播。
**单一时机获取应急广播**
该接口返回当前用户可以收到的应急广播内容，建议启动时或启动后仅调用一次，如果需要频繁调用，请使用轮询获取应急广播
**长连接推送应急广播**
通过已建立的长连接，推送应急广播。
**轮询获取应急广播**
在启动后轮询获取消息，轮询时间间隔n分钟（10<=n<=1440)

### 3.18.1 单一时机获取应急广播
**接口名称**
`EmergencyRequest#getEmergencyMessage(HttpCallback<EmergencyBroadcast> callback)`

**接口说明**
单一时机获取应急广播，该接口返回当前用户可以收到的应急广播内容，建议启动时或启动后仅调用一次，如果需要频繁调用，请使用轮询获取应急广播。
**参数说明**

|参数名|类型|是否必须| 描述                                                                                                              |
|:---|:---|:---|:----------------------------------------------------------------------------------------------------------------|
|callback|HttpCallback&lt;EmergencyBroadcast&gt;|否| 结果回调，成功返回[EmergencyBroadcast](##9.58 EmergencyBroadcast) |

**代码示例**

```java
  new EmergencyRequest().getEmergencyMessage(new HttpCallback<EmergencyBroadcast>() {
              @Override
              public void onSuccess(EmergencyBroadcast result) {
              
              }
  
              @Override
              public void onError(ApiException e) {
              }
          });
```
### 3.18.2 长连接推送应急广播
**接口名称**
`EmergencyBroadcastManager#getInstance()#requestEmergencyBroadcast(EmergencyBroadcastListener callback)`
`EmergencyBroadcastManager#getInstance()#disconnectSocket()`

**接口说明**
长连接推送接口，以及关闭长连接推送
**参数说明**

|参数名|类型| 是否必须 | 描述                                    |
|:---|:---|:-----|:--------------------------------------|
|callback|EmergencyBroadcastListener| 是    | 结果回调，成功返回[EmergencyBroadcast](##9.58 EmergencyBroadcast) |

**代码示例**

```java
  EmergencyBroadcastManager.getInstance().requestEmergencyBroadcast(new EmergencyBroadcastListener() {
              @Override
              public void onSuccess(EmergencyBroadcast result) {
              
              }
  
              @Override
              public void onError(ApiException e) {
              }
          });
```
### 3.18.3 轮询获取应急广播
**接口名称**
`EmergencyBroadcastManager#getInstance()#requestEmergencyBroadcast(int intervalTimeMinutes, EmergencyBroadcastListener callback)`
`EmergencyBroadcastManager#getInstance()#stopPollingRequest()`

**接口说明**
定时轮询接口，以及停止轮询
**参数说明**

|参数名|类型| 是否必须 | 描述                                                       |
|:---|:---|:-----|:---------------------------------------------------------|
|intervalTimeMinutes|int| 是    | 间隔时间，单位分钟，最小10分钟，最大1440分钟                                |
|callback|EmergencyBroadcastListener| 是    | 结果回调，成功返回[EmergencyBroadcast](##9.58 EmergencyBroadcast) |

**代码示例**

```java
  EmergencyBroadcastManager.getInstance().requestEmergencyBroadcast(10, new EmergencyBroadcastListener() {
              @Override
              public void onSuccess(EmergencyBroadcast result) {
              
              }
  
              @Override
              public void onError(ApiException e) {
              }
          });
```

# 4 播放器说明

本部分介绍云听车载版SDK中附带播放器的使用说明。附件提供演示demo。

## 4.1 播放器概述
由于Android系统自带的媒体播放器不同版本差异较大，且后期我们会对云听平台的音频内容加密，所以如果希望正常收听云听平台的音频内容，需要使用SDK自带版播放器才能正常播放。目前播放器支持点播、在线广播和听电视的直播和回放。

新版（1.6.0）开始，点播、广播和听电视放均使用PlayerManager（com.kaolafm.opensdk.player.logic. PlayerManager）完成播放。
旧版本（1.6.0之前，不包含1.6.0）点播使用PlayerManager（com.kaolafm.sdk.core.mediaplayer.PlayerManager），在线广播直播和回放使用BroadcastRadioPlayerManager(com.kaolafm.sdk.core.mediaplayer. BroadcastRadioPlayerManager)。
需注意：旧版本PlayerManager和BroadcastRadioPlayerManager已废弃，为了更好地使用SDK，请尽快使用新版本接入。


## 4.2 播放器状态介绍
为了方便开发者接入SDK播放器，SDK播放器的各状态尽量保持和Android系统播放器状态一致。

## 4.3 播放器初始化
初始化方法：PlayerManager.getInstance().init(Context context); Context建议传Applicatioin。

SDK初始化的时候会自动调用此方法。

## 4.4 播放器监听器注册
此部分介绍第三方应用如何监听播放器的各种状态变化，具体请实现相应的回调，并通过以下操作方式添加和移除监听。播放器初始化之后，第三方应用需要在自己的应用中根据需要添加一系列监听器来实现自己的业务逻辑。

|  描述                |      方法                                      |
|---------------------|-----------------------------------------------|
|添加播放器初始化完成监听      |addPlayerInitComplete([IPlayerInitCompleteListener](# 10.1 IPlayerInitCompleteListener))|
|删除播放器初始化完成监听      |removePlayerInitComplete([IPlayerInitCompleteListener](# 10.1 IPlayerInitCompleteListener)）|
|添加播单改变监听            |addPlayListControlStateCallback([IPlayListStateListener](# 10.2 IPlayListStateListener))|
|删除播单改变监听            |removePlayListControlStateCallback([IPlayListStateListener](# 10.2 IPlayListStateListener)）|
|添加播放状态监听            |addPlayControlStateCallback([IPlayerStateListener](# 10.3 IPlayerStateListener))|
|删除播放状态监听            |removePlayControlStateCallback([IPlayerStateListener](# 10.3 IPlayerStateListener)）|
|添加音频焦点状态监听         |addAudioFocusListener([OnAudioFocusChangeInter](# 10.4 OnAudioFocusChangeInter))|
|删除音频焦点状态监听         |removeAudioFocusListener([OnAudioFocusChangeInter](# 10.4 OnAudioFocusChangeInter)）|
|添加播放器通用回调           |addGeneralListener([IGeneralListener](# 10.5 IGeneralListener))|
|删除播放器通用回调           |removeGeneralListener([IGeneralListener](# 10.5 IGeneralListener)）|

下面以播放器播放状态（IPlayerStateListener）和播单变化（IPlayListStateListener）的监听器为例。

**代码示例**
```java
// 播放状态监听器
IPlayerStateListener mPlayerStateListener = new IPlayerStateListener() {
    @Override
    public void onIdle(PlayItem playItem) {
		//TODO 播放器空闲状态（播放器没有播放内容时候会回调该方法）
    }

    @Override
    public void onPlayerPreparing(PlayItem playItem) {
		//TODO 播放器准备
    }

    @Override
    public void onPlayerPlaying(PlayItem playItem) {
		//TODO 开始播放
    }

    @Override
    public void onPlayerPaused(PlayItem playItem) {
		//TODO 播放暂停
    }

    @Override
    public void onProgress(PlayItem playItem, long progress, long total) {
		//TODO 播放进度
    }

    @Override
    public void onPlayerFailed(PlayItem playItem, int what, int extra) {
		//TODO 播放失败
    }

    @Override
    public void onPlayerEnd(PlayItem playItem) {
		//TODO 播放停止
    }

    @Override
    public void onSeekStart(PlayItem playItem) {
		//TODO 拖拽进度开始
    }

    @Override
    public void onSeekComplete(PlayItem playItem) {
		//TODO 拖拽完成
    }

    @Override
    public void onBufferingStart(PlayItem playItem) {
		//TODO 卡顿开始
    }

    @Override
    public void onBufferingEnd(PlayItem playItem) {
		//TODO 卡顿结束
    }

    @Override
    public void onDownloadProgress(PlayItem playItem, long progress, long total) {
		//TODO 下载进度
    }
};
// 添加播放状态监听器
PlayerManager.getInstance().addPlayControlStateCallback(mPlayerStateListener);

// 播单状态监听器
private IPlayListStateListener mPlayListStateListener = new IPlayListStateListener() {
        @Override
        public void onPlayListChange(List<PlayItem> list) {
			//TODO 播单列表发生变化
			List<Item> datas = new ArrayList<>();
                    if (!ListUtil.isEmpty(playItemList)) {
                        for (int i = 0, size = playItemList.size(); i < size; i++) {
                            PlayItem item = playItemList.get(i);
                            Item sai = new Item();
                            sai.id = item.getAudioId();
                            sai.type = DetailActivity.TYPE_ALBUM;
                            sai.title = item.getTitle();
                            sai.details = item.getAlbumTitle();
                            sai.audition = item.getAudition();
                            sai.fine = item.getFine();
                            sai.buyStatus = item.getBuyStatus();
                            sai.item = item;
                            sai.playItem = item;
                            datas.add(sai);
                        }
                    }

                    if (datas.isEmpty()) {
                        showToast(isLoadMore ? "没有更多" : "播单列表为空");
                    } else {
                        if (isLoadMore) {
                            mAdapter.addDataList(datas);
                        } else {
                            mAdapter.setDataList(datas);
                        }
                    }
                    if (mTrfDetailPlaylist != null) {
                        mTrfDetailPlaylist.finishLoadmore();
                    }
        }

        @Override
        public void onPlayListChangeError(int i) {
			//TODO 播单列表改变发生错误
        }
    };
// 添加播单状态的监听器
PlayerManager.getInstance().addPlayListControlStateCallback(mPlayListStateListener);
```

## 4.5 通过播放器播放
新版（1.6.0）播放器的播放主要通过调用PlayerManager（com.kaolafm.opensdk.player.logic. PlayerManager）完成。开发前需要知道的是，PlayerBuiler是播放资源的实体类，包含播放id，播放类型，是否可以订阅三个元素。开发者可通过PlayerBuiler设置需要播放的资源id和类型，然后调用PlayerManager的start(PlayerBuiler)方法进行播放。IPlayerStateListener是播放器播放状态的监听器，通过添加此监听器可以在播放器各个播放状态时实现开发者需要做的事情。IPlayListStateListener是播单变化的监听器，用来实现当播单列表发生变化时的操作。
见[4.5.1 播放专辑](# 4.5.1 播放专辑)，[4.5.2 播放单曲](# 4.5.2 播放单曲)，[4.5.3 播放AI电台](# 4.5.3 播放AI电台)，[4.5.4 播放在线广播](# 4.5.4 播放在线广播)，[4.5.5 播放听电视](# 4.5.5 播放听电视)  示例代码

### 4.5.1 播放专辑

播放指定id的专辑，播放器会从该专辑的第一条开始播放播单。

**代码示例**
```java
long albumId = 1100002157060L;
PlayerManager.getInstance().start(new PlayerBuilder().setId(String.valueOf(albumId)).setType(PlayerConstants.RESOURCES_TYPE_ALBUM));
```
更加详细的调用流程请参考demo 3.1。

### 4.5.2 播放单曲
播放指定id的单曲，播放器会从指定单曲开始播放所在专辑的播单列表。

**代码示例**

```java
long audioId = 1000026368360L;
PlayerManager.getInstance().start(new PlayerBuilder().setId(String.valueOf(audioId)).setType(PlayerConstants.RESOURCES_TYPE_AUDIO));
```
更加详细的调用流程请参考demo 3.2。

### 4.5.3 播放AI电台
播放指定id的AI电台，播放器会播放指定AI电台的播单列表。

**代码示例**
```java
long radioId = 1200000000099L;
PlayerManager.getInstance().start(new PlayerBuilder().setId(String.valueOf(radioId)).setType(PlayerConstants.RESOURCES_TYPE_RADIO));
```
更加详细的调用流程请参考demo 3.3。

### 4.5.4 播放在线广播
播放指定id的在线广播，播放器会根据指定id获取该在线广播的节目单列表加入播单，并从播单中找到当前正在直播中的节目进行播放，如果没有正在直播的节目，就从第一条开始播放。

**代码示例**
```java
long broadcastId = 1600000000323L;
PlayerManager.getInstance().start(new PlayerBuilder().setId(String.valueOf(broadcastId)).setType(PlayerConstants.RESOURCES_TYPE_BROADCAST));
```
更加详细的调用流程请参考demo 3.5。

### 4.5.5 播放听电视
播放指定id的听电视，播放器会根据指定id获取该听电视的节目单列表加入播单，并从播单中找到当前正在直播中的节目进行播放，如果没有正在直播的节目，就从第一条开始播放。

**代码示例**
```java
long listenTVId = 1700000000080L;
PlayerManager.getInstance().start(new PlayerBuilder().setId(String.valueOf(listenTVId)).setType(PlayerConstants.RESOURCES_TYPE_TV));
```

## 4.6 播放器控制
新版（1.6.0）播放器主要由PlayerManager（com.kaolafm.opensdk.player.logic. PlayerManager）提供一系列方法用于控制播放器的状态，主要有：开始播放，暂停播放，上一首，下一首，拖动等。
旧版本PlayerManager和BroadcastRadioPlayerManager提供的方法已经废弃。

### 新版本播放器控制函数
| 描述                              | 方法 | 备注                                                |
|---------------------------------|-----|---------------------------------------------------|
| 获取播放器对象单例                       |getInstance()|                                                   |
| 获取播放器初始化是否成功                    |isPlayerInitSuccess()| 返回boolean类型，true表示成功，false表示失败                    |
| 请求音频焦点                          |requestAudioFocus()| 返回boolean类型，true表示成功，false表示失败                    |
| 释放音频焦点                          |abandonAudioFocus()| 返回boolean类型，true表示成功，false表示失败                    |
| 获取当前音频焦点状态                      |getCurrentAudioFocusStatus()| 返回int值，对应取值和Android系统保持一致                         |
| 设置音量                            |setMediaVolume(float leftVolume, float rightVolume)| 取值范围[0,100]                                       |
| 设置是否支持音量均衡                      |setLoudnessNormalization(int isActive)| 1是开启，0是关闭                                         |
| 关闭log                           |setLogInValid()| 设置关闭ijkplayer的日志信息                                |
| 播放一个指定的播放源(专辑、单曲、AI电台、在线广播、听电视) | start(PlayerBuilder builder) | 播放一个[PlayerBuilder](#9.39 PlayerBuilder)          |
| 播放一个临时任务                        |startTempTask([TempTaskPlayItem](# 9.21 TempTaskPlayItem) playItem)| 用来播放一个临时任务，例如广告或者定时播报                             |
| 停止播放临时任务                        |stopTempTask()|                                                   |
| 通过audioId请求数据拿到playItem         |getPlayItemFromAudioId(long audioId,GetPlayItemListener listener)| 成功返回PlayItem，失败返回ApiException                     |
| 播放                              | play(boolean fromUser) | fromUser表示是否由用户主动调用                               |
| 播放                              | play() | 相当于调用play(false)                                  |
| 暂停                              | pause(boolean fromUser) | fromUser表示是否由用户主动调用。                              |
| 暂停                              | pause() | 相当于调用pause(false);        |
| 拖动                              |seek(int position)| 在线广播和听电视直播无法拖动                                    |
| 结束播放                            | reset(boolean fromUser) | fromUser表示是否由用户主动调用                               |
| 结束播放                            | reset() | 相当于调用reset(false)                                 |
| 判断播放器是否正在播放                     |isPlaying() | true 正在播放；false 未播放。                              |
| 是否有上一首                          |hasPre()| true表示有，false表示没有                                 |
| 是否有下一首                          |hasNext()| true表示有，false表示没有                                 |
| 播放当前列表中的上一首                     | playPre() |                                                   |
| 播放当前列表中的下一首                     | playNext() |                                                   |
| 获取当前播放时间点                        | getCurrentPlayPlace()  | 返回long类型，单位是毫秒                                           |
| 判断播放状态是否由用户主动暂停                 |isPauseFromUser()| 返回boolean类型，true 为用户主动暂停，false表示非用户主动暂停           |
| 切换播放器暂停或开始状态                    |switchPlayerStatus(boolean fromUser)| fromUser表示是否由用户主动调用                               |
| 切换播放器暂停或开始状态                    |switchPlayerStatus() | 相当于调用switchPlayerStatus(false)                    |
| 播放播单列表里面的数据                     |startPlayItemInList([PlayItem](# 9.17 PlayItem) playItem)|                                                   |
| 获取当前播放资源在播单中的位置                 |getPlayListCurrentPosition()| 返回int类型                                           |
| 获取当前播单列表                        |getPlayList()| 返回List&lt;[PlayItem](# 9.17 PlayItem)&gt;         |
| 获取当前播单信息                        |getPlayListInfo()| 返回[PlaylistInfo](# 9.28 PlaylistInfo)             |
| 获取播单控制                          |getPlayListControl()| 返回[IPlayListControl](# 10.6 IPlayListControl)的实体类 |
| 注册用户自定义播单控制                     |registerCustomPlayList(int type, Class<? extends IPlayListControl> iPlayListClass)| 参数type为自定义内容类型，必须大于1000，防止与预设的内容类型冲突              |
| 重置播单                            |resetPlayListControl()| 用户状态变化或支付状态变化时重新拉取播单时调用                           |

## 4.7 播放器监听器移除
第三方应用在自己的业务逻辑完成后（例如退出页面或者添加监听器的作用完成）需要移除之前注册过的监听器。

**代码示例**
```java
PlayerManager.getInstance().removePlayControlStateCallback(mPlayerStateListener);
PlayerManager.getInstance().removePlayListControlStateCallback(mPlayListStateListener);
```

## 4.8 播放器释放
当程序不在使用播放器时，应及时释放播放器，让系统回收资源。
具体调用方法是：
`PlayerManager.getInstance().destroy();`

## 4.9 设置播放器音频属性
同时设置音频属性的预期用途（例如闹铃或铃声），以及设置音频属性的内容类型（例如语音或音乐）。此设置作用域为全局。
具体调用方法是：
`PlayerManager.getInstance().setUsageAndContentType(int usage, int contentType);`

**参数说明**

|参数名| 类型  | 是否必须 | 描述                                                          |
|:---|:----|:-----|:------------------------------------------------------------|
|usage| int | 是    | 预期用途，默认AudioAttributes.USAGE_MEDIA，所有取值见[AudioAttributes](https://developer.android.com/reference/android/media/AudioAttributes) |
|contentType| int | 是    | 内容类型，默认AudioAttributes.CONTENT_TYPE_MUSIC，所有取值见[AudioAttributes](https://developer.android.com/reference/android/media/AudioAttributes)                   |

# 5 错误码
--
## 5.1 ErrorCode

网络接口错误码

errcode | errmsg
------- | --------
307   | 请求被重定向
404	  | 请求地址不存在
403	  | 请求被服务器拒绝
408	  | 请求超时
500	  | 服务器错误
503   | 服务不可用
600   | 未知错误
601	  | json解析异常
602   | 无法解析该域名
603   | 网络连接异常
604   | 网络连接超时
605   | 数据错误
606   | 数据为空
607   | 网络不可用
40000 | 参数有误
40100 | 身份验证未通过
40102 | Token失效
40300 | 权限校验错误
40301 | 设备未激活
40400 | 资源不存在
40431 | refreshToken不存在或已过期
40432 | token不存在或已过期
40500 | 请求方式不允许
40600 | 请求url不存在
42900 | 请求频率超配
42901 | 设备超限额
50000 | 服务器内部错误
50001 | 设备已激活
50200 | 服务关闭或正在升级
50500 | 重复激活
50501 | 参数缺失异常
50503 | 参数类型不匹配异常
50600 | 未激活
50811 | 付费节目，未购买|
80100 | 非叶子分类编码无法获取分类成员数量
80200 | 非父分类编码无法获取子分类列表

## 5.2 onPlayerFailed错误码

播放器回调IPlayerStateListener中onPlayerFailed方法的错误码

|errcode      |errmsg |
|------- |-------|
|-2147483648  |系统底层错误 |
|-1413828334  |底层IJKPLAYER没有连接网络 |
|-10000       |底层IJKPLAYER出现错误 |
|-1010        |数据不⽀持 |
|-1007        |比特流不符合相关的编码标准和文件规范 |
|-1004        |本地文件或网络相关错误（IO Exception） |
|-110         |数据超时错误（通常是超过了3—5秒） |
|-1           |无网络 |
|1            |未指定的错误 |
|100          |视频中断，一般是视频源异常或者不支持的视频类型 |
|200          |数据错误没有有效的回收
|400          |没有ts文件造成的错误 |
|404          |播放资源不存在，表示服务器上还没有这个资源，表示这个节目从来没有在服务器上出现过 |
|502          |网关错误 |
|503          |播放的文件不存在。是在拿到索引文件后，却拿不到具体的TS文件，这时通常都是没有推流 |
|700          |域名解析错误 |
|701          |多次请求索引文件相同 多半是服务器没有更新m3u8文件。这个错误码会在连续10次请求到相同索引文件的情况下发出 |
|800          |音视频数据错误 |
|801          |不支持seek+/- |
|804          |音频不支持 |
|805          |视频不支持 |
|901          |不支持字幕 |
|902          |字幕超时 |
|50811        |付费节目，未购买|

## 5.3 onPlayListChangeError错误码

播放器回调IPlayListStateListener中onPlayListChangeError方法的错误码

|errcode |errmsg |
|------- |-------|
|-1 |获取播单列表失败 |

## 5.4 getPlayListError错误码

播放器回调IGeneralListener中getPlayListError方法的错误码

|errcode |errmsg |
|------- |-------|
|-1 |获取播单列表失败 |

## 5.5 playUrlError错误码

播放器回调IGeneralListener中playUrlError方法的错误码

|errcode |errmsg |
|------- |-------|
|-1 |无网络 |

## 5.6 播放器增加播单失败错误码

 针对播放器增加播单失败的几种错误码

| errcode                          |errmsg |
|-------------------------------|-------|
|10001| 播放内容已下线                       |
|10002| 当前播放已经是最后一首                   |
|10003| 当前播放已经是第一首                    |
|10010| 专辑、AI电台、广播、听电视、直播的id无效(接口返回详情数据异常) |
|10011| 专辑、AI电台、广播、听电视、直播的id无效(服务器错误) |
|10012| 初始化播单失败(接口返回列表数据为空)           |
|10013| 初始化播单失败(服务器错误)                |
|10014| 播放失败(url为空)                   |
|10015| 播放失败(付费专辑，获取加密url失败)          |
|10016| 播放失败(直播url为空)      |
|10020| 获取下一页播单失败(接口返回列表数据为空)         |
|10021| 获取下一页播单失败(服务器错误)              |
|10022| 获取上一页播单失败(接口返回列表数据为空)         |
|10023| 获取上一页播单失败(服务器错误)              |
|10040| 获取下一首失败(播放信息为空)               |
|10041| 获取上一首失败(播放信息为空)               |
|10042| 获取下一首失败(数组角标越界)               |
|10043| 获取上一首失败(数组角标越界)               |
|10044| 获取下一首失败(时间错误，广播或者听电视节目单开始时间未到) |
|10045| 获取上一首失败(时间错误，广播或者听电视节目单开始时间未到) |


# 6 Android SDK相关问题反馈
--

## 6.1 关于接口返回图片URL使用说明
目前接口返回的url均为default.jpg或default.png，例如https://iovimg.radio.cn/mz/images/202301/c59f520f-e9b5-4428-9219-127eb6fae640/default.jpg，开发者可根据实际情况将图片url替换成https://iovimg.radio.cn/mz/images/202301/c59f520f-e9b5-4428-9219-127eb6fae640/250_250.jpg 等不同尺寸资源。
因服务器给出的default名称的图片尺寸会比较大，建议开发者根据实际使用场景酌情进行图片尺寸的替换。
目前服务器支持100\_100，250\_250，340\_340，550\_550四种定制尺寸资源。

## 6.2 关于运营数据的说明
运营数据分人工运营数据和自动运营数据，都是需要根据厂商各自的需要在后台进行个性化配置，该后台目前暂未对外开放，所以需要联系项目经理进行配置。配置时最好根据UI/UE进行定制化配置，可以减少接入成本。

## 6.3 关于使用< 获取整棵栏目树> 接口说明
当使用 [获取整棵栏目树](#3.4.1 获取整棵栏目树) 接口时，如果传入的isWithMembers为true， 会返回整棵栏目树(包含具体的栏目成员)，接口返回```List<ColumnGrp>```的数据对象。如果想得到具体的栏目，需要判断List里面```ChildColumns```成员是否为[Column](#9.4 Column)对象，需要强转，请使用如下代码判断：
 ```ColumnGrp.getChildColumns().get(index) instanceof Column```


# 7 工具类
--

## 7.1 OperationAssister
该工具类主要用来处理运营接口返回的成员Bean对象，根据传入的成员对象获取对应的属性。

### 7.1.1 获取成员的Id
获取栏目成员的id

`OperationAssister.getId(ColumnMember member)`

获取分类成员的id

`OperationAssister.getId(CategoryMember member)`

### 7.1.2 获取收听数
只有分类成员的部分类型有收听数，不支持收听数的返回0。

`OperationAssister.getListenNum(CategoryMember member)`

### 7.1.3 获取成员类型
对应类型见资源类型[ResType](#9.31 ResType)

获取分类成员的类型

`OperationAssister.getType(CategoryMember member)`

获取栏目成员类型

`OperationAssister.getType(ColumnMember member)`

### 7.1.4 获取图标和封面
返回图标或封面的url地址或“”。

获取分类成员的图标

`OperationAssister.getIcon(CategoryMember member)`

获取栏目成员的图标

`OperationAssister.getIcon(ColumnMember member)`

获取分类成员的封面

`OperationAssister.getCover(CategoryMember member)`

获取栏目成员的封面

`OperationAssister.getCover(ColumnMember member)`

获取分类成员的图片，如果没有Icon就返回Cover

`OperationAssister.getImage(CategoryMember member)`

获取栏目成员的图片，如果没有Icon就返回cover

`OperationAssister.getImage(ColumnMember member)`

## 7.2 BeanUtil
该工具类主要用来Bean转换

### 7.2.1 单曲转PlayItem
将单曲转换为[PlayItem](# 9.17 PlayItem)用于播放。

专辑/AI电台单曲转换为播放item

`BeanUtil.translateToPlayItem(AudioDetails audioDetails)`

在线广播节目单曲转换成播放item

`BeanUtil.translateToPlayItem(ProgramDetails details)`

## 7.3 Logging
用于控制log的打印，需要在初始化之前进行调用。

开启全局log

```java
Logging.setDebug(true);//true表示开启log，false表示关闭

```

网络请求信息log。需要在全局log都开启的情况下才有效
开启所有请求信息RequestLevel.ALL；
关闭RequestLevel.NONE；
只打印请求信息RequestLevel.REQUEST；
只打印响应信息RequestLevel.RESPONSE；

```java
Logging.setRequestLevel(RequestLevel.ALL);

```

可以实现`Printer`接口来实现自己的打印方式

```java
Logging.printer(new CustomPrinter());

```

播放器日志输出控制分为2部分，分别是：（1）底层IJKPlayer播放器日志，（2）SDK中播放器管理类日志。

对于底层IJKPlayer播放器日志，其主要作用是监控播放器运行状态，默认是**开启**的，可通过PlayerManager.setLogInValid()关闭该日志，除非有特别的日志控制需求，不需要关闭该日志。

对于SDK中播放器管理类日志，其主要作用是分析SDK播放器问题，默认是**关闭**的，**在APP开发和测试阶段建议开启该日志**，开启方法为：PlayerManager.enableSDKPlayerLog()；**当正式交付版本时，请关闭该日志**，关闭方法为：PlayerManager.disableSDKPlayerLog()。

# 8 数据上报

==注意==：使用SDK播放器，不需要手动处理播放相关的数据上报，播放器会自动处理。

## 8.1 规则

1. SDK轮询批量上报，SDK产生数据后，由SDK批量将数据上报到服务端；如果因为无网等情况，导致上报失败，则将数据保存在SDK本地，加入批量上报的数据队列；
2. SDK每隔60s上报一次（60s为默认值，服务器可以返回上报间隔时间，"小于60s, 大于600s"无效）；
3. 如果产生数据上报时，数据库里面的数据>=10条，上报一次。
   补充说明：SDK上报成功后，返回状态码204。如果上报失败，SDK需要将数据保存在本地，待下次上报时一起上报。对本地保留数据，每次上报时均需添加，直到上报成功，上报成功的数据自动删除。

## 8.2 公共参数

|参数|类型|含义|是否必须|描述|
|:-----|:-----|:-----|:-----|:-----|
| appid       |string     | 应用id         |是    | 创建应用时生成的唯一标识                                     |
| udid        |string     | 设备唯一标识     |是  | 设备激活时使用的deviceid                                     |
| uid         |string     | 用户id         |否    | 用户授权返回的open_uid                                     |
| eventcode   |string     | 事件代码        |是   |                                                              |
| sessionid   |string     | 会话id         |是    | 应用每启动一次，生成一个新的sessionid                        |
| action_id   |string     | 行为序号        |是   | 当前应用每发生一次事件上报，action_id自增1                   |
| timestamp   |string     | 事件发生时间     |是  | 客户端事件发生的时刻，Unix毫秒数时间戳                       |
| wifi        |string     | wifi           |是    | 0：否；1：真                                                 |
| network     |string     | 网络            |是   | -1：未知，0：无网，1：wifi，2：2g，3：3g，4：4g，5：5g       |
| carrier     |string     | 运营商          |是   | 0、未知，1、移动，2、联通，3、电信                           |
| lon         |string     | 经度           |否    | 需用户授权后方可上报                                                             |
| lat         |string     | 维度           |否    | 需用户授权后方可上报                                                             |
| openid      |string     | 车载设备唯一识别 |是  | 激活生成的唯一id                                             |
| car_id      |string     | 车厂账号唯一标识码 |否 | 车厂账号                                                     |
| lib_version |string     | SDK版本          |是  | 当前SDK版本                                                  |
| market_type |string     | 渠道类型        |否   | 0：后装，1：前装                                             |
| appid_type  |string     | 合作方式         |否  | 0：SDK，1：车载APK，2：API，3：H5服务端（初始化接口v2/app/init）传给客户端 |
| oem         |string     | 车厂            |否   | 产品直接落地车辆所属的车厂，后装则为方案商名称               |
| car_brand   |string     | 品牌            |否   |                                                              |
| car_type    |string     | 车型            |否   |    需开发者传入                                                          |
| os          |string     | 操作系统名称      |是     | 传名称，例如android                                          |
| osversion   |string     | 操作系统版本     |是  | 例如5.1.0                                                    |
| screen_direction|string | 屏幕状态        |是   | 0：横屏；1：竖屏                                             |
| screen_height|string    | 分辨率高        |是   |                                                              |
| screen_width|string     | 分辨率宽        |是   |                                                              |
| manufacturer|string     | 设备制造商      |是   | 例如HUAWEI                                                   |
| model       |string     | 设备型号        |是   | 车机型号                                                     |
| playid      |string     | 播放id         |是    | 播放行为发生时，每播放一个音频时，生成一个新的唯一的id，没有音频播放时，<br>可以赋默认值0000 播放器拉取新的播放资源时更新playid，<br>比如点播、播放开始时，playid均更新；选择搜索结果上报时playid更新（取值md5(audioId+System.currentTimeMillis())） |
| channel     |string     | 渠道包名        |是   | 创建应用时使用的包名                         |
| developer   |string     | 开发者id        |是   | 该应用所属的开发者id                                         |

## 8.3 应用启动上报

基于SDK合作方式的启动事件上报时机分为以下两种场景：
（1）有界面的：kill进程后界面再次展示时上报，界面在后台运行一段时间再次进入时不算启动不上报；
（2）无界面的：接口返回内容首次曝光时进行上报，首次为每间隔2小时计算。

| 事件特有参数|类型 | 含义     | 是否必填项 | 描述                            |
| ----------|-- | -------- | ---------- | --------------------------------- |
| type      |string   | 启动类型 | 是         | 0：语音启动，1：launch，2：widget |


**代码示例**

```java
ReportHelper.getInstance().addAppStart("0");  //type 0：语音启动，1：launch，2：widget
```

## 8.4 数据上报事件接口

**接口名称**
`ReportHelper#addEvent(BaseReportEventBean reportEventBean)`

**接口说明**

添加数据上报事件.

**参数说明**

|参数名|类型|含义|是否必须| 描述                                                                                                                                 |
|:---|:---|:---|:---|:-----------------------------------------------------------------------------------------------------------------------------------|
|reportEventBean|BaseReportEventBean|数据上报事件父类|是| BaseReportEventBean是父类，需要传具体的子类，见[播放结束数据上报](#8.5.1 播放结束数据上报)、[在线广播播放中数据上报](#8.5.2 在线广播播放中数据上报)                                     |

**代码示例**

``` java
       /**
       *  上报收听开始事件
       */
       StartListenReportEvent startListenReportEvent = new StartListenReportEvent();
        startListenReportEvent.setAlbumid("专辑id");
        startListenReportEvent.setAudioid("单曲id");
        startListenReportEvent.setRadioid("AI电台id");
        ....
        ReportHelper.getInstance().addEvent(startListenReportEvent);
```
## 8.5 数据上报事件 Model

### 8.5.1 播放结束数据上报
播放结束数据上报``EndListenReportEvent``, 继承自``BaseReportEventBean``。

（专辑、单曲、AI电台、在线广播、听电视）播放结束上报。熄火、闪退、直接退出等当时不上报，待再开机且切换播放内容时上报。

|变量名|类型|含义| 是否必填项| 描述                                                                                                                                                             |
|:-----|:-----|:-----|:-----|:-----|
|audioid|string|单曲id|是| 当前播放单曲id、广播直播节目id或广播回放音频id（没有广播直播节目id时返回null）、听电视直播节目id或听电视回放音频id                                                                                                                 |
|radioid|string  |节目id|是| 当播放专辑/单曲时，radioid为当前播放单曲所属专辑id；<br>当播放AI电台时，radioid为当前AI电台id；<br>注：播放AI电台包括点击AI电台播单的某一专辑单曲或AI电台流自动播放到某一专辑单曲，<br>此时上报的radioid都为当前AI电台id；当播放广播时（无论直播或回放），radioid为当前广播id；当播放听电视时（无论直播或回放），radioid为当前听电视id。 |
|albumid|string|专辑id|是| 当播放专辑/单曲时，albumid为当前播放单曲所属专辑id；当播放AI电台里专辑/单曲时，albumid为当前播放单曲所属专辑id；当播放广播和听电视时，albumid可为null                                                                        |
|type|string |播放类型 |是| 0：离线播放；1：在线播放                                                                                                                                                  |
|position|string|播放器位置|是| 1：SDK自带播放器；2：外部播放器                                                                                                                                             |
|ai_mz_location|string|否|AI电台编排类型| AI电台编排位类型，只有radioid为AI电台时传，0：分类，1：专辑，2：台宣，3：在线广播，4：歌曲，5：个推，6：地域，7：直播                                                                                           |
|playtime|string|播放时长  |是| 播放器实际播放时长，精确到秒                                                                                                                                                 |
|playrate|string|播放比率  |是| 播放时长/单曲总时长，小数，保留小数点后两位                                                                                                                                         |
|length|string|单曲总时长|是| 单曲实际的时长，精确到秒                                                                                                                                                   |
|remarks2|string|播放开始时间|是| 开始播放的时间戳，精确到毫秒                                                                                                                                                 |
|remarks4|string|首次收听  |是| 0：否；1：是                                                                                                                                                        |
|remarks7|string|播放切换原因|是| 1：播放器自动切换；2: 其他；3：手动（点击）；4：语音（ 需开发者传入）；5：方控（ 需开发者传入）。                                                                                                          |
|remarks8|string|播放内容获取方式|是| 1语音点播；2选择搜索结果；3其他                                                                                                                                              |
|remarks9|string|搜索结果追踪号|否| 搜索服务端透传的数据                                                                                                                                                     |
|remarks10|string|是否应用启动首次收听|是| 0：否；1：是                                                                                                                                                        |
|remarks11|string|推荐结果追踪号|否| 推荐服务端透传的数据                                                                                                                                                     |
|audioid_type|string|单曲付费类型|是| 0：免费，1：付费，2：试听                                                                                                                                                 |
|tag|string|标签|是| 精品，VIP，无                                                                                                                                                       |

### 8.5.2 在线广播播放中数据上报
播放中数据上报``BroadcastPlayingReportEvent``, 继承自``BaseReportEventBean``
当广播播放开始后，每隔10秒上报（广播回放时不上报）
新增：听电视（听电视回放时不上报）

|变量名|类型| 含义            |是否必填项|描述|
|:-----|:-----|:--------------|:-----|:-----|
|audioid|string| 节目id          |是||
|radioid|string  | 在线广播id、或者电视id |是||
|playtime|string| 播放时长          |是|精确到秒，播放开始后，每隔10秒上报|


### 8.5.3 推荐展示数据上报
推荐展示数据上报``RecommendShowReportEvent``, 继承自``BaseReportEventBean``
推荐内容展示在应用的可视区域时（包括初始展示和用户操作后的），上报此事件，展示n个推荐内容就上报n次。注：每次请求数据首次展示时，每个推荐内容最多上报一次，不要重复上报；客户端读取本地缓存时不需要上报。

| 变量名     |类型| 含义                                                   |是否必填项|描述|
|:--------|:-----|:-----------------------------------------------------|:-----|:-----|
| type    |string| 填入此值：[SceneDataList](#9.56 SceneDataList)的outputMode |是||
| remarks11 |string  | 填入此值：[SceneData](##9.57 SceneData)的callback                  |是||

### 8.5.4 推荐点击数据上报
推荐点击数据上报``RecommendSelectReportEvent``, 继承自``BaseReportEventBean``
当用户点击推荐的电台或专辑时，上报此事件。

| 变量名     |类型| 含义                                                   |是否必填项|描述|
|:--------|:-----|:-----------------------------------------------------|:-----|:-----|
| type    |string| 填入此值：[SceneDataList](#9.56 SceneDataList)的outputMode |是||
| remarks11 |string  | 填入此值：[SceneData](##9.57 SceneData)的callback                  |是||

# 9 Model
--

## 9.1 BasePageResult&lt;T&gt;
带有分页的返回结果基类

|变量名|类型|描述|
|:-----|:-----|:-----|
|haveNext|int|是否有下一页，1表示有下一页，0表示没有|
|nextPage|int|下一页页码，分页请求下一页需要传入|
|havePre|int|是否有上一页，1表示有，0表示没有|
|prePage|int|上一页页码|
|currentPage|int|当前页码|
|count|int|总行数|
|sumPage|int|总页数|
|pageSize|int|每页个数|
|dataList|T|结果数据|

## 9.2 Success
检查手机号、注册、登录返回信息

|变量名|类型|描述|
|:-----|:-----|:-----|
|code|String|结果的code。等于`Success.CODE_SUCCESS`表示成功；等于`Success.PHONE_NUMBER_IS_EXIST`表示手机号已经注册；等于`Success.PHONE_NUMBER_IS_NOT_EXIST`表示手机号未注册|
|msg|String|结果信息|

## 9.3 ColumnGrp
栏目组

|变量名|类型|描述|
|:-----|:-----|:-----|
|code|String|栏目组的code，请求栏目相关接口会用到。该值不是节目的id，不能用于获取节目信息或播单|
|title|String|标题|
|subtitle|String|副标题|
|description|String|描述|
|imageFiles|Map\<String, [ImageFile](#9.8 ImageFile)\>|图片集合|
|extInfo|Map\<String, String\>|额外信息集合，暂时没用|
|type|String|类型，ColumnGrp-栏目组；Column-栏目。<br>开发者可以不用关注|
|childColumns|List\<? extends ColumnGrp\>|子栏目组|

## 9.4 Column
栏目，继承自栏目组[ColumnGrp](#9.3 ColumnGrp)。

|变量名|类型|描述|
|:-----|:-----|:-----|
|forwardToMore|int|是否支持跳转至更多。<br>1，支持；0，不支持|
|moreColumnMember|[ColumnMember](#9.5 ColumnMember)|跳转至更多的目标栏目成员，<br>一般是分类|
|columnMembers|List\<? extends [ColumnMember](#9.5 ColumnMember)\>|栏目成员|

## 9.5 ColumnMember
栏目成员父类，所有栏目成员都继承该类

|变量名|类型|描述|
|:-----|:-----|:-----|
|code|String|栏目成员的code，请求栏目相关接口会用到。该值不是节目的id，不能用于获取节目信息或播单|
|title|String|标题|
|subtitle|String|副标题|
|description|String|描述|
|cornerMark|int|是否显示角标，1是，0否|
|imageFiles|Map\<String, [ImageFile](#9.8 ImageFile)\>|图片集合|
|extInfo|Map\<String, String\>|额外信息，暂时没用|
|recommendReason|String|推荐理由|
|type|String|类型，开发者可以不用关注|
|componentType|int|组件类型，1：上2下1组件，2：轮播组件，3：上2下3组件，4：上1下1组件，5：单内容大卡组件，6：首页福利活动组件，7：品牌入口组件，10：圆形组件，11：话题大卡组件，12：话题小卡组件，13：品牌主页大卡，14：品牌主页 1+1，15：活动类型组件|
|contentList|List\<[ColumnContent](#9.5.10 ColumnContent)\>|内容列表|


### 9.5.1 AlbumDetailColumnMember
栏目成员-专辑，继承自[ColumnContent](#9.5.10 ColumnContent)

|变量名|类型|描述|
|:-----|:-----|:-----|
|albumId|long|专辑Id|
|playTimes|int|收听数|
|vip|int|是否vip，1：是，0：否|
|fine|int|是否精品，1：是，0：否|

### 9.5.2 AudioDetailColumnMember
栏目成员-单曲，继承自[ColumnContent](#9.5.10 ColumnContent)

|变量名|类型|描述|
|:-----|:-----|:-----|
|audioId|long|单曲Id|
|playTimes|int|收听数|

### 9.5.3 BroadcastDetailColumnMember
栏目成员-在线广播，继承自[ColumnContent](#9.5.10 ColumnContent)

|变量名|类型|描述|
|:-----|:-----|:-----|
|broadcastId|long|在线广播Id|
|playTimes|int|收听数|
|freq|String|在线广播频率|

### 9.5.4 CategoryColumnMember
栏目成员-分类，继承自[ColumnContent](#9.5.10 ColumnContent)

|变量名|类型| 描述                                                                                                                                       |
|:-----|:-----|:-----------------------------------------------------------------------------------------------------------------------------------------|
|categoryCode|String| 分类Code                                                                                                                                   |
|contentType|String| 分类内容类型。1：专辑；2：在线广播；3：直播；4：AI电台；6：新闻模式；7：听电视。<br>该类型与[ResType](#9.31 ResType)不对应，可以使用`getContenResType()`获取与[ResType](#9.31 ResType)对应的类型 |

### 9.5.5 LiveProgramDetailColumnMember
栏目成员-直播，继承自[ColumnContent](#9.5.10 ColumnContent)

|变量名|类型|描述|
|:-----|:-----|:-----|
|liveProgramId|long|直播Id|
|anchor|String|主播|

### 9.5.6 RadioDetailColumnMember
栏目成员-AI电台，继承自[ColumnContent](#9.5.10 ColumnContent)

|变量名|类型|描述|
|:-----|:-----|:-----|
|radioId|long|AI电台Id|
|playTimes|int|收听数|
### 9.5.7 TVDetailColumnMember
栏目成员-听电视，继承自[ColumnContent](#9.5.10 ColumnContent)

|变量名|类型|描述|
|:-----|:-----|:-----|
|listenTVid|long|听电视Id|
|playTimes|int|收听数|
|tvSort|int|用来区分听电视类型（音乐，交通，新闻等）|

### 9.5.8 SearchResultColumnMember
栏目成员-搜索结果，继承自[ColumnContent](#9.5.10 ColumnContent)

|变量名|类型|描述|
|:-----|:-----|:-----|
|keyword|String|关键词|

### 9.5.9 WebViewColumnMember
栏目成员-web页面，继承自[ColumnContent](#9.5.10 ColumnContent)

|变量名|类型|描述|
|:-----|:-----|:-----|
|url|String|页面链接|

### 9.5.10 ColumnContent
栏目成员-内容，继承自[ColumnMember](#9.5 ColumnMember)

| 变量名 | 类型                                                 | 描述                   |
|:----|:---------------------------------------------------|:---------------------|
| id  | String                                             | 资源id                 |
| canPlay  | int                                                | 是否可以播放，0：不可播放；1：可以播放 |
| brandTitle  | String                                             | 品牌电台标题 |
| tag  | String                                             | 标签 |
| tagColor  | String                                             | 标签颜色 |
|columnMemberChildContents| List\<[ColumnContentChild](#9.5.11 ColumnContentChild)\> |子内容合集|

### 9.5.11 ColumnContentChild
| 变量名             | 类型                     | 描述                                                                                                               |
|:----------------|:-----------------------|:-----------------------------------------------------------------------------------------------------------------|
| id              | long                   | 资源id                                                                                                             |
| code            | String                 | 栏目成员的code值，用于获取子栏目成员。该值是可变的。                                                                                     |
| title           | String                 | 标题                                                                                                               |
| description     | String                 | 描述                                                                                                               |
| cornerMark      | String                 | 是否显示角标，1是，0否                                                                                                     |
| recommendReason | String                 | 推荐理由                                                                                                             |
| tag             | String                 | 标签                                                                                                               |
| updateDate      | long                   | 更新时间                                                                                                             |
| imageFiles      | Map<String, ImageFile> | 内容图片                                                                                                             |
| extInfo         | String                 | 拓展信息                                                                                                             |
| playTimes       | String                 | 播放量                                                                                                              |
| canPlay         | int                    | 是否可以播，0-不可播放 1-可以播放                                                                                              |
| broadcastSort   | String                 | 广播类型，6：交通台，7：经济台，8：新闻台，9：音乐台，10：校园台，11：娱乐台，12：方言台，13：曲艺台，14：外语台，15：文艺台，16：旅游台，17：体育台，18：生活台，19：都市台，20：综合台，21：民族台 |
| fine            | int                    | 专辑是否精品，1：是；0：否                                                                                                   |
| vip             | int                    | 专辑是否vip，1：是；0：否                                                                                                  |
| freq            | String                 | 广播频率                                                                                                             |
| categoryCode            | String                 | 分类code                                                                                                           |
| contentType            | String                 | 分类内容类，0：综合 ，1：专辑 ，2：广播 ，4：AI电台                                                                                                           |
| pageId            | String                 | 跳转页面id                                                                                                               |

## 9.6 Category
分类，该类型下面可以有子分类。

|变量名|类型| 描述                                                                                                                                                        |
|:-----|:-----|:----------------------------------------------------------------------------------------------------------------------------------------------------------|
|code|String| 分类的code，请求分类相关接口会用到。该值不是节目的id，不能用于获取节目信息或播单                                                                                                               |
|type|String| 类型。开发者无需关心，会一直为null                                                                                                                                       |
|name|String| 分类名                                                                                                                                                       |
|description|String| 描述                                                                                                                                                        |
|contentType|int| 分类内容类型。<br>0：综合；1：专辑；2：在线广播；3：直播；<br>4：AI电台；<br>6：新闻模式；7：听电视。<br>该类型与[ResType](#9.31 ResType)不对应，可以使用`getContenResType()`获取与[ResType](#9.31 ResType)对应的类型 |
|childCategories|List\<Category\>| 子分类                                                                                                                                                       |
|imageFiles|Map\<String, [ImageFile](#9.8 ImageFile)\>| 图片集合                                                                                                                                                      |
|extInfo|Map\<String, String\>| 额外信息，暂时没用                                                                                                                                                 |

### 9.6.1 LeafCategory
叶子分类，继承自分类，该类型下面可以有分类成员。

|变量名|类型|描述|
|:-----|:-----|:-----|
|categoryMembers|List&lt;CategoryMember&gt;|分类成员列表|

## 9.7 CategoryMember
分类成员父类，所有分类成员都继承该类

|变量名|类型|描述|
|:-----|:-----|:-----|
|code|String|分类成员的code，请求分类相关接口会用到。该值不是节目的id，不能用于获取节目信息或播单|
|title|String|标题|
|subtitle|String|副标题|
|description|String|描述|
|imageFiles|Map\<String, [ImageFile](#9.8 ImageFile)\>|图片集合|
|extInfo|Map\<String, String\>|额外信息，暂时没用|
|type|String|类型，是对应分类成员子类的类名或自定义名称。<br>开发者可以不用关注|

### 9.7.1 AlbumCategoryMember
运营分类成员-专辑，继承自[CategoryMember](#9.7 CategoryMember)

|变量名|类型|描述|
|:-----|:-----|:-----|
|albumId|long|专辑id|
|playTimes|int|收听数|
|vip|int|是否vip，1：是，0：否|
|fine|int|是否精品，1：是，0：否|

### 9.7.2 BroadcastCategoryMember
运营分类成员-在线广播，继承自[CategoryMember](#9.7 CategoryMember)

|变量名|类型|描述|
|:-----|:-----|:-----|
|broadcastId|long|在线广播id|
|playTimes|int|收听数|
|freq|String|在线广播频率|

### 9.7.3 LiveProgramCategoryMember
运营分类成员-直播，继承自[CategoryMember](#9.7 CategoryMember)

|变量名|类型|描述|
|:-----|:-----|:-----|
|liveProgramId|long|直播id|

### 9.7.4 RadioCategoryMember
运营分类成员-AI电台，继承自[CategoryMember](#9.7 CategoryMember)

|变量名|类型|描述|
|:-----|:-----|:-----|
|radioId|long|AI电台id|
|playTimes|int|收听数|

### 9.7.5 TVCategoryMember
运营分类成员-听电视，继承自[CategoryMember](#9.7 CategoryMember)

|变量名|类型|描述|
|:-----|:-----|:-----|
|listenTVid|long|听电视id|
|playTimes|int|收听数|
|freq|String|频率|

### 9.7.6 AudioDetailTbCategoryMember
运营分类成员-单曲，继承自[CategoryMember](#9.7 CategoryMember)

|变量名|类型|描述|
|:-----|:-----|:-----|
|audioId|long|单曲id|
|playTimes|long|收听数|
|duration|long|时长|
|updateTime|string|更新时间，格式为yyyy-MM-dd HH:mm:ss|
|text|string|音频文本|
|newsSource|string|新闻来源|
|mp3PlayUrl32|string|格式mp3，码率32k，播放地址|
|mp3PlayUrl64|string|格式mp3，码率64k，播放地址|
|aacPlayUrl|string|格式aac的默认播放地址|
|aacPlayUrl32|string|格式aac，码率32k，播放地址|
|aacPlayUrl64|string|格式aac，码率64k，播放地址|
|aacPlayUrl128|string|格式aac，码率128k，播放地址|
|aacPlayUrl320|string|格式aac，码率320k，播放地址|
|aacFileSize|long|aac文件大小|
|mp3FileSize32|long|格式mp3，码率32k，文件大小|
|mp3FileSize64|long|格式mp3，码率64k，文件大小|

## 9.8 ImageFile
图片文件

|变量名|类型| 描述                                                                                                 |
|:-----|:-----|:---------------------------------------------------------------------------------------------------|
|url|String| 图片地址                                                                                               |
|width|int| 图片的宽                                                                                               |
|height| int | 图片的高                                                                                               |
|type|String| 图片的类型，<br>等于`ImageFile.KEY_ICON`表示图标，<br>等于`ImageFile.KEY_COVER`表示封面，<br>等于`ImageFile.KEY_BG`表示背景图 |

## 9.9 VoiceSearchResult
语音搜索返回结果。playType为1, playIndex为0, 直接播放第一个结果.

|变量名|类型|描述|
|:-----|:-----|:-----|
|playType|int|播放类型，<br>0：选择播放；1：直接播放；2：延时播放|
|playIndex|int|播放音频下标，下标以0开始；<br>大于0，表示列表播放，取到此下标位置|
|delayTime|int|播放延迟时间单位为ms，playTime为2时有效|
|programList|List&lt;[VoiceSearchProgramBean](#9.9.1 VoiceSearchProgramBean)&gt;|结果数据|

## 9.9.1 VoiceSearchProgramBean
语音搜索返回的节目数据

|变量名|类型|描述|
|:-----|:-----|:-----|
|id|long|节目id|
|name|String|节目名称|
|img|String|节目图片链接|
|comperes|List&lt;[Compere](#9.10.1 Compere)&gt;|主持人信息列表|
|type|int|节目类型，0-专辑，1-单曲，3-AI电台，11-在线广播，12-听电视。见[ResType](#9.31 ResType)|
|albumName|String|专辑名称|
|duration|long|时长|
|playUrl|String|播放地址，已放弃。未使用SDK播放器，请参照[3.8.3 根据单曲播放id请求播放的url地址](#3.8.3 根据单曲播放id请求播放的url地址)|
|vip|int|是否vip，1-是，0-否|
|fine|int|是否付费，1-是，0-否|
|audition|int|是否试听，1-是，0-否|

## 9.10 SearchProgramBean
文本搜索返回的节目数据

|变量名|类型|描述|
|:-----|:-----|:-----|
|id|long|节目id|
|name|String|节目名称|
|img|String|节目图片链接|
|comperes|List&lt;[Compere](#9.10.1 Compere)&gt;|主持人信息列表|
|type|int|节目类型，0-专辑，1-单曲，3-AI电台，11-在线广播，12-听电视。见[ResType](#9.31 ResType)|
|albumName|String|专辑名称|
|duration|long|时长|
|playUrl|String|播放地址，已放弃。未使用SDK播放器，请参照[3.8.3 根据单曲播放id请求播放的url地址](#3.8.3 根据单曲播放id请求播放的url地址)|
|vip|int|是否vip，1-是，0-否|
|fine|int|是否付费，1-是，0-否|
|audition|int|是否试听，1-是，0-否|
|listenNum|long|播放量|
|highlight|List&lt;[HighLightWord](#9.10.2 HighLightWord)&gt;|高亮展示|

### 9.10.1 Compere
主持人信息

|变量名|类型|描述|
|:-----|:-----|:-----|
|name|String|主持人姓名|
|des|String|主持人描述|
|img|String|主持人图片|

### 9.10.2 HighLightWord
高亮展示信息

|变量名|类型|描述|
|:-----|:-----|:-----|
|field|String|所在字段|
|start|int|起始位置|
|offset|int|位移|
|token|String|展示字符|

## 9.11 AlbumDetails
专辑详细信息

|变量名 | 类型 | 描述                                          |
|:------------ | :------------- |:--------------------------------------------|
|id | long  | 专辑id                                        |
|name | String  | 专辑名称                                        |
|img | String  | 专辑封面URL                                     |
|desc | String  | 专辑简介                                        |
|listenNum | long  | 收听数                                         |
|followedNum | long  | 订阅数                                         |
|countNum | int  | 总期数                                         |
|isOnline | int  | 是否上线，0-否，1-是                                |
|sortType | int | 默认热度排序，1-正序，0-倒序                            |
|hasCopyright | int  | 是否有版权，0-否 ,1-有                              |
|host |List&lt;[Host](#9.14 Host)&gt;| 主持人信息组                                      |
|status | String  | 完结状态描述，更新中，已完结                              |
|updateDay | String  | 更新时间描述，不定期更新，每天更新，每周更新，每月更新，周一到周四更新，周一到周五更新 |
|keyWords | List&lt;String&gt;  | 关键词，仅用于展示。示例：["资讯","智能硬件","智东西"]            |
|commentNum | int  | 评论数，已废弃                                         |
|lastCheckDate | long  | 最新单曲更新时间                                    |
|type | String  | 类型，0-专辑。见[ResType](#9.31 ResType)           |
|isSubscribe | int  | 是否订阅,1-是，0-否                                |
|breakPointContinue | String  | 收听历史续播类型，1-建议断点续播 2-建议播放最新单曲，如资讯类节目         |
|noSubscribe | int  | 是否支持订阅，1-不支持，0-支持                           |
|vip | int  | 是否vip，1-是，0-否                               |
|fine | int  | 是否精品，1-是，0-否                                |
|songNeedPay | int  | 是否单曲付费，1-是，0-否                              |
|buyStatus | int  | 是否已购，专辑付费的购买状态，1-已购买，0-未购买                  |
|payMethod |List&lt;[AlbumDetailsPayMethod](#9.46 AlbumDetailsPayMethod)&gt;| 支付方式                                        |

## 9.12 RadioDetails
AI电台详细信息

|变量名 | 类型 | 描述                                  |
|:------------ | :------------- |:------------------------------------|
|id|long | AI电台ID                              |
|name | String  | AI电台名称                              |
|img | String  | AI电台封面URL                           |
|type | String  | 类型，3-AI电台。见[ResType](#9.31 ResType) |
|followedNum | long  | 订阅数                                 |
|isOnline | int  | 是否在线，0-否，1-是                        |
|listenNum | long  | 收听数                                 |
|desc | String  | AI电台简介                              |
|isSubscribe | int  | 是否订阅，0-否，1-是                        |
|host | List&lt;[Host](#9.14 Host)&gt; | 主持人信息组                              |

## 9.13 AudioDetails
单曲详细信息

|变量名 | 类型 | 描述                                               |
|:------------ | :------------- |:-------------------------------------------------|
|audioId | long  | 单曲id                                             |
|audioName | String  | 单曲名称                                             |
|audioPic | String  | 单曲图片URL                                          |
|audioDes | String  | 单曲简介                                             |
|listenNum | long  | 收听数                                              |
|likedNum | long  | 点赞数，已废弃                                              |
|commentNum | int  | 评论数，已废弃                                              |
|orderNum | int  | 期数                                               |
|mp3PlayUrl32 | String  | 32码率mp3格式地址，已废弃，报时还在用                                  |
|mp3PlayUrl64 | String  | 64码率mp3格式地址，已废弃，报时还在用                                      |
|aacPlayUrl | String  | aac格式地址，已废弃，报时还在用                                          |
|aacPlayUrl32 | String  | 32码率aac格式地址，已废弃，报时还在用                                      |
|aacPlayUrl64 | String  | 64码率aac格式地址，已废弃，报时还在用                                      |
|aacPlayUrl128 | String  | 128码率aac格式地址，已废弃，报时还在用                                     |
|aacPlayUrl320 | String  | 320码率aac格式地址，已废弃                                     |
|mp3FileSize32 | int  | 32码率mp3格式文件大小，已废弃，报时还在用                                    |
|mp3FileSize64 | int  | 64码率mp3格式文件大小，已废弃，报时还在用                                    |
|aacFileSize | int  | aac格式文件大小，已废弃，报时还在用                                        |
|host | List&lt;[Host](#9.14 Host)&gt; | 主持人信息组                                           |
|updateTime | long  | 更新时间                                             |
|hasCopyright | int  | 是否有版权，0-否，1-有，已废弃                                    |
|clockId | String  | 时间标记，已废弃                                             |
|originalDuration | int  | 音频文件原始时长，不带片花，已废弃                                    |
|duration | int  | 音频文件默认时长                                         |
|trailerStart | long  | 片花开始位置，已废弃                                           |
|trailerEnd | long  | 片花结束位置，已废弃                                           |
|albumId | long  | 专辑id                                             |
|albumName | String  | 专辑名称                                             |
|albumPic | String  | 专辑封面URL                                          |
|contentType | int | 编排位内容类型0：分类,1：专辑,2：台宣,3：在线广播,4：歌曲,5：个推,6：地域,9：直播。已废弃 |
|contentTypeName | String | 内容类型名称，已废弃                                           |
|mainTitleName | String | 内容主标题名称，已废弃                                          |
|subheadName | String | 内容副标题名称，已废弃                                          |
|hasNextPage | int | 是否有下一页，0表示没有，1表示有。再播放AI电台时，当为0时不再拉取数据。已放弃           |
|albumIsFine | int | 是否精品专辑，1-是，0-不是                                  |
|albumIsVip | int | 是否VIP专辑，1-是，0-不是                                 |
|songNeedPay | int | 单曲是否付费，1-是，0-不是，已放弃                                  |
|audition | int | 单曲是否试听，1-是，0-不是，已放弃                                  |
|playUrlId | String | 播放id，AI电台不需要此字段                                  |
|playInfoList | List&lt;[AudioFileInfo](#9.48 AudioFileInfo)&gt;  | 音频文件信息（只有AI电台使用）          |
|payMethod |List&lt;[AlbumDetailsPayMethod](#9.46 AlbumDetailsPayMethod)&gt;| 支付方式                                        |
|buyType | int | 购买类型 0-免费，1-试听，2-单曲购买 3-专辑购买 4-vip                                  |
|buyStatus | int | 是否购买 0-未购买；1-已购买                                  |

## 9.14 Host
主持人信息

|变量名|类型|描述|
|:-----|:-----|:-----|
|name|String|主持人姓名|
|des|String|主持人描述|
|img|String|主持人图片|

## 9.15 BroadcastDetails
在线广播详细信息

|变量名 | 类型 | 描述                    |
|:------------ | :------------- |:----------------------|
|broadcastId | long  | 在线广播id                |
|name | String  | 在线广播名称                |
|img | String  | 在线广播封面URL             |
|classifId | int  | 在线广播类型id              |
|classifyName | String  | 在线广播类型名称              |
|isSubscribe | int  | 是否订阅,1-是，0-否          |
|playUrl | String  | 直播流地址, 已放弃            |
|playInfoList | List&lt;[AudioFileInfo](#9.48 AudioFileInfo)&gt;  | 音频文件信息，未使用SDK播放器需要使用  |
|onLineNum | int  | 在线收听数                 |
|likeNum | long | 赞数                    |
|status | int  | 上下线状态,1-上线0-下线        |
|roomId | int  | 直播间id                 |
|freq | String | 在线广播频段                |
|icon | String | 在线广播标                 |
|programEnable | int | 广播回放的状态 1开启节目单回放可播，0关闭节目单，2开启节目单回放不可播 |

## 9.16 ProgramDetails
在线广播节目详细信息

|变量名 | 类型 | 描述                                                  |
|:------------ | :------------- |:----------------------------------------------------|
|programId | long | 节目id                                                |
|broadcastId | long | 在线广播id                                              |
|nextProgramId | long | 下一期节目id，默认为-1                                       |
|preProgramId | long | 上一期节目id，默认为-1                                       |
|title | String | 节目名称                                                |
|backLiveUrl | String | 回听地址，已放弃，参考backPlayInfoList                                                |
|playUrl | String | 直播流地址，已放弃                                               |
|backPlayInfoList | List&lt;[AudioFileInfo](#9.48 AudioFileInfo)&gt;  | 回听地址信息，未使用SDK播放器需要使用|
|comperes | String | 主播名称                                               |
|begIntime | String| 节目展示开始时间，如“11:00”                                  |
|endTime | String | 节目展示结束时间，如“12:00”                                  |
|startTime | long | 节目开始时间，单位毫秒                                        |
|finishTime | long | 节目结束时间，单位毫秒                                        |
|status | int | 播放状态，1-直播中，2-回放，3-未开播                              |
|isSubscribe| int | 是否预定节目，0-未预定，1-预定                                  |
|desc | String | 节目简介                                               |
|broadcastDesc | String | 在线广播简介                                             |
|broadcastName | String | 在线广播名称                                             |
|broadcastImg | String | 在线广播图片                                             |
|icon|String| 节目图标url                                            |
|programEnable | int | 广播回放的状态,已废弃，参考[BroadcastDetails](#9.15 BroadcastDetails)的programEnable |

## 9.17 PlayItem
播放对象，是一个抽象类，所有需要播放的东西都要转换成该对象的子类。

|变量名 | 类型 | 描述|
|:------------ | :------------- | :------------|
|type|int|单曲类型|
|audioId|long|单曲Id|
|playUrl|String|播放地址|
|playUrlId|String|播放地址Id|
|position|int|当前播放位置|
|buyStatus | int  | 是否已购，专辑付费的购买状态，1-已购买，0-未购买 |
|buyType | int  | 购买类型 0-免费，1-专辑购买，2-单曲购买 |
|payMethod |List&lt;[AlbumDetailsPayMethod](#9.35 AlbumDetailsPayMethod)&gt;| 支付方式 |
|audition | int | 单曲是否试听，1-是，0-不是 |
|fine | int | 是否精品专辑，1-是，0-不是 |
|vip | int | 是否VIP专辑，1-是，0-不是 |
|duration|int|单曲时长|
|title|String|单曲名称|
|albumId|long|单曲所属专辑Id|
|albumTitle|String|单曲所属专辑名称|
|picUrl|String|当前单曲图片地址|
|hosts|String|当前单曲主播|
|isLiving|boolean|是否是直播中|
|beginTime|String|单曲开始时间|
|endTime|String|单曲结束时间|
|updateTime|String|单曲更新时间|

## 9.18 AlbumPlayItem
专辑-播放对象，继承自PlayItem。

|变量名 | 类型 | 描述|
|:------------ | :------------- | :------------|
|mPlayUrlData|PlayUrlData|播放url相关数据|
|mOfflineInfoData|OfflineInfoData|离线相关数据|
|mInfoData|InfoData|信息相关数据|
|mAlbumInfoData|AlbumInfoData|所属专辑信息|

## 9.19 RadioPlayItem
AI电台-播放对象，继承自PlayItem。

|变量名 | 类型 | 描述|
|:------------ | :------------- | :------------|
|mPlayUrlData|PlayUrlData|播放url相关数据|
|mInfoData|InfoData|信息相关数据|
|mRadioInfoData|RadioInfoData|所属AI电台信息|

## 9.20 BroadcastPlayItem
广播-播放对象，继承自PlayItem。

|变量名 | 类型 | 描述                        |
|:------------ | :------------- |:--------------------------|
|mInfoData|InfoData| 信息相关数据                    |
|mTimeInfoData|TimeInfoData| 播放时间相关数据                  |
|frequencyChannel|String| 广播频段                      |
|status|int| 在线广播播放状态，1-直播中，2-回放，3-未开播 |
|programEnable | int | 广播回放的状态 1开启节目单回放可播，0关闭节目单，2开启节目单回放不可播       |

## 9.21 TempTaskPlayItem
临时任务播放对象，继承自PlayItem。

|变量名 | 类型 | 描述|
|:------------ | :------------- | :------------|
|tempTaskType|int|临时任务类型|
|isNeedPlayStateCallBack|boolean|是否需要临时任务播放状态回调|
|playerIsPlaying|boolean|播放器是否正在播放|
|mPlayStateListener|BasePlayStateListener|播放状态监听|



## 9.22 PlayUrlData
播放url- 数据类。

|变量名 | 类型 | 描述|
|:------------ | :------------- | :------------|
|defaultPlayUrl|String|默认url|
|aacPlayUrl32|String|AAC 32kbp/s码率播放地址|
|aacPlayUrl64|String|AAC 64kbp/s码率播放地址|
|aacPlayUrl128|String|AAC 128kbp/s码率播放地址|
|aacPlayUrl320|String|AAC 320kbp/s码率播放地址|
|mp3PlayUrl32|String|mp3 32kbp/s码率播放地址|
|mp3PlayUrl64|String|mp3 64kbp/s码率播放地址|
|mp3PlayUrl128|String|mp3 128kbp/s码率播放地址|
|mp3PlayUrl320|String|mp3320kbp/s码率播放地址|
|mp3PlayUrl|String|mp3播放url|
|m3u8PlayUrl|String|m3u8播放url|

## 9.23 OfflineInfoData
离线信息-数据类。

|变量名 | 类型 | 描述|
|:------------ | :------------- | :------------|
|offlineUrl|String|离线url|
|isOffline|boolean|是否为离线|
|offlinePlayUrl|String|离线地址|
|fileSize|long|文件大小|
|albumOfflinePic|String|专辑下载图片地址|

## 9.24 InfoData
播放信息-数据类。

|变量名 | 类型 | 描述|
|:------------ | :------------- | :------------|
|title|String|单曲名称|
|albumId|long|单曲所属专辑Id|
|albumPic|String|专辑图片|
|albumName|String|专辑名称|
|dataSrc|String|当前播单对象来源|
|icon|String|来源icon url
|audioPic|String|单曲图片|
|audioDes|String|单曲描述|
|orderNum|int|期数|
|hosts|int|主播|
|isLiked|int|是否订阅|
|updateTime|String|单曲更新时间|
|createTime|long|单曲创建时间|
|sourceLogo|String|第三方来源logo|
|sourceName|String|第三方来源名称|

## 9.25 AlbumInfoData
专辑信息类

|变量名 | 类型 | 描述|
|:------------ | :------------- | :------------|
|followedNum | long  | 订阅数|
|countNum | int  | 总期数|
|breakPointContinue | String  | 收听历史续播类型，1-建议断点续播 2-建议播放最新单曲，如资讯类节目 |

## 9.26 RadioInfoData
AI电台信息类

|变量名 | 类型 | 描述|
|:------------ | :------------- | :------------|
|followedNum | long  | 订阅数|
|radioId |long|AI电台id|
|followedNum |long|专辑订阅数|
|countNum |long|总期数|
|listenNum |long|收听数|
|radioName |String|AI电台名称|
|clockId |String||
|isThirdParty |int|是否为第三方源 1为是第三方 0为不是|
|radioSubTag |String|内容类型 标题|
|radioSubTagType |int|内容类型, 0:分类,1:专辑,2:台宣,3:在线广播,4:歌曲,5:个推,6:地域,7:直播|
|mainTitleName |String|内容主标题名称|
|subheadName |String|内容副标题名称|
|callBack|String|推荐callback|
|source |String|来源 |
|categoryId |long|分类id|
|radioPic |String|AI电台图片|
|radioType |int|AI电台类型|
|adZoneChooseType|int|1，表示广告位ID配置在AI电台详情中；2，表示广告位配置在编排位中。|
|adZoneId|int|广告位Id|

## 9.27 TimeInfoData
时间-数据类

|变量名 | 类型 | 描述|
|:------------ | :------------- | :------------|
|startTime | long  |开始时间|
|finishTime |long|结束时间|
|beginTime |String|使用开始时间|
|endTime |String|使用结束时间|
|curSystemTime |long|服务器当前时间|

## 9.28 PlaylistInfo
播单信息类

|变量名 | 类型 | 描述|
|:------------ | :------------- | :------------|
|mAlbumName|String|专辑名称|
|mAlbumPic|String|专辑图片|
|mBreakPointContinue | String  | 收听历史续播类型，1-建议断点续播 2-建议播放最新单曲，如资讯类节目 |
|mPageIndex|String|页码|
|mAllSize|int|总数|
|mNextPage|int|下一页|
|mPrePage|int|上一页|
|hasNextPage | boolean | 是否有下一页|
|hasPrePage | boolean | 是否有上一页|
|listenNum | long  | 收听数|
|followedNum | long  | 订阅数|
|countNum | int  | 总期数|
|broadcastChannel | String | 在广播频道|
|sourceLogo|String|第三方来源logo|
|sourceName|String|第三方来源名称|
|mTempId |String|播放item的id|
|mTempChildId |String |播放单曲id|
|radioType |int|AI电台类型|
|adZoneChooseType|int|1，表示广告位ID配置在AI电台详情中；2，表示广告位配置在编排位中。|
|adZoneId|int|广告位Id|
|noSubscribe | int  | 是否支持订阅，1-不支持，0-支持 |
|programEnable | int | 广播回放的状态 1开启节目单回放可播，0关闭节目单，2开启节目单回放不可播|


## 9.29 LiveInfoDetail
直播节目详信息

| 变量名| 类型| 描述                       |
| --------| :-----|:-------------------------|
| liveName| String| 直播名称                     |
| liveId| long| 直播id                     |
| liveDesc | String| 直播描述                     |
| programId | long | 节目id                     |
| programName | String | 节目名称                     |
| programDesc | String | 节目描述                     |
| comperes | String | 主播                       |
| liveUrl | String | 直播流地址，已放弃，参考playInfoList |
| programPic | String | 节目图片                     |
| livePic | String | 直播图片                     |
| timeLength | String | 字符串格式的时长                 |
| startTime | long | 时间戳格式的开始时间               |
| finishTime | long | 时间戳格式的结束时间               |
| serverTime | long | 服务器时间                    |
| duration | int | 毫秒时长                     |
| roomId | String | 聊天室id                    |
| comperesId|long | 主播Id                     |
|playInfoList | List&lt;[AudioFileInfo](#9.48 AudioFileInfo)&gt;  | 直播地址信息，未使用SDK播放器需要使用     |

## 9.30 ChatRoomTokenDetail
聊天室Token详细信息，包含用户信息

| 变量名| 类型 | 描述|
| -------- | :-----| :-----  |
| accid| long | 进入聊天室的id|
| token | String | 进入聊天室的token|
| nickName | String | 昵称 |

## 9.31 ResType
资源类型

|常量名|类型|描述|
|:----|:----|:----|
|TYPE&#95;INVALID|int|无类型|
|TYPE&#95;ALBUM|int|专辑|
|TYPE&#95;RADIO|int|AI电台|
|TYPE&#95;BROADCAST|int|在线广播|
| TYPE&#95;LIVE    |int|直播|
| TYPE&#95;AUDIO   |int|单曲|
| TYPE&#95;SEARCH  |int|搜索结果|
| TYPE&#95;URL     |int|URL|
| TYPE&#95;CATEGORY|int|分类|
| TYPE_NEWS        |int|新闻模式|
| TYPE_TV          |int|听电视|
| TYPE&#95;ALL     |int|综合，包括所有类型|

## 9.32 SubscribeInfo
订阅信息类

| 变量名| 类型| 描述                                                                                                       |
| :-------| :-----|:---------------------------------------------------------------------------------------------------------|
|id|long| 订阅的专辑、AI电台、在线广播、听电视等id                                                                                   |
|name|String| 订阅的专辑、AI电台、在线广播、听电视等名称                                                                                   |
|type|int| 订阅的资源类型。0：专辑，3：AI电台，5：单曲，11：在线广播，12：听电视. 与[ResType](#9.31 ResType)不一致，为了统一类型，请使用getContentResType()获取类型。 |
|img|String| 封面图片url                                                                                                  |
|updateTime|long| 最新更新时间，时间戳，毫秒                                                                                            |
|newNum|int| 最新期数                                                                                                     |
|newTitle|String| 最新节目的标题                                                                                                  |
|updateNum|int| 一直为0                                                                                                     |
|isOnline|int| 是否在线，1表示在线                                                                                               |
|hasCopyright|int| 是否有版权，1表示有                                                                                               |
|time|String| 专辑更新时间                                                                                                   |
|desc|String| 描述                                                                                                       |
|countNum|int| 专辑总期数                                                                                                    |
|comperes|String| 主持人名称                                                                                                    |
|freq|String| 在线广播频率                                                                                                   |
|playCount|long| 在线广播收听次数                                                                                                 |
|vip|int| 专辑是否VIP，1-是，0-否                                                                                          |
|fine|int| 专辑是否精品，1-是，0-否                                                                                           |
|createdTime|long| 订阅时间，时间戳，单位毫秒                                                                                            |
|subscribeCount|long| 订阅量                                                                                                      |

## 9.33 SceneInfo
场景信息类

| 变量名      | 类型   | 描述                                                                                                                 |
| ----------- | ------ |--------------------------------------------------------------------------------------------------------------------|
| code        | int    | 场景类型编号.10000代表可推送，10001代表不推送                                                                                       |
| icon        | String | 场景icon                                                                                                             |
| message     | String | 场景信息                                                                                                               |
| contentId   | long   | 该场景的节目id                                                                                                           |
| contentName | String | 该场景的节目标题                                                                                                           |
| contentType | int    | 该场景的节目类型内容类型。1：专辑，2：在线广播，3：直播，4：AI电台， 6.新闻模式，7：听电视。与[ResType](#9.31 ResType)不一致，为了统一类型，请使用getContentResType()获取类型。 |

### 9.33.1 AccScene
点火场景对象参数，调用场景推送接口时需要传入，该对象不需要参数。

### 9.33.2 SpeedScene
速度场景对象参数，调用场景推送接口时需要传入，该对象需要传入对应的速度参数。

| 变量名     | 类型   | 描述                                     |
| ---------- | ------ | ---------------------------------------- |
|type|String|速度类型。<br>`SpeedScene.TYPE_LOW_SPEED`-堵车；<br>`SpeedScene.TYPE_MEDIUM_SPEED`-中速行驶；<br>`SpeedScene.TYPE_HIGH_SPEED`-高速行驶|


## 9.34 ListeningHistory
收听历史类

| 变量名     | 类型   | 描述                                                         |
| ---------- | ------ |------------------------------------------------------------|
| audioId    | String | 单曲id                                                       |
| audioTitle | String | 单曲标题                                                       |
| createTime | long   | 创建时间                                                       |
| duration   | int    | 单曲时长                                                       |
| orderNum   | int    | 期数                                                         |
| picUrl     | String | 图片地址                                                       |
| playUrl    | String | 播放地址                                                       |
| playedTime | long   | 已播时长                                                       |
| radioId    | String | AI电台/专辑 id                                                 |
| radioTitle | String | AI电台标题                                                     |
| shareUrl   | String | 分享链接                                                       |
| status     | int    | 节目状态                                                       |
| type       | int    | 节目类型 0：专辑，1：单曲，3：AI电台，11：在线广播，12：听电视，见[ResType](#9.31 ResType) |
| updateTime | long   | 更新时间                                                       |
| timeStamp | long   | 产生记录的时间戳，保存收听历史接口上传的值                                      |
| vip | int   | 专辑是否VIP，1-是，0-否                                            |
| fine | int   | 专辑是否精品，1-是，0-否                                             |
| online | int   | 是否在线，1-是，0-否                                               |
| freq | String   | 在线广播频率                                                     |
| broadcastSort | int   | 广播内容类型（音乐，交通，新闻等）                                          |
| listenCount | long   | 播放量                                                        |

## 9.35 UserInfo
云听账号用户信息

|变量名 | 类型 | 描述|
|:------------ | :------------- | :-----------|
|nickName|String|用户昵称|
|avatar|String|用户头像|
|vip|int|是否vip 1-是，0-否 |
|vipTime|String|到期时间，格式：yyyy-MM-dd|
|vipRemainDays|int| n天后到期            |
|gender|String| 用户性别             |
|userArea|String| 用户地区             |

## 9.36 QRCodeInfo
云听账号二维码等相关信息

|变量名 | 类型 | 描述|
|:------------ | :------------- | :------------|
|code|String|用于登录云听车载版的Code值|
|qrCodePath|String|登录二维码链接地址|
|status|String|二维码状态|
|uuid|String|二维码唯一标识|
|STATUS\_NORMAL|int常量|正常状态. 等待扫描|
|STATUS\_LOSE\_EFFICACY|int常量|二维码过期，需要重新请求二维码|
|STATUS\_AUTHORIZATION|int常量|二维码已授权|
|STATUS\_SCANED|int常量|已被扫描，还未登录|

## 9.37 BrandDetails
品牌信息类

| 变量名        | 类型   | 描述         |
| ------------- | ------ | ------------ |
| brand         | String | 品牌名       |
| logo          | String | 品牌logo地址 |
| userAgreement | String | 服务协议地址 |

## 9.38 SoundQuality
音质常量

| 常量名           | 类型 | 描述     |
| ---------------- | ---- | -------- |
| LOW_QUALITY      | int  | 低品质   |
| STANDARD_QUALITY | int  | 标准品质 |
| HIGH_QUALITY     | int  | 高品质   |

## 9.39 PlayerBuilder
播放源常量

| 常量名           | 类型     | 描述     |
| --------------- | ------- | -------- |
| mId             | String  | 播放源id  |
| mType           | int     | 播放源类型（专辑、单曲、在线广播、直播等） |
| noSubscribe     | int     | 是否可以订阅 只有为1的时候不能订阅    |


## 9.40 VipMeals
会员套餐信息类

| 变量名        | 类型   | 描述         |
| ------------- | ------ | ------------ |
| mealId        | Long   | 套餐id     |
| mealName      | Long   | 套餐名称    |
| originPrice   | Long   | 套餐原价，单位分    |
| discountPrice | Long   | 套餐折扣价，单位分  |
| vipDays       | Long   | VIP天数    |
| description   | String | 商品说明    |
| discountDes   | String | 折扣说明    |
| vipLableDisplay   | String | vip展示标签    |

## 9.41 QRCodeInfo
支付二维码信息类

| 变量名        | 类型   | 描述         |
| ------------- | ------ | ------------ |
| status        | int   | 支付状态（0-失败，1-成功，2-金额错误，3-重复购买）     |
| qrCodeId      | String    | 二维码id    |
| qrCodeImg     | String    | 二维码图片地址    |
| expireTime    | Long      | 失效时长，单位秒  |
| payStatus    | int      | 购买状态 0-未支付，1-已支付，2-过期  |

## 9.42 PurchaseSucess
支付结果类

| 变量名        | 类型   | 描述         |
| ------------- | ------ | ------------ |
| status        | int   | 支付状态（0-未支付，1-已支付，2-过期     |
| payType       | int   | 支付方式（1-微信，2-支付宝，-1-无类型，4-云闪付）    |

## 9.43 PurchasedItem
购买内容类

| 变量名        | 类型   | 描述                |
| ------------- | ------ |-------------------|
| id        | Long   | 内容id              |
| name      | String | 专辑名称              |
| img       | Long   | 专辑封面              |
| fine      | int    | 是否是付费内容（1-是，0-否）  |
| vip       | int    | 是否是VIP内容（1-是，0-否） |
| online    | int    | 是否上线，（1-是，0-否）    |
| createTime| Long   | 购买时间（时间戳，毫秒）      |

## 9.44 Order
订单信息类

| 变量名        | 类型   | 描述         |
| ------------- | ------ | ------------ |
| id               | Long   | 订单id     |
| productType      | int    | 订单内容类型（1-vip，2-专辑，3-单曲 ）   |
| title            | String | 订单显示名称（根据专辑类型：1： VIP会员x天，2： 专辑：专辑名称，3：单条单曲：专辑名（单曲名称）。多条单曲：专辑名（共xx集））       |
| billNo | String  | 订单编号  |
| payType          | int    | 支付方式（1：微信，2：支付宝，4：云闪付）    |
| plantform        | int    | 支付设备（1-Android，2-iOS）    |
| status           | int    | 订单状态（0-未支付，1-已取消，2-已支付）    |
| createTime       | String | 下单时间，xxxx（年）-xx（月）-xx（日） xx（时）：xx（分)    |
| vipEffectiveDate | String | vip生效时间， xxxx（年）-xx（月）-xx（日）   |
| vipExpireDate    | String | vip到期时间， xxxx（年）-xx（月）-xx（日）   |
| statusDesc       | String | 支付结果文字描述   |

## 9.45 Activity
活动内容类

| 变量名         | 类型       | 描述         |
| ------------- | ------ | ------------ |
| id            | int   | 活动id     |
| name          | String    | 活动名称    |
| description   | String    | 活动说明    |
| status        | int       | 活动状态（0-已结束，1-进行中）   |
| qrCodeUrl     | String    | 二维码地址  |
| codeDes       | String    | 二维码下方描述   |
| radioUrl       | String    | 音频播放地址   |
| vedioUrl       | String    | 视频播放地址   |
| styleType       | String    | 活动样式类型   |
| hasButton       | int    | 是否显示详情按钮 0-不显示 1-显示   |
| backgroundUrl       | String    | 背景图片地址   |
| activityType       | int    | 活动类型   |
| startTime       | String    | 活动开始时间   |
| endTime       | String    | 活动结束时间   |
| imgUrl       | String    | 活动详情图片   |

## 9.46 AlbumDetailsPayMethod
支付方式

| 变量名         | 类型       | 描述         |
| ------------- | ------ | ------------ |
| buyNotice            | String   | 精品标签内容     |
| payType            | int   | 支付类型，1:人民币     |
| originPrice            | int   | 原价，单位分     |
| currentPrice            | int   | 单位分，折扣价格，可为空     |
| payTypeName            | String   | 支付类型名称     |

## 9.47 AudioPlayInfo
音频播放信息

|变量名 | 类型 | 描述          |
|:------------ | :------------- |:------------|
|currentTime | long  | 当前时间 单位秒    |
|distanceTime | long  | 该秒数之后失效 单位秒 |
|duration | Integer  | 时长，单位秒      |
|playInfoList | List&lt;[AudioFileInfo](#9.48 AudioFileInfo)&gt;  | 音频文件信息          |

## 9.48 AudioFileInfo
音频文件信息

|变量名 | 类型 | 描述|
|:------------ | :------------- | :------------|
|fileType | String  | 文件格式，目前支持格式：mp3 |
|bitrate | Integer  | 码率（废弃） |
|bitrateNew | Integer  | 目前支持码率：48和192码率 |
|playUrl | String  | 播放地址，有有效期，禁止缓存 |
|fileSize | Integer  | 文件大小，单位byte |

## 9.49 TVDetails
听电视详情

|变量名 | 类型 | 描述|
|:------------ | :------------- | :------------|
| listenTVid | long  | 听电视id|
|name | String  | 听电视名称|
|img | String  | 听电视封面url|
|classifyName | String  | 听电视类型名称|
|isSubscribe | int  | 是否订阅,1=是，0=否|
|playInfoList | List&lt;[AudioFileInfo](#9.48 AudioFileInfo)&gt;  | 音频文件信息|
|playUrl | String  | 播放地址，已废弃 |
|onLineNum | int  | 在线收听数 |
|likedNum | int  | 点赞数 |
|status | int  | 上下线状态,1=上线0=下线 |
|classifyId | int  | 听电视类型id |
|roomId | int  | 电视直播间id |
|icon | String  | 电视图标 |
|isLite | int  | 是否简版 |
|areaCode | int  | 地区编码 |

## 9.50 TVPlayItem
听电视-播放对象。

|变量名 | 类型 | 描述        |
|:------------ | :------------- |:----------|
|mInfoData|InfoData| 信息相关数据    |
|mTimeInfoData|TimeInfoData| 播放时间相关数据  |
|frequencyChannel|String| 频段        |
|status|int| 听电视定制状态   |
|playInfoList | List&lt;[AudioFileInfo](#9.48 AudioFileInfo)&gt;  | 直播分音质播放地址 |
|backPlayInfoList | List&lt;[AudioFileInfo](#9.48 AudioFileInfo)&gt;  | 回放分音质播放地址 |


## 9.51 TVProgramDetails
听电视-电视节目详情。

|变量名 | 类型 | 描述|
|:------------ | :------------- | :------------|
| programId | long  | 节目id|
| listenTVid | long  | 听电视id|
| nextProgramId | int  | 下一期节目id，默认为-1|
| preProgramId | int  | 上一期节目id，默认为-1|
| title | String  | 节目名称|
| backLiveUrl | String  | 回听地址，已放弃，参考backPlayInfoList|
| playUrl | String  | 直播流地址，已放弃|
| comperes | String  | 主播名字|
| beginTime | String  | 节目展示开始时间，如“11:00”|
| endTime |   | 节目展示结束时间，如“12:00”|
| startTime | long  | 节目开始时间，单位毫秒|
| finishTime | long  | 节目结束时间，单位毫秒|
| status | int  |  播放状态，1-直播中，2-回放，3-未开播|
| isSubscribe | int  |  是否预定节目，0-未预定，1-预定|
| desc | String  | 节目简介|
| listenTVDesc | String  | 电视简介|
| listenTVName | String  | 电视名称|
| listenTVImg | String  | 电视图片|
| icon | String  | 节目图标url|
|backPlayInfoList | List&lt;[AudioFileInfo](#9.48 AudioFileInfo)&gt;  | 回放分音质播放地址|

## 9.52 CumPlaytimeInfo

用户收听时长信息（仅记录车载端）

|变量名 | 类型 | 描述                   |
|:------------ | :------------- |:---------------------|
|uid|String| 用户id                 |
|listenRank|String| 收听排名（超过的用户百分比 "90%"） |
|cumulativeDuration|long| 收听总时，单位毫秒            |
|monthCumulative|long| 当月收听时长，单位毫秒               |

## 9.53 ToneQuality

app音质配置详情

|变量名 | 类型 | 描述|
|:------------ | :------------- | :------------|
|type|int|类型|
|title|String|题目|
|des|String|描述|
|audioTargetField|String|专辑、ai电台目标码率属性|
|audioTargetValue|String|专辑、ai电台目标码率值|
|audioTargetType|String|专辑、ai电台源文件类型|
|broadcastTargetField|String|广播直播属性|
|broadcastTargetValue|String|广播直播值|
|broadcastTargetType|String|广播源文件类型|
|programTargetField|String|广播回放属性|
|programTargetValue|String|广播回放码率|
|programTargetType|String|广播回放源文件类型|
|liveTargetField|String|直播目标码率使用的属性名|
|liveTargetValue|String|直播目标码率|
|liveTargetType|String|直播源文件类型|
|listenTVTargetField|String|听电视直播源属性名|
|listenTVTargetValue|String|听电视直播源码率|
|listenTVTargetType|String|听电视直播源文件类型|
|listenTVProgramTargetField|String|听电视回听节目源属性名|
|listenTVProgramTargetValue|String|听电视回听节目源文件码率|
|listenTVProgramTargetType|String|听电视回听节目源文件类型|

## 9.54 ToneQualityResponse

音质列表数据

|变量名 | 类型 | 描述|
|:------------ | :------------- | :------------|
|bitrate| List&lt;[ToneQuality](#9.53 ToneQuality)&gt; |音质列表|

## 9.55 BaseSceneListData
推荐返回结果的基类

|变量名 | 类型 | 描述|
|:------------ | :------------- | :------------|
|code| int | 错误代码 |
|message| String | 错误信息 |
|serverTime| int | 服务器时间，单位秒 |
|result|T|结果数据|

## 9.56 SceneDataList
推荐返回结果列表

|变量名 | 类型                                        | 描述                   |
|:------------ |:------------------------------------------|:---------------------|
|outputMode| String | 推荐类型（数据上报使用）， 2：场景推荐 |
|dataList| List&lt;[SceneData](##9.57 SceneData)&gt; | 结果列表                 |

## 9.57 SceneData
推荐返回结果

|变量名 | 类型   | 描述 |
|:------------ |:-----|:--|
|id| long | 内容标识 |
|name| String | 名称 |
|img| String | 图片地址 |
|algo| String | 算法 |
|callback| String | callback字段，数据上报使用 |
|labels| List&lt;String&gt; | 标签 |
|contentType| int | 内容类型，1 专辑 ；4 电台 |

## 9.58 EmergencyBroadcast
应急广播推送的信息

|变量名 | 类型   | 描述 |
|:------------ |:-----|:--|
|headline| String | 标题文字 |
|eventDescription| String | 正文文字 |
|headlinePath| String | 标题加工音频地址 |
|eventDescriptionPath| String | 正文加工音频地址 |
|emergencyId| String | 应急广播id |
|publishTime| String | 发布时间 时间戳,到毫秒 |
|sender| String | 发布机构名称 |
|eventType| String | 灾害类型，见[应急广播灾害类型](##11.3 应急广播灾害类型) |
|eventLevel| String | 预警等级:1.红色预警 2.橙色预警 3.黄色预警 4.蓝色预警 |
|playType| String | 插播类型:0-立即插播  1-延时插播 |
|msgLevel| String | 消息等级:1-情感化问候、车载服务类消息 2-节目预约、社群等主动交互类消息 3-云听应急广播消息 |
|msgContentType| String | 消息内容类型:0.应急播报、1.AI路况消息、2.AI气象消息、3.出行服务及电商消息 、4.本地服务消息、5.活动运营相关消息、6.收听助手相关消息 |
|msgStyleType| String | 消息展示样式:0-纯文本 1-文+图  2-文+按钮 3-文+图+按钮 |
|tipsTitle| String | 提示标题:如疫情消息，国家应急广播，节日提醒等 |
|eventDescriptionExtract| String | 一句话简述 |
|msgDetailsBtnTextLeft| String | 左侧按钮文字 |
|msgDetailsBtnTextRight| String | 右侧按钮文字 |
|msgTipsPicUrl| String | 消息标题图片:消息来源机构的图片url，如国家应急广播的图片url |
|cardBgUrl| String | 消息泡泡小卡片背景图地址 |
|msgDetailsBgUrl| String | 消息详情背景图片地址 |

# 10 Interface
--

## 10.1 IPlayerInitCompleteListener
播放器初始化完毕回调

|方法名|入参类型|返回类型|描述|
|:-----|:-----|:-----|:-----|
|onPlayerInitComplete|boolean|void|true初始化成功 false初始化失败|

## 10.2 IPlayListStateListener
播单列表发生变化时回调

|方法名|入参类型|返回类型|描述|
|:-----|:-----|:-----|:-----|
|onPlayListChange|List&lt;[PlayItem](# 9.17 PlayItem)&gt;|void|播单改变时传入一个PlayItem类型的播放列表|
|onPlayListChangeError|int|void|播单改变发生错误时传入一个[5.3 onPlayListChangeError错误码](#5.3 onPlayListChangeError错误码)|

## 10.3 IPlayerStateListener
播放状态发生变化时回调

|方法名|入参类型|返回类型|描述|
|:-----|:-----|:-----|:-----|
|onIdle |PlayItem |void|播放器空闲时回调 |
|onPlayerPreparing |PlayItem |void|播放器准备时回调 |
|onPlayerPlaying |PlayItem |void|开始播放时回调 |
|onPlayerPaused |PlayItem |void|暂停播放时回调 |
|onProgress |PlayItem, long , long |void|播放中回调（播放的PlayItem，播放进度，总时长）|
|onPlayerFailed |PlayItem, int, int |void|播放失败时回调，第二个参数类型返回出现的错误类型，第三个参数返回针对与具体错误的附加码, 用于定位错误更详细信息，详见[onPlayerFailed错误码](#5.2 onPlayerFailed错误码)|
|onPlayerEnd |PlayItem |void|结束播放时调用 |
|onSeekStart |PlayItem |void|拖拽进度时回调 |
|onSeekComplete |PlayItem |void|拖拽结束时回调 |
|onBufferingStart |PlayItem |void|卡顿发生时回调 |
|onBufferingEnd |PlayItem |void|卡顿结束时回调 |
|onDownloadProgress |PlayItem, long , long |void|播放中回调（下载的PlayItem，播放进度，总时长）|

## 10.4 OnAudioFocusChangeInter
音频焦点状态发生变化时回调

|方法名|入参类型|返回类型|描述|
|:-----|:-----|:-----|:-----|
|onAudioFocusChange|int|void|返回音频焦点状态的code,code值和Android系统保持一致|

## 10.5 IGeneralListener
发生错误时回调

|方法名|入参类型|返回类型|描述|
|:-----|:-----|:-----|:-----|
|getPlayListError|int|void|返回[5.4 getPlayListError错误码](#5.4 getPlayListError错误码)|
|playUrlError|int|void|返回[5.5 playUrlError错误码](#5.5 playUrlError错误码)|

## 10.6 IPlayListControl
播单控制接口，获取播单控制时回调

|方法名|入参类型|返回类型|描述|
|:-----|:-----|:-----|:-----|
|hasNextPage |void |boolean | 是否有下一页数据 |
|hasPrePage |void |boolean | 是否有上一页数据 |
|getNextPlayItem |IPlayListGetListener | void |获取下一个PlayItem，传入一个接收播单的回调 |
|getPrePlayItem |IPlayListGetListener | void |获取上一个PlayItem，传入一个接收播单的回调 |
|getCurPlayItem |void |PlayItem | 获取当前播放PlayItem |
|getPlayItem |PlayerBuilder |PlayItem | 根据传入的PlayerBuilder返回对应的PlayItem |
|isExistPlayItem |long |boolean | 判断传入的id在当前list里面是否存在 |
|getCurPosition |void |int | 获取当前播放播单位置 |
|setCurPosition |int |void | 设置播单当前索引值 |
|setCurPosition |PlayItem |void | 设置当前播放位置 |
|hasPre |void |boolean | 是否存在上一曲目 |
|hasNext |void |boolean | 是否存在下一曲目 |
|getPlayList |void |List&lt;[PlayItem](# 9.17 PlayItem)&gt; | 获取播放列表 |
|getPlayList |void |PlaylistInfo | 获取播单详情 |
|loadNextPage |IPlayListGetListener |void | 获取下一页数据，传入一个接收播单的回调 |
|loadPrePage |IPlayListGetListener |void | 获取上一页数据，传入一个接收播单的回调 |
|loadPrePage |int, long, int, IPlayListGetListener |void | 根据资讯类型刷新获取数据，传入一个资源类型，资源id，页码，一个接收播单的回调 |
|setCallback |IPlayListStateListener |void | 设置播单变化，传入一个接收播单变化的回调 |
|clearPlayList |void |void | 清空播单 |
|release |void |void | 释放 |


# 11 附录
## 11.1 城市编码表

| 城市编码         | 城市名称                |
| ------------- | ------  |
|110000|北京市|
|120000|天津市|
|130000|河北省|
|130100|石家庄市|
|130200|唐山市|
|130300|秦皇岛市|
|130400|邯郸市|
|130500|邢台市|
|130600|保定市|
|130700|张家口市|
|130800|承德市|
|130900|沧州市|
|131000|廊坊市|
|131100|衡水市|
|140000|山西省|
|140100|太原市|
|140200|大同市|
|140300|阳泉市|
|140400|长治市|
|140500|晋城市|
|140600|朔州市|
|140700|晋中市|
|140800|运城市|
|140900|忻州市|
|141000|临汾市|
|141100|吕梁市|
|150000|内蒙古自治区|
|150100|呼和浩特市|
|150200|包头市|
|150300|乌海市|
|150400|赤峰市|
|150500|通辽市|
|150600|鄂尔多斯市|
|150700|呼伦贝尔市|
|150800|巴彦淖尔市|
|150900|乌兰察布市|
|152200|兴安盟|
|152500|锡林郭勒盟|
|152900|阿拉善盟|
|210000|辽宁省|
|210100|沈阳市|
|210200|大连市|
|210300|鞍山市|
|210400|抚顺市|
|210500|本溪市|
|210600|丹东市|
|210700|锦州市|
|210800|营口市|
|210900|阜新市|
|211000|辽阳市|
|211100|盘锦市|
|211200|铁岭市|
|211300|朝阳市|
|211400|葫芦岛市|
|220000|吉林省|
|220100|长春市|
|220200|吉林市|
|220300|四平市|
|220400|辽源市|
|220500|通化市|
|220600|白山市|
|220700|松原市|
|220800|白城市|
|222400|延边朝鲜族自治州|
|230000|黑龙江省|
|230100|哈尔滨市|
|230200|齐齐哈尔市|
|230300|鸡西市|
|230400|鹤岗市|
|230500|双鸭山市|
|230600|大庆市|
|230700|伊春市|
|230800|佳木斯市|
|230900|七台河市|
|231000|牡丹江市|
|231100|黑河市|
|231200|绥化市|
|232700|大兴安岭地区|
|310000|上海市|
|320000|江苏省|
|320100|南京市|
|320200|无锡市|
|320300|徐州市|
|320400|常州市|
|320500|苏州市|
|320600|南通市|
|320700|连云港市|
|320800|淮安市|
|320900|盐城市|
|321000|扬州市|
|321100|镇江市|
|321200|泰州市|
|321300|宿迁市|
|330000|浙江省|
|330100|杭州市|
|330200|宁波市|
|330300|温州市|
|330400|嘉兴市|
|330500|湖州市|
|330600|绍兴市|
|330700|金华市|
|330800|衢州市|
|330900|舟山市|
|331000|台州市|
|331100|丽水市|
|340000|安徽省|
|340100|合肥市|
|340181|巢湖市|
|340200|芜湖市|
|340300|蚌埠市|
|340400|淮南市|
|340500|马鞍山市|
|340600|淮北市|
|340700|铜陵市|
|340800|安庆市|
|341000|黄山市|
|341100|滁州市|
|341200|阜阳市|
|341300|宿州市|
|341500|六安市|
|341600|亳州市|
|341700|池州市|
|341800|宣城市|
|350000|福建省|
|350100|福州市|
|350200|厦门市|
|350300|莆田市|
|350400|三明市|
|350500|泉州市|
|350600|漳州市|
|350700|南平市|
|350800|龙岩市|
|350900|宁德市|
|360000|江西省|
|360100|南昌市|
|360200|景德镇市|
|360300|萍乡市|
|360400|九江市|
|360500|新余市|
|360600|鹰潭市|
|360700|赣州市|
|360800|吉安市|
|360900|宜春市|
|361000|抚州市|
|361100|上饶市|
|370000|山东省|
|370100|济南市|
|370200|青岛市|
|370300|淄博市|
|370400|枣庄市|
|370500|东营市|
|370600|烟台市|
|370700|潍坊市|
|370800|济宁市|
|370900|泰安市|
|371000|威海市|
|371100|日照市|
|371300|临沂市|
|371400|德州市|
|371500|聊城市|
|371600|滨州市|
|371700|菏泽市|
|410000|河南省|
|410100|郑州市|
|410200|开封市|
|410300|洛阳市|
|410400|平顶山市|
|410500|安阳市|
|410600|鹤壁市|
|410700|新乡市|
|410800|焦作市|
|410900|濮阳市|
|411000|许昌市|
|411100|漯河市|
|411200|三门峡市|
|411300|南阳市|
|411400|商丘市|
|411500|信阳市|
|411600|周口市|
|411700|驻马店市|
|419000|其他|
|420000|湖北省|
|420100|武汉市|
|420200|黄石市|
|420300|十堰市|
|420500|宜昌市|
|420600|襄阳市|
|420700|鄂州市|
|420800|荆门市|
|420900|孝感市|
|421000|荆州市|
|421100|黄冈市|
|421200|咸宁市|
|421300|随州市|
|422800|恩施土家族苗族自治州|
|429000|其他|
|430000|湖南省|
|430100|长沙市|
|430200|株洲市|
|430300|湘潭市|
|430400|衡阳市|
|430500|邵阳市|
|430600|岳阳市|
|430700|常德市|
|430800|张家界市|
|430900|益阳市|
|431000|郴州市|
|431100|永州市|
|431200|怀化市|
|431300|娄底市|
|433100|湘西土家族苗族自治州|
|440000|广东省|
|440100|广州市|
|440200|韶关市|
|440300|深圳市|
|440400|珠海市|
|440500|汕头市|
|440600|佛山市|
|440700|江门市|
|440800|湛江市|
|440900|茂名市|
|441200|肇庆市|
|441300|惠州市|
|441400|梅州市|
|441500|汕尾市|
|441600|河源市|
|441700|阳江市|
|441800|清远市|
|441900|东莞市|
|442000|中山市|
|445100|潮州市|
|445200|揭阳市|
|445300|云浮市|
|450000|广西壮族自治区|
|450100|南宁市|
|450200|柳州市|
|450300|桂林市|
|450400|梧州市|
|450500|北海市|
|450600|防城港市|
|450700|钦州市|
|450800|贵港市|
|450900|玉林市|
|451000|百色市|
|451100|贺州市|
|451200|河池市|
|451300|来宾市|
|451400|崇左市|
|460000|海南省|
|460100|海口市|
|460200|三亚市|
|460300|三沙市|
|460400|儋州市|
|469000|其他|
|500000|重庆市|
|510000|四川省|
|510100|成都市|
|510300|自贡市|
|510400|攀枝花市|
|510500|泸州市|
|510600|德阳市|
|510700|绵阳市|
|510800|广元市|
|510900|遂宁市|
|511000|内江市|
|511100|乐山市|
|511300|南充市|
|511400|眉山市|
|511500|宜宾市|
|511600|广安市|
|511700|达州市|
|511800|雅安市|
|511900|巴中市|
|512000|资阳市|
|513200|阿坝藏族羌族自治州|
|513300|甘孜藏族自治州|
|513400|凉山彝族自治州|
|520000|贵州省|
|520100|贵阳市|
|520200|六盘水市|
|520300|遵义市|
|520400|安顺市|
|520500|毕节市|
|520600|铜仁市|
|522300|黔西南布依族苗族自治州|
|522600|黔东南苗族侗族自治州|
|522700|黔南布依族苗族自治州|
|530000|云南省|
|530100|昆明市|
|530300|曲靖市|
|530400|玉溪市|
|530500|保山市|
|530600|昭通市|
|530700|丽江市|
|530800|普洱市|
|530900|临沧市|
|532300|楚雄彝族自治州|
|532500|红河哈尼族彝族自治州|
|532600|文山壮族苗族自治州|
|532800|西双版纳傣族自治州|
|532900|大理白族自治州|
|533100|德宏傣族景颇族自治州|
|533300|怒江傈僳族自治州|
|533400|迪庆藏族自治州|
|540000|西藏自治区|
|540100|拉萨市|
|542100|昌都地区|
|542200|山南地区|
|542300|日喀则地区|
|542400|那曲地区|
|542500|阿里地区|
|542600|林芝地区|
|610000|陕西省|
|610100|西安市|
|610200|铜川市|
|610300|宝鸡市|
|610400|咸阳市|
|610500|渭南市|
|610600|延安市|
|610700|汉中市|
|610800|榆林市|
|610900|安康市|
|611000|商洛市|
|620000|甘肃省|
|620100|兰州市|
|620200|嘉峪关市|
|620300|金昌市|
|620400|白银市|
|620500|天水市|
|620600|武威市|
|620700|张掖市|
|620800|平凉市|
|620900|酒泉市|
|621000|庆阳市|
|621100|定西市|
|621200|陇南市|
|622900|临夏回族自治州|
|623000|甘南藏族自治州|
|630000|青海省|
|630100|西宁市|
|632100|海东地区|
|632200|海北藏族自治州|
|632300|黄南藏族自治州|
|632500|海南藏族自治州|
|632600|果洛藏族自治州|
|632700|玉树藏族自治州|
|632800|海西蒙古族藏族自治州|
|640000|宁夏回族自治区|
|640100|银川市|
|640200|石嘴山市|
|640300|吴忠市|
|640400|固原市|
|640500|中卫市|
|650000|新疆维吾尔自治区|
|650100|乌鲁木齐市|
|650200|克拉玛依市|
|652100|吐鲁番地区|
|652200|哈密地区|
|652300|昌吉回族自治州|
|652700|博尔塔拉蒙古自治州|
|652800|巴音郭楞蒙古自治州|
|652900|阿克苏地区|
|653000|克孜勒苏柯尔克孜自治州|
|653100|喀什地区|
|653200|和田地区|
|654000|伊犁哈萨克自治州|
|654200|塔城地区|
|654300|阿勒泰地区|
|660000|新疆兵团|

## 11.2 场景推荐的场景枚举值
### 11.2.1 时间
| 代码        |  逻辑场景     |物理条件|
| ------------ | ------  |------  |
|101  |凌晨|   00:00-04:59:59|
|102  |清晨|   05:00-06:59:59|
|103 |早高峰|   07:00-09:59:59|
|104  |上午|   10:00-10:59:59|
|105  |中午|   11:00-13:59:59|
|106  |午后|   14:00-15:59:59|
|107  |下午|   16:00-16:59:59|
|108 |晚高峰|   17:00-19:59:59|
|109  |晚间|   20:00-21:59:59|
|110  |深夜|   22:00-23:59:59|

### 11.2.2 年龄
| 代码        |  逻辑场景     |物理条件|
| ------------ | ------  |------  |
|   201     |0-17岁||
|   202    |18-25岁||
|  203     |26-30岁||
|  204     |31-35岁||
| 205      |36-40岁||
|  206    |41岁以上||

### 11.2.3 车型
| 代码         |逻辑场景       |物理条件|
| ------------- | ------  |------  |
|301                    |MPV||
|302                    |SUV||
|303            |豪华-大型车||
|304     |豪华-中型车（C级）||
|305          |中型车（B级）||
|306         |紧凑型车（A级)||
|307         |小型车（A0级）||

### 11.2.4 性别
|代码          | 逻辑场景      |物理条件|
| ------------- | ------  |------  |
|401|男	||
|402|女	||

### 11.2.5 天气
| 代码         | 逻辑场景      |物理条件|
| ------------- | ------  |------  |
|501   |高温    |车外温度>35度|
|502   |低温    |车外温度<0度|
|503   |大风    |6级<风力<12级|
|504   |飓风    |风力>12级|
|505   |小雨    |0.1~ 9.9 mm/ d|
|506   |中雨    |10 ~ 24. 9 mm / d|
|507   |大雨    |25 ~ 49 . 9 mm/ d|
|508   |暴雨    |50. 0 ~ 99. 9 mm/ d|
|509   |小雪    |面积雪深度在3厘米以下，降水量级为24小时降雪量在0.1～2.4毫米之间。|
|510   |中雪    |地面积雪深度为3～5厘米，24小时降雪量达2.5～4.9毫米。|
|511   |大雪    |地面积雪深度等于或大于5厘米，24小时降雪量达5.0～9.9毫米。|
|512   |暴雪    |24小时降雪量达到10.0～19.9毫米时为暴雪。|

### 11.2.6 车速
| 代码         | 逻辑场景     |物理条件|
| ------------- | ------  |------  |
|601	|堵车|行车的速度<20km/h|
|602	|快速	|行车的速度>100km/h|

### 11.2.7 特定事件场景
| 代码         | 逻辑场景 |物理条件|
| ------------- |------|------  |
|701	| 长途出游	    |导航目的地距离>300KM|

### 11.2.8 场景代码
| 代码         | 逻辑场景    |物理条件|
| ------------- |-------|------  |
|1031	| 早高峰	 |5:00-09:59|
|1051	| 午高峰	 |10:00-13:59|
|1061	| 午后	 |14:00-16:59|
|1081	| 晚高峰	 |17:00-19:59|
|1091	| 深夜	 |20:00-22:59|
|1011	| 凌晨	 |23:00-04:59|

## 11.3 应急广播灾害类型
| 灾害类型代码 |   灾害类型描述    |
|--------| ------  |
|11B52	|雷雨大风-2|
|12Q17	|空气重污染|
|11B75	|城市内涝气象风险|
|11B56	|低温|
|11B43	|雷暴事件|
|11B57	|道路冰雪|
|11D08	|地质灾害气象风险|
|11B62	|海区大风|
|12Q19	|重污染天气|
|11B71	|腹泻等肠道疾病气象条件|
|11B59	|持续低温|
|11B69	|感冒等呼吸道疾病气象条件|
|11B72	|心脑血管疾病气象条件|
|11B73	|洪涝灾害气象风险|
|11B01	|台风事件|
|11B03	|暴雨事件|
|11B04	|暴雪事件|
|11B05	|寒潮事件|
|11B06	|大风事件|
|11B07	|沙尘暴事件|
|11B08	|低温冻害事件|
|11B09	|高温事件|
|11B11	|干热风|
|11B14	|雷电事件|
|11B15	|冰雹事件|
|11B16	|霜冻事件|
|11B17	|大雾事件|
|11B19	|雾霾|
|11B20	|雷雨大风|
|11B21	|道路结冰|
|11B22	|干旱|
|11B23	|海上大风|
|11B24	|高温中暑|
|11B25	|森林火险|
|11B26	|草原火险 |
|11B29	|重污染|
|11B30	|低温雨雪冰冻 |
|11B31	|强对流|
|11B33	|大雪|
|11B34	|寒冷|
|11B37	|地质灾害|
|11B38	|强降雨|
|11B39	|强降温|
|11B40	|雪灾|
|11B41	|森林（草原）火险|
|11G09	|森林火险预警|
|11G10	|高森林火险|
|11F15	|农业气象灾害|
|11Y04	|应急安全提醒|
|11D09	|地质灾害气象|
|11D99	|其它地质灾害事件|
|11A04	|堤防重大险情|
|13A10	|新型冠状病毒肺炎|
|11C02	|地震|
|11E01	|海啸事件|
|11E02	|风暴潮|
|11A07	|洪涝|
|11A56	|山洪灾害气象风险|
|11A11	|中小河流洪水气象风险|
|11A55	|灾害风险预警|
|11A80	|测试类型|
|11A09	|中小河流洪水和山洪气象风险|
|11A13	|防汛抗旱风险提示|
|11A14	|城市内涝|
|11A01	|洪水|
|11A02	|内涝|
|11A51	|山洪事件|
|11A52	|农业干旱|
|11A54	|生态干旱|


# 更新说明

### 版本：v1.8.0  2023-04-15
--
**改动**
1. 新增[3.17 场景推荐](##3.17 场景推荐)
2. 新增[3.18 应急广播](##3.18 应急广播)
3. 新增[4.9 设置播放器音频属性](##4.9 设置播放器音频属性)
4. 新增[8.5.3 推荐展示数据上报](###8.5.3 推荐展示数据上报)
5. 新增[8.5.4 推荐点击数据上报](###8.5.4 推荐点击数据上报)
### 版本：v1.7.0  2022-12-15
--
**改动**
1. 新增[5.6 播放器增加播单失败错误码](#5.6 播放器增加播单失败错误码)
2. 新增[8.5.2 在线广播播放中数据上报](#8.5.2 在线广播播放中数据上报)，去掉原先的"在线广播播放结束数据上报"
3. 新增接口：
[3.2.8 云听账号收听时长](#3.2.8 云听账号收听时长)
[3.8.3 根据单曲播放id请求播放的url地址](#3.8.3 根据单曲播放id请求播放的url地址)
[3.15 听电视](#3.15 听电视)
[3.16 音质设置](#3.16 音质设置)
4. 新增对象：
[9.5.7 TVDetailColumnMember](#9.5.7 TVDetailColumnMember)
[9.7.5 TVCategoryMember](#9.7.5 TVCategoryMember)
[9.47 AudioPlayInfo](#9.47 AudioPlayInfo)
[9.48 AudioFileInfo](#9.48 AudioFileInfo)
[9.49 TVDetails](#9.49 TVDetails)
[9.51 TVProgramDetails](#9.51 TVProgramDetails)
[9.52 CumPlaytimeInfo](#9.52 CumPlaytimeInfo)
[9.53 ToneQuality](#9.53 ToneQuality)
[9.54 ToneQualityResponse](#9.54 ToneQualityResponse)
5. 新增字段：
[9.31 ResType](#9.31 ResType) 
|参数名称|参数类型|描述|
|:-----|:-----|:-----|
|TYPE_TV|int|听电视|

[9.32 SubscribeInfo](#9.32 SubscribeInfo) 
|参数名称|参数类型|描述|
|:-----|:-----|:-----|
|subscribeCount|long|订阅量|

[9.34 ListeningHistory](#9.34 ListeningHistory)
|参数名称|参数类型|描述|
|:-----|:-----|:-----|
| broadcastSort | int   | 广播内容类型（音乐，交通，新闻等）|
| listenCount | long   | 播放量 |

[9.35 UserInfo](#9.35 UserInfo)  
|参数名称|参数类型|描述|
|:-----|:-----|:-----|
|gender|String|用户性别|
|userArea|String|用户地区|

[9.45 Activity](#9.45 Activity) 
|参数名称|参数类型|描述|
|:-----|:-----|:-----|
| radioUrl       | String    | 音频播放地址   |
| vedioUrl       | String    | 视频播放地址   |
| styleType       | String    | 活动样式类型   |
| hasButton       | int    | 是否显示详情按钮 0-不显示 1-显示   |
| backgroundUrl       | String    | 背景图片地址   |
| activityType       | int    | 活动类型   |
| startTime       | String    | 活动开始时间   |
| endTime       | String    | 活动结束时间   |
| imgUrl       | String    | 活动详情图片   |

[9.5 ColumnMember](#9.5 ColumnMember)
|参数名称|参数类型|描述|
|:-----|:-----|:-----|
|recommendReason|String|推荐理由|

### 版本：v1.6.0.24  2022-11-28
--
**改动**
接口[3.9.1 获取在线广播详情](#3.9.1 获取在线广播详情)使用的对象[9.15 BroadcastDetails](#9.15 BroadcastDetails)新增字段programEnable
接口[3.9.2 获取在线广播节目单列表](#3.9.2 获取在线广播节目单列表)使用的对象[9.16 ProgramDetails](#9.16 ProgramDetails)新增字段programEnable
接口[3.9.3 获取在线广播单个节目详情](#3.9.3 获取在线广播单个节目详情)使用的对象[9.16 ProgramDetails](#9.16 ProgramDetails)新增字段programEnable
接口[3.9.4 获取在线广播当前正在播放的节目](#3.9.4 获取在线广播当前正在播放的节目)使用的对象[9.16 ProgramDetails](#9.16 ProgramDetails)新增字段programEnable
对象[9.20 BroadcastPlayItem](#9.20 BroadcastPlayItem)新增字段programEnable
对象[9.28 PlaylistInfo](#9.28 PlaylistInfo)新增字段programEnable

### 版本：v1.6.0  2022-04-24
--
**新功能**

- [3.13](#3.13 会员功能) 会员功能
- [3.14](#3.14 活动专区) 活动功能

**改动**

- SDK订阅列表广播类型4改为11
- 文档增加激活失败码607

**优化**

播放器重构

**删减**

- 运营，根据父编码获取多层级子分类列表

### 版本：v1.5.11  2022-04-23
--
**新功能**

- [3.10](#3.10 订阅) 订阅，必须先登录云听账号
- [3.11](#3.11 收听历史) 历史记录，必须先登录云听账号

### 版本：v1.5.10  2022-02-23
--
**新功能**

- 播放器播放在线广播的时候，上报开始和结束事件

### 版本：v1.5.9  2021-10-09
--
**新功能**

- [3.3.1](#3.3.1 获取指定内容类型的整棵分类树) 新增contentType字段TYPE_NEWS及说明
- [3.3.2](#3.3.2 获取指定内容类型的根分类列表) 新增contentType字段TYPE_NEWS及说明
- [9.24](#9.33 SceneInfo)  修改contentType字段说明
- [9.5.4](#9.5.4 CategoryColumnMember) 修改contentType字段说明
- [9.6](#9.6 Category) 修改contentType字段说明
- [9.10](#9.10 SearchProgramBean) 修改type字段说明
- [9.22](#9.31 ResType) ResType新增字段TYPE_NEWS


### 版本：v1.5.8.05 2021-10-09

--

**新功能**

- 新增 “ 3.1.5 应用启动上报” 的接口调用


### 版本：v1.5.8 2020-10-29

--

**Bug修复**

- 修复因访问网络超时导致的ANR问题。

**优化**

- SDK播放器添加完善日志，方便问题定位。


### 版本：v1.5.7 2020-10-22

--

**优化**

- 播放器增加准备完毕不自动播放功能。
- 播放器默认打开日志，提供关闭日志方法。


### 版本：v1.5.5 2020-07-10

--

**Bug修复**

* 修复激活时可能导致死锁的问题。
* 修复内存泄露问题。
* 其他已知Bug修复。
* 修改仓库地址，原仓库有被墙的风险。


### 版本：v1.5.4 2019-12-03

--

**优化**
* 优化IJK播放器底层逻辑。

### 版本：v1.5.3 2019-10-28

--

**新功能**

- 新增直播主持人Id字段
- 新增获取热词列表的接口
- 新增同时获取多级子分类接口
- 新增设置车型（CarType）方式。
- 新增在线广播可订阅、取消订阅。

**Bug修复**

- 修正一键播放拉取clockId获取的值
- 修正搜索结果字段更新说明类型。
- 修复打印log设置tag的Bug。
- 调整deviceId逻辑。
- 修复其他已知Bug。

### 版本：v1.5.2 2019-07-30

--

**Bug修复**

- 修正播放AI电台的功能逻辑。

### 版本：v1.5.1 2019-07-15

--

**Bug修复**

* 修复播放器特定情况下由于状态不一致导致播放失败问题。

### 版本：v1.5.0 2019-06-21

--

**新功能**

- 新增运营相关的八个接口，原有接口已废弃，但仍可使用。
- 新增关键词搜索。
- 新增获取联想词。
- 网络请求超时时间修改为10秒。
- 新增获取二维码时可以传入回调地址。
- 新增播放器设置音质功能。
- 单曲新增编排内容类型相关字段。

**Bug修复**

- 修复数据上报相关崩溃。
- 修复特定情况下播放器无法播放的问题。
- 修复初始化后直接激活可能会崩溃


### 版本：v1.4.0 2019-03-31

--

**新功能**

- 新增收听历史相关接口。
- 新增获取品牌相关信息。
- 新增网络请求可以绑定rxlifecycle或tag取消请求。
- 新增一键播放订阅功能。
- 新增场景推送功能。
- 账号体系修改。
- 添加单曲转PlayItem工具类。
- 添加可以显示log。

**Bug修复**

- 修正网络请求错误码和错误信息。
- 修复跑Monkey可能引起OOM的问题。


### 版本：v1.3.1 2019-01-17

--

**新功能**

- 添加判断是否已经激活接口。
- 在线广播栏目成员添加在线广播频率。

**Bug修复**

- 修复跑monkey时可能出现死锁。
- fastjson替换成Gson，以解决内存泄露问题。
- 修复播放专辑上一页一直是第一页的问题。

### 版本：v1.3.0 2018-11-07

--

**新功能**

- 新增获取当前报时单曲接口。
- 新增可以根据地区或地区编码获取AI电台播单。
- 新增语音搜索接口，去掉暂不支持的参数。
- 新增播放器音频焦点变化回调和开关。
- 新增第三方账号打通接口。
- 新增运营接口的Bean操作辅助类OperationAssister。

**Bug修复**

- 修复SDK初始化可能会崩溃。
- 修复QQ音乐Token失效不自动刷新。
- 修改SDK激活逻辑，有异常回调异常，返回数据有问题返回false。

### 版本：v1.2.1 2018-10-29

--

**Bug修复**

- 修复播放器在Android O以上版本兼容问题。

### 版本：v1.2.0 2018-10-19

--

**新功能**

- 新增直播功能，包括接口和播放器相关功能。

### 版本：v1.1.0 2018-9-26

--

**新功能**

- 新增云听账号登录相关功能。
- 新增QQ音乐登录等相关接口。
- 修改运营接口相关Bean类的Integer、Long为int、long。
- 更新云听SDK版本到2.2.4。

**Bug修复**

- 修复token未到期失效问题。
- 优化sign参数生成时机。

