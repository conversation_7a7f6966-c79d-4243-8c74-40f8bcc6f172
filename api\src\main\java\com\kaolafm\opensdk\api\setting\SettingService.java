package com.kaolafm.opensdk.api.setting;


import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.KaolaApiConstant;
import com.kaolafm.opensdk.api.setting.model.SettingConfigBean;

import io.reactivex.Single;
import retrofit2.http.GET;
import retrofit2.http.Headers;

public interface SettingService {
    @Headers({ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER, ApiHostConstants.HTTPS_PROTOCOL_DOMAIN_HEADER})
    @GET(KaolaApiConstant.GET_SETTING_CONFIG)
    Single<BaseResult<SettingConfigBean>> getSettingConfig();
}
