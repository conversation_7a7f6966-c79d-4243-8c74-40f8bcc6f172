package com.kaolafm.report.api;

import com.kaolafm.opensdk.HostConstants;
import com.kaolafm.opensdk.http.urlmanager.UrlManager;

/**
 * <AUTHOR>
 * @date 2023-03-10
 */
public class ReportHostConstant {

    /**
     * 数据上报host地址
     */
    public static final String REPORT_BASE_URL = "http://" + HostConstants.REPORT_HOST;

    public static final String REPORT_DOMAIN_NAME = "ReportSDK";
    public static final String REPORT_DOMAIN_HEADER = UrlManager.DOMAIN_NAME_HEADER
            + REPORT_DOMAIN_NAME;


    /**
     * open-kaola
     */
    public static final String OPEN_KAOLA_HOST = "https://" + HostConstants.OPEN_KAOLA_HOST;
    public static final String OPEN_KAOLA_DOMAIN_NAME = "open_kaola";
    public static final String OPEN_KAOLA_DOMAIN_HEADER = UrlManager.DOMAIN_NAME_HEADER + OPEN_KAOLA_DOMAIN_NAME;

}
