发布OpenSDK版本步骤
1. 解决完相关问题；
2. 调整sdk/maven_push.gradle文件中的upload.sdkFlavors.dev作用域下的versionName
3. Mac 系统下执行./gradlew uploadDev Windows系统下执行gradlew uploadDev 或 选择项目右侧Gradle/KaoLaOpenSdk/Tasks/build-upload/uploadDev
4. 版本号以-SNAPSHOT为后缀，提交到远程库，编译kradio会自动拉取最新的，不用再手动更改版本号了。
5. 编译opensdk提交到远程库，新增了两个参数dt(域名类型)，av（api版本）。
    dt方便使用不同的域名，默认不写的话是线上环境，其他取值为test和prelease。
    av默认是v3，版本号包含"snapshot"，推送到http://nexus.kaolafm.com/nexus/content/repositories/snapshots/
，不包含"snapshot"，推送到http://nexus.kaolafm.com/nexus/content/repositories/releases/；
    v2是推送到http://pub.nexus.kaolafm.com:8082/repository/maven-releases。
    例如./gradlew clean;./gradlew uploadDev -Ddt=test -Dav=v3