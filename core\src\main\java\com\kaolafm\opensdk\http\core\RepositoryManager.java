package com.kaolafm.opensdk.http.core;

import androidx.annotation.NonNull;
import android.text.TextUtils;

import com.kaolafm.opensdk.di.scope.AppScope;
import com.kaolafm.opensdk.http.cache.Cache;
import com.kaolafm.opensdk.http.cache.Cache.Factory;
import com.kaolafm.opensdk.http.cache.CacheType;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.http.error.ErrorCode;
import com.kaolafm.opensdk.http.error.FlowableErrorFunc;
import com.kaolafm.opensdk.http.error.ObservableErrorFunc;
import com.kaolafm.opensdk.http.error.ResponseErrorListener;
import com.kaolafm.opensdk.http.error.SingleErrorFunc;
import com.trello.rxlifecycle3.LifecycleProvider;
import com.trello.rxlifecycle3.LifecycleTransformer;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map.Entry;
import java.util.Set;

import javax.inject.Inject;
import javax.inject.Provider;

import dagger.Lazy;
import io.reactivex.Flowable;
import io.reactivex.Observable;
import io.reactivex.ObservableTransformer;
import io.reactivex.Single;
import io.reactivex.SingleTransformer;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Function;
import io.reactivex.schedulers.Schedulers;
import retrofit2.Call;
import retrofit2.Retrofit;

/**
 * 用来管理网络请求层,以及数据缓存层。
 *
 * <AUTHOR>
 * @date 2018/4/18
 */
@AppScope
public class RepositoryManager implements IRepositoryManager {

    @Inject
    Factory mCacheFactory;

    @Inject
    Lazy<Retrofit> mRetrofit;

    @Inject
    @AppScope
    List<ResponseErrorListener> mResponseErrorListenerList;

    @Inject
    Provider<SingleRetryFunction> mSingleRetryFunctionProvide;

    @Inject
    Provider<ObservableRetryFunction> mObservableRetryFunctionLazy;

    private Cache<String, Object> mRetrofitServiceCache;

    private Cache<String, Object> mCacheServiceCache;

    private HashMap<Object, CompositeDisposable> mDisposables = new HashMap<>();

    @Inject
    public RepositoryManager() {
    }

    @Override
    public synchronized <T> T obtainRetrofitService(Class<T> service) {
        if (mRetrofitServiceCache == null) {
            mRetrofitServiceCache = mCacheFactory.build(CacheType.RETROFIT_SERVICE_CACHE);
        }
        T retrofitService = (T) mRetrofitServiceCache.get(service.getCanonicalName());
        if (retrofitService == null) {
            retrofitService = mRetrofit.get().create(service);
            mRetrofitServiceCache.put(service.getCanonicalName(), retrofitService);
        }
        return retrofitService;
    }

    @Override
    public synchronized <T> T obtainCacheService(Class<T> cache) {
        if (mCacheServiceCache == null) {
            mCacheServiceCache = mCacheFactory.build(CacheType.CACHE_SERVICE_CACHE);
        }
        T cacheService = (T) mCacheServiceCache.get(cache.getCanonicalName());
        if (cacheService == null) {
//            cacheService = mRxCache.get().using(cache);
            mCacheServiceCache.put(cache.getCanonicalName(), cacheService);
        }
        return cacheService;
    }

    @Override
    public void clearAllCache() {
//        mRxCache.get().evictAll();
    }

    //============================Observable===============================================

    /**
     * 处理网络请求。Observable，
     *
     * @param observable retrofit接口返回的Observable对象
     * @param <T>        原始数据
     */
    @Override
    public <T> void doHttpDeal(Observable<T> observable) {
//        doHttpDeal(observable, null);
    }

    /**
     * 处理网络请求。Observable，
     *
     * @param tag        tag标记，用于取消网络请求
     * @param observable retrofit接口返回的Observable对象
     * @param callback   请求回调
     * @param <T>        原始数据
     */
    @Override
    public <T> void doHttpDeal(Object tag, Observable<T> observable, HttpCallback<T> callback) {
        subscribeObservable(tag, configObservable(observable), callback);
    }

    /**
     * 处理网络请求。Observable，
     *
     * @param tag        tag标记，用于取消网络请求
     * @param observable retrofit接口返回的Observable对象
     * @param function   用于数据处理, 不可为空。如果不需要处理数据，请使用{@link #doHttpDeal(Object, Observable, HttpCallback)}
     * @param callback   请求回调
     * @param <T>        原始数据
     * @param <E>        处理后的数据
     */
    @Override
    public <T, E> void doHttpDeal(Object tag, Observable<T> observable, Function<T, E> function,
                                  HttpCallback<E> callback) {
        if (function != null) {
            subscribeObservable(tag, configObservable(observable).map(function), callback);
        }
    }

    /**
     * 处理网络请求。Observable，
     *
     * @param observable retrofit接口返回的Observable对象
     * @param function   用于数据处理, 不可为空。如果不需要处理数据，请使用{@link #doHttpDeal(Object, Observable, HttpCallback)}
     * @param <T>        原始数据
     * @param <E>        处理后的数据
     */
    @Override
    public <T, E> void doHttpDeal(Observable<T> observable, Function<T, E> function) {
        doHttpDeal(null, observable, function, null);
    }

    @Override
    public <T, R> void doHttpDeal(ObservableTransformer observableTransformer, Observable<T> observable,
                                  Function<T, R> function, HttpCallback<R> callback) {
        if (observableTransformer != null) {
            observable = observable.compose(observableTransformer);
        }
        subscribeObservable(configObservable(observable).map(function), callback);
    }

    @Override
    public <T> void doHttpDeal(ObservableTransformer observableTransformer, Observable<T> observable,
                               HttpCallback<T> callback) {
        if (observableTransformer != null) {
            observable = observable.compose(observableTransformer);
        }
        subscribeObservable(configObservable(observable), callback);
    }

    /**
     * 给Observable添加公共配置
     */
    private <T> Observable<T> configObservable(Observable<T> observable) {
        return Observable.defer(() -> observable)
                .subscribeOn(Schedulers.io())
                .unsubscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .map(this::handleResultError)
                .retryWhen(mObservableRetryFunctionLazy.get());
    }

    private <E> void subscribeObservable(Observable<E> observable, HttpCallback<E> callback) {
        subscribeObservable(null, observable, callback);
    }

    private <E> void subscribeObservable(Object tag, Observable<E> observable, HttpCallback<E> callback) {
        subscribeObservable(tag, observable, new AutoDisposeObserver<>(callback));
    }

    public <E> void subscribeObservable(Object tag, Observable<E> observable, CompositeObserver<E> observer) {
        observable.onErrorResumeNext(new ObservableErrorFunc<>(mResponseErrorListenerList))
                .doOnDispose(() -> removeDisposable(tag, observer))
                .subscribe(observer);
        addDisposable(tag, observer);
    }

    //===============================Single=======================

    /**
     * 处理网络请求。使用Single.
     *
     * @param single   retrofit接口返回的Single对象
     * @param function 用于数据处理, 不可为空。如果不需要处理数据，请使用{@link #doHttpDeal(Single, HttpCallback)}
     * @param callback 请求回调
     * @param <T>      原始数据
     * @param <E>      处理后的数据
     */
    @Override
    public <T, E> void doHttpDeal(Single<T> single, Function<T, E> function, HttpCallback<E> callback) {
        if (function != null) {
            subscribeSingle(configSingle(single).map(function), callback);
        }
    }

    @Override
    public <T, E> Single<E> doHttpDeal(Single<T> single, Function<T, E> function) {
        if (function != null) {
            return configSingle(single).map(function)
                    .onErrorResumeNext(new SingleErrorFunc<>(mResponseErrorListenerList));
        }
        return null;
    }

    @Override
    public <T, R> void doHttpDeal(SingleTransformer singleTransformer, Single<T> single,
                                  Function<T, R> function, HttpCallback<R> callback) {
        if (singleTransformer != null) {
            single = single.compose(singleTransformer);
        }
        subscribeSingle(configSingle(single).map(function), callback);
    }

    @Override
    public <T, R> void doHttpDeal(Object obj, Single<T> single, Function<T, R> function, HttpCallback<R> callback) {
        subscribeSingle(obj, configSingle(single).map(function), callback);
    }

    /**
     * 处理网络请求。使用Single.
     *
     * @param single   retrofit接口返回的Single对象
     * @param callback 请求回调
     * @param <T>      原始数据
     */
    @Override
    public <T> void doHttpDeal(Single<T> single, HttpCallback<T> callback) {
        subscribeSingle(configSingle(single), callback);
    }

    /**
     * 处理网络请求。使用Single.
     *
     * @param single retrofit接口返回对象
     */
    @Override
    public <T> void doHttpDeal(Single<T> single) {
        subscribeSingle(configSingle(single), null);
    }

    /**
     * 处理网络请求。使用Single.
     *
     * @param singleTransformer 实现{@link LifecycleProvider}接口的类，用于将rxjava绑定到其生命周期上。
     * @param single            retrofit接口返回对象
     * @param callback          请求回调
     */
    @Override
    public <T> void doHttpDeal(SingleTransformer singleTransformer, Single<T> single, HttpCallback<T> callback) {
        if (singleTransformer != null) {
            single = single.compose(singleTransformer);
        }
        subscribeSingle(configSingle(single), callback);
    }

    @Override
    public <T> void doHttpDeal(Object tag, Single<T> single, HttpCallback<T> callback) {
        subscribeSingle(tag, configSingle(single), callback);
    }

    private <T> Single<T> configSingle(Single<T> single) {
        return Single.defer(() -> single)
                .subscribeOn(Schedulers.io())
                .unsubscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .map(this::handleResultError)
                .retryWhen(mSingleRetryFunctionProvide.get());
    }

    private <E> void subscribeSingle(Single<E> single, HttpCallback<E> callback) {
        subscribeSingle(null, single, callback);
    }

    private <E> void subscribeSingle(Object tag, Single<E> single, HttpCallback<E> callback) {
        subscribeSingle(tag, single, new AutoDisposeObserver<>(callback));
    }

    private <E> void subscribeSingle(Object tag, Single<E> single, CompositeObserver<E> observer) {
        single.onErrorResumeNext(new SingleErrorFunc<>(mResponseErrorListenerList))
                .doOnDispose(() -> removeDisposable(tag, observer))
                .subscribe(observer);
        addDisposable(tag, observer);
    }

    //=============================Flowable======================

    @Override
    public <T> void doHttpDeal(LifecycleTransformer transformer, Flowable<T> flowable, FlowableCallback<T> callback) {
        if (transformer != null) {
            flowable = flowable.compose(transformer);
        }
        subscribeFlowable(null, configFlowable(flowable), callback);
    }

    @Override
    public <T> void doHttpDeal(Object tag, Flowable<T> flowable, FlowableCallback<T> callback) {
        subscribeFlowable(tag, configFlowable(flowable), new DisposableSubscriber<>(callback));
    }

    @Override
    public <T> void doHttpDeal(Flowable<T> flowable, FlowableCallback<T> callback) {
        doHttpDeal(null, flowable, callback);
    }

    private <T> Flowable<T> configFlowable(Flowable<T> flowable) {
        return flowable
                .subscribeOn(Schedulers.io())
                .unsubscribeOn(Schedulers.io())
                .map(this::handleResultError)
                .retryWhen(mSingleRetryFunctionProvide.get());
    }

    private <T> void subscribeFlowable(Object tag, Flowable<T> flowable, FlowableCallback<T> callback) {
        subscribeFlowable(tag, flowable, new DisposableSubscriber<>(callback));
    }

    private <E> void subscribeFlowable(Object tag, Flowable<E> flowable, DisposableSubscriber<E> subscriber) {
        flowable.onErrorResumeNext(new FlowableErrorFunc<>(mResponseErrorListenerList))
                .doOnComplete(() -> removeDisposable(tag, subscriber))
                .doOnError(throwable -> removeDisposable(tag, subscriber))
                .subscribe(subscriber);
        addDisposable(tag, subscriber);
    }

    //=============================共用方法=============================

    /**
     * 取消或请求完成移除内存中的disposable
     */
    private void removeDisposable(Object tag, Disposable disposable) {
        if (tag != null) {
            CompositeDisposable compositeDisposable = mDisposables.get(tag);
            if (compositeDisposable != null) {
                compositeDisposable.delete(disposable);
                if (compositeDisposable.size() == 0) {
                    mDisposables.remove(tag);
                    System.gc();
                }
            }
        }
    }

    /**
     * 根据tag保存disposable到内存
     */
    private void addDisposable(Object tag, Disposable disposable) {
        if (tag != null) {
            CompositeDisposable compositeDisposable = mDisposables.get(tag);
            if (compositeDisposable == null) {
                compositeDisposable = new CompositeDisposable();
                mDisposables.put(tag, compositeDisposable);
            }
            compositeDisposable.add(disposable);
        }
    }


    @Override
    public void cancel(Object tag) {
        if (tag != null) {
            CompositeDisposable compositeDisposable = mDisposables.remove(tag);
            if (compositeDisposable != null && !compositeDisposable.isDisposed()) {
                compositeDisposable.dispose();
                System.gc();
            }
        }
    }

    public void cancel() {
        Set<Entry<Object, CompositeDisposable>> entrySet = mDisposables.entrySet();
        for (Entry<Object, CompositeDisposable> entry : entrySet) {
            CompositeDisposable compositeDisposable = mDisposables.remove(entry);
            if (compositeDisposable != null && !compositeDisposable.isDisposed()) {
                compositeDisposable.dispose();
            }
        }
        System.gc();
    }

    /**
     * 处理服务器异常信息
     */
    @NonNull
    private <T> T handleResultError(T t) throws ApiException {
        if (t == null || TextUtils.isEmpty(t.toString())) {
            throw new ApiException(ErrorCode.HTTP_RESULT_ERROR, "数据错误");
        } else if (t instanceof Response) {
            Response response = (Response) t;
            int code = response.getCode();
            boolean error = (code != 0 && code != 10000 && !response.isSuccess());
            if (error) {
                throw new ApiException(code, response.getMessage());
            }
        }
        return t;
    }

    //==========================同步==============================

    /**
     * 处理同步网络请求,子线程中
     */
    @Override
    public <T> T doHttpDealSync(Call<T> call) {
        retrofit2.Response<T> response = doHttpDealSyncResponse(call);
        return response != null ? response.body() : null;
    }

    @Override
    public <T> retrofit2.Response<T> doHttpDealSyncResponse(Call<T> call) {
        try {
            return call.execute();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

}
