package com.kaolafm.opensdk.api.search.model;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * 语义搜索返回的节目数据
 *
 * <AUTHOR>
 * @date 2018/8/7
 */

public class VoiceSearchProgramBean implements Parcelable {

    /**
     {
     "id": 1000026405264,
     "name": "《我要旅游》 郭德纲 于谦",
     "img": "http://iovimg.radio.cn/mz/images/202104/c2fbd3e6-9c88-42af-aa2b-c10cbf6c2872/default.jpg",
     "type": 1,
     "albumName": "中国相声榜",
     "source": 34,
     "duration": 509753,
     "playUrl": "http://iovimage.radio.cn/mz/aac_64/202104/5ea47084-67e0-4303-9406-31bb5d10adb2.aac",
     "oldId": 0,
     "sourceName": "Unknown",
     "callback": "19_analyzer_01_1650435151546253",
     "fine": 0,
     "vip": 0,
     "audition": 0,
     "totalNumber": 0,
     "comperes": [],
     "freq": ""
     }
     */

    /**资源Id*/
    @SerializedName("id")
    protected Long id;

    /** 节目名*/
    @SerializedName("name")
    protected String name;

    /** 图片url*/
    @SerializedName("img")
    protected String img;

    /** 主持人列表*/
    @SerializedName(value = "comperes", alternate = {"host"})
    protected List<Compere> comperes;

    /** 资源类型 0为专辑;1为单曲;11为广播;3为电台;1003,qq音乐场景电台;1004,qq音乐标签电台{@link com.kaolafm.opensdk.ResType}*/
    @SerializedName("type")
    protected Integer type;

    /** 资源单曲所属专辑名称, 资源是广播时就是广播的名称。可为空。*/
    @SerializedName("albumName")
    protected String albumName;

    /** 时长*/
    @SerializedName("duration")
    protected Long duration;


    /** 播放的url */
    @SerializedName("playUrl")
    protected String playUrl;

    /** 专辑是否vip */
    @SerializedName("vip")
    protected Integer vip;

    /** 是否需要付费 */
    @SerializedName("fine")
    protected Integer fine;

    /** 专辑是否试听  */
    @SerializedName("audition")
    protected Integer audition;

    @SerializedName("freq")
    protected String freq;

    /** 添加服务器透传数据.供用户搜索数据上报使用*/
    @SerializedName("callback")
    protected String callback;

    /** 运营语 - 专辑/AI电台/专题 */
    @SerializedName("recommend")
    protected String recommend ;

    /** 正在播放的节目信息标题 广播/电视 */
    @SerializedName("currentProgram")
    protected String currentProgram;

    public VoiceSearchProgramBean() {
    }

    protected VoiceSearchProgramBean(Parcel in) {
        if (in.readByte() == 0) {
            id = null;
        } else {
            id = in.readLong();
        }
        name = in.readString();
        img = in.readString();
        comperes = in.createTypedArrayList(Compere.CREATOR);
        if (in.readByte() == 0) {
            type = null;
        } else {
            type = in.readInt();
        }
        albumName = in.readString();
        if (in.readByte() == 0) {
            duration = null;
        } else {
            duration = in.readLong();
        }
        playUrl = in.readString();
        if (in.readByte() == 0) {
            vip = null;
        } else {
            vip = in.readInt();
        }
        if (in.readByte() == 0) {
            fine = null;
        } else {
            fine = in.readInt();
        }
        if (in.readByte() == 0) {
            audition = null;
        } else {
            audition = in.readInt();
        }
        freq = in.readString();
        callback = in.readString();
        recommend = in.readString();
        currentProgram = in.readString();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        if (id == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeLong(id);
        }
        dest.writeString(name);
        dest.writeString(img);
        dest.writeTypedList(comperes);
        if (type == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeInt(type);
        }
        dest.writeString(albumName);
        if (duration == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeLong(duration);
        }
        dest.writeString(playUrl);
        if (vip == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeInt(vip);
        }
        if (fine == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeInt(fine);
        }
        if (audition == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeInt(audition);
        }
        dest.writeString(freq);
        dest.writeString(callback);
        dest.writeString(recommend);
        dest.writeString(currentProgram);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<VoiceSearchProgramBean> CREATOR = new Creator<VoiceSearchProgramBean>() {
        @Override
        public VoiceSearchProgramBean createFromParcel(Parcel in) {
            return new VoiceSearchProgramBean(in);
        }

        @Override
        public VoiceSearchProgramBean[] newArray(int size) {
            return new VoiceSearchProgramBean[size];
        }
    };

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public List<Compere> getComperes() {
        return comperes;
    }

    public void setComperes(List<Compere> comperes) {
        this.comperes = comperes;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getAlbumName() {
        return albumName;
    }

    public void setAlbumName(String albumName) {
        this.albumName = albumName;
    }

    public Long getDuration() {
        return duration;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }

    public String getPlayUrl() {
        return playUrl;
    }

    public void setPlayUrl(String playUrl) {
        this.playUrl = playUrl;
    }

    public Integer getVip() {
        return vip;
    }

    public void setVip(Integer vip) {
        this.vip = vip;
    }

    public Integer getFine() {
        return fine;
    }

    public void setFine(Integer fine) {
        this.fine = fine;
    }

    public Integer getAudition() {
        return audition;
    }

    public void setAudition(Integer audition) {
        this.audition = audition;
    }

    public String getFreq() {
        return freq;
    }

    public void setFreq(String freq) {
        this.freq = freq;
    }

    public String getCallback() {
        return callback;
    }

    public void setCallback(String callback) {
        this.callback = callback;
    }

    public static Creator<VoiceSearchProgramBean> getCREATOR() {
        return CREATOR;
    }

    public String getRecommend() {
        return recommend;
    }

    public void setRecommend(String recommend) {
        this.recommend = recommend;
    }

    public String getCurrentProgram() {
        return currentProgram;
    }

    public void setCurrentProgram(String currentProgram) {
        this.currentProgram = currentProgram;
    }

    @Override
    public String toString() {
        return "VoiceSearchProgramBean{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", img='" + img + '\'' +
                ", comperes=" + comperes +
                ", type=" + type +
                ", albumName='" + albumName + '\'' +
                ", duration=" + duration +
                ", playUrl='" + playUrl + '\'' +
                ", vip=" + vip +
                ", fine=" + fine +
                ", audition=" + audition +
                ", freq=" + freq +
                ", callback='" + callback + '\'' +
                ", recommend='" + recommend + '\'' +
                ", currentProgram='" + currentProgram + '\'' +
                '}';
    }
}
