package com.kaolafm.opensdk.demo.history;

import android.content.Intent;
import android.os.Bundle;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.widget.Button;
import butterknife.BindView;
import butterknife.OnClick;
import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.api.history.HistoryRequest;
import com.kaolafm.opensdk.api.history.model.ListeningHistory;
import com.kaolafm.opensdk.demo.BaseActivity;
import com.kaolafm.opensdk.demo.R;
import com.kaolafm.opensdk.demo.live.LiveActivity;
import com.kaolafm.opensdk.demo.player.AlbumPlayerActivity;
import com.kaolafm.opensdk.demo.player.AudioPlayerActivity;
import com.kaolafm.opensdk.demo.player.BasePlayerActivity;
import com.kaolafm.opensdk.demo.player.BroadcastPlayerActivity;
import com.kaolafm.opensdk.demo.player.RadioPlayerActivity;
import com.kaolafm.opensdk.demo.player.TVPlayerActivity;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import java.util.List;

/**
 * 收听历史页面
 *
 * <AUTHOR> Yan
 * @date 2019/3/8
 */
public class HistoryActivity extends BaseActivity {

    @BindView(R.id.btn_clear_history)
    Button mBtnClearHistory;

    @BindView(R.id.rv_history_list)
    RecyclerView mRvHistoryList;

    private HistoryAdapter mHistoryAdapter;

    @Override
    public int getLayoutId() {
        return R.layout.activity_history;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        setTitle("收听历史");
        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(this);
        mRvHistoryList.setLayoutManager(linearLayoutManager);
        mHistoryAdapter = new HistoryAdapter();
        mRvHistoryList.setAdapter(mHistoryAdapter);
        mHistoryAdapter.setOnItemClickListener((view, viewType, listeningHistory, position) -> {
            play(listeningHistory);
        });
        mRvHistoryList.addItemDecoration(new DividerItemDecoration(this, DividerItemDecoration.VERTICAL));
    }

    @OnClick(R.id.btn_clear_history)
    public void onViewClicked() {
        new HistoryRequest().clearListeningHistory(new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean aBoolean) {
                if (aBoolean) {
                    showToast("清空历史成功");
                    mHistoryAdapter.clear();
                }else {
                    showToast("清空历史失败");
                }
            }

            @Override
            public void onError(ApiException exception) {
                showError("清空历史错误", exception);
            }
        });
    }

    private void play(ListeningHistory listeningHistory) {
        int type = listeningHistory.getType();
        Class activity = null;
        String id = "";
        switch (type) {
            case ResType.TYPE_ALBUM:
                activity = AlbumPlayerActivity.class;
                id = listeningHistory.getRadioId();
                break;
            case ResType.TYPE_AUDIO:
                activity = AudioPlayerActivity.class;
                id = listeningHistory.getAudioId();
                break;
            case ResType.TYPE_RADIO:
                activity = RadioPlayerActivity.class;
                id = listeningHistory.getRadioId();
                break;
            case ResType.TYPE_LIVE:
                activity = LiveActivity.class;
//                id = listeningHistory.getRadioId();
                break;
            case ResType.TYPE_BROADCAST:
                activity = BroadcastPlayerActivity.class;
                id = listeningHistory.getRadioId();
                break;
            case ResType.TYPE_TV:
                activity = TVPlayerActivity.class;
                id = listeningHistory.getRadioId();
                break;
            default:
        }
        Intent intent = new Intent(this, activity);
        intent.putExtra(BasePlayerActivity.KEY_ID, id);
        startActivity(intent);
    }

    @Override
    public void initData() {
        new HistoryRequest().getHistoryList(new HttpCallback<List<ListeningHistory>>() {
            @Override
            public void onSuccess(List<ListeningHistory> listeningHistories) {
                if (mHistoryAdapter != null) {
                    mHistoryAdapter.setDataList(listeningHistories);
                }
            }

            @Override
            public void onError(ApiException exception) {
                showError("获取收听历史列表失败", exception);
            }
        });
    }
}
