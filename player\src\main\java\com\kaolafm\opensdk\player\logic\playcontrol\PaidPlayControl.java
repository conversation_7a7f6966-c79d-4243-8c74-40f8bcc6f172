package com.kaolafm.opensdk.player.logic.playcontrol;

import android.text.TextUtils;
import android.util.Log;

import com.kaolafm.opensdk.api.media.AudioRequest;
import com.kaolafm.opensdk.api.media.model.AudioFileInfo;
import com.kaolafm.opensdk.api.media.model.AudioPlayInfo;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.core.ijk.VideoView;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

import java.util.List;

/**
 * 支付播放控制
 * <AUTHOR> shi qian
 */
public abstract class PaidPlayControl extends BasePlayControl {

    //加密音频播放测试
//            String testUrl = "https://ytmedia.radio.cn/CCYT%2F2021%2F08%2F02%2F1627892285de0d974ced6fefe3381f20be9d8cd70alc.mp3";
////            String testUrl = Environment.getExternalStorageDirectory().getAbsolutePath() +"/Download/decode_test.mp3";
////            String testUrl = Environment.getExternalStorageDirectory().getAbsolutePath() +"/Download/encode_test.mp3";
//            Log.i("start play", "testUrl:" + testUrl);
////            String testUrl = "/sdcard/Download/encode_test.mp3";
//            playItem.setPlayUrl(testUrl);
//            playWithPosition(playItem);
    // 由于添加了付费的内容播放，专辑内容需要单独请求播放的url地址
    @Override
    void requestPlayUrl(PlayItem playItem, OnGetPlayUrlData callback) {
        new AudioRequest().getAudioPlayInfo(playItem.getPlayUrlId(), new HttpCallback<AudioPlayInfo>() {
            @Override
            public void onSuccess(AudioPlayInfo audioPlayInfo) {
                List<AudioFileInfo> playList = audioPlayInfo.getPlayInfoList();
                if (playList == null) {
                    onError(new ApiException(PlayerConstants.ERROR_CODE_PLAY_LIST_URL_NULL, "playList is null"));
                    return;
                }
                //设置并回调播放地址
                setPlayUrl(playItem, playList);
                callback.onDataGet(playItem.getPlayUrl());
            }

            @Override
            public void onError(ApiException e) {
                Log.i("BasePlayControl", "getPlayUrl error:" + e.toString());
                stop();
                if (e.getCode() == PlayerConstants.ERROR_CODE_NO_COPYRIGHT){
                    // code - 50816, message - 因版权原因，暂时无法播放
                    if(mBasePlayControlListener != null){
                        mBasePlayControlListener.onPlayerFailed(playItem, PlayerConstants.ERROR_CODE_NO_COPYRIGHT, e.getCode());
                    }
                    return;
                }
                if (e.getCode() == PlayerConstants.ERROR_CODE_PLAY_LIST_URL_NULL) {
                    if(mBasePlayControlListener != null){
                        mBasePlayControlListener.onPlayerFailed(playItem, PlayerConstants.ERROR_CODE_PLAY_LIST_URL_NULL, 404);
                    }
                    return;
                }
                if(mBasePlayControlListener != null){
                    mBasePlayControlListener.onPlayerFailed(playItem, PlayerConstants.ERROR_CODE_PLAY_LIST_URL_DECODE, e.getCode());
                }
            }
        });
    }

}
