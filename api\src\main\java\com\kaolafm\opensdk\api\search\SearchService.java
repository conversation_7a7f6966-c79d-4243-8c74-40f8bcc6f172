package com.kaolafm.opensdk.api.search;

import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.KaolaApiConstant;
import com.kaolafm.opensdk.api.search.internal.HotWords;
import com.kaolafm.opensdk.api.search.model.SearchProgramBean;
import com.kaolafm.opensdk.api.search.model.VoiceSearchResult;

import java.util.List;
import java.util.Map;

import io.reactivex.Single;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.Query;
import retrofit2.http.QueryMap;

/**
 * 语音搜索Service
 * <AUTHOR>
 * @date 2018/8/2
 */
interface SearchService {

    @Headers(ApiHostConstants.SEARCH_DOMAIN_HEADER)
    @GET(KaolaApiConstant.SEARCH_BY_SEMANTICS)
    Single<VoiceSearchResult> searchBySemantics(@QueryMap Map<String, Object> params);

    @Headers(ApiHostConstants.SEARCH_DOMAIN_HEADER)
    @GET(KaolaApiConstant.SEARCH_ALL)
    Single<BaseResult<List<SearchProgramBean>>> searchAll(@Query("q")String keyword);

    @Headers(ApiHostConstants.SEARCH_DOMAIN_HEADER)
    @GET(KaolaApiConstant.SEARCH_BY_TYPE)
    Single<BaseResult<BasePageResult<List<SearchProgramBean>>>> searchByType(@Query("q")String keyword, @Query("rtype")String type, @Query("pagenum")int pageNum, @Query("pagesize")int pageSize);

    @Headers(ApiHostConstants.SEARCH_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_SUGGESTED_WORDS)
    Single<BaseResult<List<String>>> getSuggestedWords(@Query("word")String word);
    @Headers(ApiHostConstants.SEARCH_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_SUGGESTED_WORDS)
    Single<BaseResult<List<String>>> getSuggestedWords(@Query("word")String word,@Query("contentType")String contentType);

    @Headers(ApiHostConstants.SEARCH_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_HOT_WORDS)
    Single<BaseResult<HotWords>> getHotWords();

}
