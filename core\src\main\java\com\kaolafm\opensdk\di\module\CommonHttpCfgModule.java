package com.kaolafm.opensdk.di.module;

import android.app.Application;
import android.os.Environment;
import androidx.annotation.NonNull;

import com.google.gson.TypeAdapterFactory;
import com.kaolafm.opensdk.account.profile.KaolaProfile;
import com.kaolafm.opensdk.account.profile.KaolaProfileManager;
import com.kaolafm.opensdk.account.token.KaolaAccessToken;
import com.kaolafm.opensdk.account.token.KaolaAccessTokenCache;
import com.kaolafm.opensdk.account.token.RealAccessTokenManager;
import com.kaolafm.opensdk.account.token.TokenCache;
import com.kaolafm.opensdk.di.qualifier.AccessTokenQualifier;
import com.kaolafm.opensdk.di.qualifier.CacheDir;
import com.kaolafm.opensdk.di.qualifier.DomainMapQualifier;
import com.kaolafm.opensdk.di.qualifier.DomainQualifier;
import com.kaolafm.opensdk.di.qualifier.HandlerInterceptor;
import com.kaolafm.opensdk.di.qualifier.HttpInterceptor;
import com.kaolafm.opensdk.di.qualifier.ProfileQualifier;
import com.kaolafm.opensdk.di.scope.AppScope;
import com.kaolafm.opensdk.http.cache.Cache;
import com.kaolafm.opensdk.http.cache.CacheType;
import com.kaolafm.opensdk.http.cache.IntelligentCache;
import com.kaolafm.opensdk.http.cache.LruCache;
import com.kaolafm.opensdk.http.core.HttpBeforeHandler;
import com.kaolafm.opensdk.http.core.HttpHandler;
import com.kaolafm.opensdk.http.core.IRepositoryManager;
import com.kaolafm.opensdk.http.core.RepositoryManager;
import com.kaolafm.opensdk.http.core.RequestInterceptor;
import com.kaolafm.opensdk.http.error.ResponseErrorListener;
import com.kaolafm.opensdk.http.urlmanager.UrlManager;
import com.kaolafm.opensdk.http.urlmanager.UrlManagerImpl;
import com.kaolafm.opensdk.log.LogLevel;
import com.kaolafm.opensdk.log.Logging;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import dagger.Binds;
import dagger.Module;
import dagger.Provides;
import dagger.multibindings.IntoMap;
import dagger.multibindings.IntoSet;
import dagger.multibindings.Multibinds;
import dagger.multibindings.StringKey;
import okhttp3.HttpUrl;
import okhttp3.Interceptor;

/**
 * 通用的http配置module
 *
 * <AUTHOR> Yan
 * @date 2020-01-13
 */
@Module
public abstract class CommonHttpCfgModule {

    @Provides
    @AppScope
    static HttpUrl provideApiUrl() {
        return HttpUrl.parse("https://iovopen-prelease.radio.cn");
//        return HttpUrl.parse("https://26e9a8ef-3e24-456a-9bc7-2a4479e5bf57.mock.pstmn.io");
    }

    @Binds
    @HandlerInterceptor
    abstract Interceptor bindHttpHandlerInterceptor(HttpHandler httpHandler);

    @Binds
    @IntoSet
    abstract HttpBeforeHandler bindUrlManager(@AppScope UrlManagerImpl manager);

    @Binds
    abstract UrlManager bindUrlManagerImpl(@AppScope UrlManagerImpl manager);

    @Binds
    @AppScope
    abstract Interceptor bindInterceptor(RequestInterceptor requestInterceptor);

    @Multibinds
    abstract Set<TypeAdapterFactory> provideTypeAdapterFactory();

    @Multibinds
    @HttpInterceptor
    abstract Set<Interceptor> bindHttpInterceptor();

    @Provides
    @AppScope
    @CacheDir
    static File provideCacheFile(Application application) {
        if (Environment.getExternalStorageDirectory().getPath().equals(Environment.MEDIA_MOUNTED)) {
            File file = application.getExternalCacheDir();
            if (file == null) {
                file = new File("/mnt/sdcard/" + application.getPackageName());
                if (!file.exists()) {
                    file.mkdirs();
                }
            }
            return file;
        } else {
            return application.getCacheDir();
        }
    }

    @Provides
    @AppScope
    static LogLevel.RequestLevel providePrintHttpLogLevel() {
        return Logging.getRequestLevel();
    }

    @Provides
    @IntoSet
    static ResponseErrorListener provideErrorListenerList() {
        return exception -> { };
    }

    @Provides
    @AppScope
    static List<ResponseErrorListener> provideResponseErrorListeners(Set<ResponseErrorListener> listenerSet) {
        return new ArrayList<>(listenerSet);
    }

    @Provides
    @AppScope
    static Cache.Factory provideCacheFactory(Application application) {
        return new Cache.Factory() {
            @NonNull
            @Override
            public <K, V> Cache<K, V> build(CacheType type) {
                switch (type.getCacheTypeId()) {
                    case CacheType.EXTRAS_TYPE_ID:
                    case CacheType.ACTIVITY_CACHE_TYPE_ID:
                    case CacheType.FRAGMENT_CACHE_TYPE_ID:
                        return new IntelligentCache(type.calculateCacheSize(application));
                    default:
                        return new LruCache<>(type.calculateCacheSize(application));
                }
            }
        };
    }

    /**
     * 提供IRepositoryManager实例，这种方式和@Provides是一样的效果，
     * 就是不需要手动new，dagger2会直接找RepositoryManager的构造函数。实现接口只能用bind这个方式，不然该实现类内部的inject不起作用。
     *
     * @param repositoryManager
     * @return
     */
    @Binds
    @AppScope
    abstract IRepositoryManager bindRepositoryManager(RepositoryManager repositoryManager);

    @Provides
    @ProfileQualifier
    static KaolaProfile provideProfile(@AppScope KaolaProfileManager profileManager) {
        return profileManager.getProfile();
    }

    /**
     * KaolaAccessTokenCache实例注入到map集合中，在{@link AccessTokenManager}中使用。实现接口只能用bind这个方式
     * @param tokenCache
     * @return
     */
    @Binds
    @IntoMap
    @AccessTokenQualifier
    @StringKey(RealAccessTokenManager.TOKEN_KAOLA)
    abstract TokenCache provideKaolaAccessTokenCache(KaolaAccessTokenCache tokenCache);

    @Provides
    @AccessTokenQualifier
    static KaolaAccessToken provideKaolaAccessToken(@AppScope RealAccessTokenManager accessTokenManager) {
        return accessTokenManager.getKaolaAccessToken();
    }

    /**Dagger2 自动生成的Map无法修改，但是需要修改Map，手动往里添加域名*/
    @Provides
    @DomainMapQualifier
    static Map<String, String> provideDomains(@DomainQualifier Map<String, String> domains) {
        return new HashMap<>(domains);
    }
}
