package com.kaolafm.opensdk;

import android.Manifest;
import android.app.Application;
import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Handler;
import android.os.Message;
import androidx.core.content.ContextCompat;
import android.util.Log;

import com.kaolafm.ad.AdvertOptions;
import com.kaolafm.base.utils.SeCoreUtils;
import com.kaolafm.base.utils.SpUtil;
import com.kaolafm.opensdk.crash.DeviceIdException;
import com.kaolafm.opensdk.di.component.ComponentKit;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.permission.LocationUtil;

import java.util.List;

/**
 * OpenSDK初始化。合并打包时
 *
 * <AUTHOR>
 * @date 2018/4/13
 */
public final class OpenSDK {

    private static volatile OpenSDK mInstance;

    private Engine mEngine;
    private OpenSDK() {
        mEngine = new OpenSDKEngine();
    }

    public static OpenSDK getInstance() {
        if (mInstance == null) {
            synchronized (OpenSDK.class) {
                if (mInstance == null) {
                    mInstance = new OpenSDK();
                }
            }
        }
        return mInstance;
    }

    public Engine getmEngine() {
        return mEngine;
    }
    public void getLocation(){
        LocationUtil.instance.getLocation();
    }

    public Application getContext() {
        return ComponentKit.getInstance().getApplication();
    }

    public void initSDK(Application application,String deviceId) throws DeviceIdException {
        initSDK(application, deviceId,null);
    }

    /**
     * 初始化sdk
     */
    public void initSDK(Application application,String deviceId, HttpCallback<Boolean> callback) throws DeviceIdException {
        initSDK(application, deviceId,AdvertOptions.DEFAULT, callback);
    }

    public void initSDK(Application application, String deviceId,Options options, HttpCallback<Boolean> callback) throws DeviceIdException {
        Log.i("initSDK:deviceId=",deviceId);
        if(!deviceId.isEmpty()){
            if(deviceId.length()>64){
                throw new DeviceIdException("the deviceId‘s length > 64");
            }
            SeCoreUtils.getInstance().SeCoreInit(application);
            SpUtil.init(application);
            options.setDeviceId(deviceId);
            mEngine.init(application, options, callback);
        }else {
            throw new DeviceIdException("the deviceId cannot be empty");
        }
    }

    /**
     * 添加配置项，该方法会初始化后直接进行激活，不需要在调用其他初始化或激活接口。
     *
     * @param application
     * @param options
     * @param callback
     */
    public void config(Application application,String deviceId, Options options, HttpCallback<Boolean> callback) {
        SpUtil.init(application);
        options.setDeviceId(deviceId);
        mEngine.config(application, options, callback);
    }
    /***
     * 激活设备
     */
    public void activate(HttpCallback<Boolean> callback) {
        mEngine.activate(callback);
    }
    /**
     * 判断设备是否已经激活。
     *
     * @return
     */
    public boolean isActivate() {
        return mEngine.isActivated();
    }

    /**
     * 初始化后激活
     *
     * @param application
     * @param callback
     */
    public void initAndActivate(Application application,String deviceId, HttpCallback<Boolean> callback) throws DeviceIdException {
        initSDK(application, deviceId,null);
        activate(callback);
    }

    public String getDeviceId() {
        if (mEngine instanceof BaseEngine) {
            return ((BaseEngine) mEngine).getDeviceId();
        }
        return null;
    }

    private void setDeviceId(String deviceId) {
        if (mEngine instanceof BaseEngine) {
            ((BaseEngine) mEngine).setDeviceId(deviceId);
        }
    }
    public void setLocation(String lng, String lat){
        if (mEngine instanceof BaseEngine) {
            ((BaseEngine) mEngine).setLocation(lng,lat);
        }
    }

    /**
     * 释放资源，一般在App退出时调用
     */
    public void release() {
        mEngine.release();
    }

}
