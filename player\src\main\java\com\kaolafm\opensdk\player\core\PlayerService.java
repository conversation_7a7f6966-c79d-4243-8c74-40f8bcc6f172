package com.kaolafm.opensdk.player.core;

import android.app.Service;
import android.content.Intent;
import android.media.AudioManager;
import android.os.Binder;
import android.os.IBinder;
import android.text.TextUtils;
import android.util.Log;

import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.player.core.ijk.VideoView;
import com.kaolafm.opensdk.player.core.listener.IPlayerAsncStartExecutingListener;
import com.kaolafm.opensdk.player.core.listener.IPlayerBufferProgressListener;
import com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener;
import com.kaolafm.opensdk.player.core.listener.IPlayerStateCoreListener;
import com.kaolafm.opensdk.player.core.listener.IPlayerStateVideoCoreListener;
import com.kaolafm.opensdk.player.core.listener.OnAudioFocusChangeInner;
import com.kaolafm.opensdk.player.core.listener.OnAudioFocusChangeInter;
import com.kaolafm.opensdk.player.core.model.AAudioFocus;
import com.kaolafm.opensdk.player.core.model.AMediaPlayer;
import com.kaolafm.opensdk.player.core.model.AudioFadeConfig;
import com.kaolafm.opensdk.player.core.utils.AudioFocusManager;
import com.kaolafm.opensdk.player.core.utils.ContextMediaPlayer;
import com.kaolafm.opensdk.player.core.utils.PlayerCustomizeManager;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.IPlayerStateVideoListener;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;

import java.util.concurrent.Callable;

import io.reactivex.Single;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.functions.Consumer;
import io.reactivex.schedulers.Schedulers;

import static android.media.AudioManager.AUDIOFOCUS_LOSS;
import static android.media.AudioManager.AUDIOFOCUS_LOSS_TRANSIENT;
import static android.media.AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK;
import static android.media.AudioManager.AUDIOFOCUS_NONE;

/**
 * <AUTHOR> on 2019-05-24.
 */

public class PlayerService extends Service {
    private String TAG = PlayerService.class.getSimpleName();

    private ContextMediaPlayer mContextMediaPlayer;
    private String mPlayingUri;
    private boolean mLastIsVideo = false;
    private String mPlayUrlId;
    private boolean mIsLiving;
    private AAudioFocus mAudioFocusManager;
    private OnAudioFocusChangeInter mIAudioFocusListener;
    /**
     * 是否失去了音频焦点
     */
    private boolean isLoseAudioFocus = true;

    /**
     * 焦点变化时, 播放状态
     */
    private int mPrePlayStatus;

    /**
     * 上次焦点状态
     */
    private int mPreFocusChange = AUDIOFOCUS_NONE;
    /**
     * 当前的音频焦点
     */
    private int mCurrentAudioFocusState = AUDIOFOCUS_NONE;

    private IPlayerStateCoreListener mIPlayerStateCoreListener;
//    private IPlayerStateVideoListener mIPlayerStateVideoListener;

//    private IPlayerAsncStartExecutingListener mIPlayerAsncStartExecutingListener;

    /**
     * 是否启用淡入淡出
     */
    private boolean audioFadeEnabled = true;

    private String httpProxy = null;

    // 是否清除dns缓存
    private boolean clearDnsCache = false;

    /**
     * 淡入淡出效果配置信息
     */
    private AudioFadeConfig audioFadeConfig;

    /**
     * 异步开始播放正在执行标记
     */
//    private volatile boolean mIsAsyncStartExecuting = false;

    @Override
    public IBinder onBind(Intent intent) {
        return new PlayerServiceBinder();
    }

    @Override
    public void onCreate() {
        super.onCreate();
        initContextMediaPlayer();
    }

    private void initContextMediaPlayer() {
        mContextMediaPlayer = new ContextMediaPlayer();
    }


    public class PlayerServiceBinder extends Binder {
        public void initPlayer() {
            initPlayerInner();
        }

        public AMediaPlayer getMediaPlayer() {
            return mContextMediaPlayer.getMediaPlayer();
        }

        public void start(String uri, long duration) {
            start(uri, duration, 0, false, null, null);
        }

        public void start(String uri, long duration, boolean isLiving) {
            start(uri, duration, 0, isLiving, null, null);
        }

        public void start(String uri, long duration, boolean isLiving, VideoView videoView) {
            start(uri, duration, 0, isLiving, videoView, null);
        }

        public void start(String uri, long duration, long position, boolean isLiving) {
            start(uri, duration, position, isLiving, null, null);
        }

        public void start(String uri, long duration, long position, boolean isLiving, VideoView videoView) {
            start(uri, duration, position, isLiving, videoView, null);
        }

        public void start(String uri, long duration, long position, boolean isLiving, VideoView videoView, String playUrlId) {
            PlayerLogUtil.log(getClass().getSimpleName(), "PlayService.start, processId= "+android.os.Process.myTid());
            startInner(uri, duration, position, isLiving, videoView, playUrlId);
        }

        public void setPosition(long sec) {
            setPositionInner(sec);
        }

        public void pause() {
            pauseInner();
        }

        public void play() {
            playInner();
        }

        public void stop() {
            stopInner();
        }

        public void reset(boolean needResetLastPlaybackRateFlag) {
            resetInner(needResetLastPlaybackRateFlag);
        }
        public void rePlay() {
            rePlayInner();
        }

        public void release() {
            releaseInner();
        }

        public void seek(long mSec) {
            seekInner(mSec);
        }

        public void setPlayerStateListener(IPlayerStateCoreListener iPlayerState) {
            setPlayerStateListenerInner(iPlayerState);
        }

        public void setPlayerStateVideoListener(IPlayerStateVideoCoreListener iPlayerStateVideoCoreListener) {
            setPlayerStateVideoListenerInner(iPlayerStateVideoCoreListener);
        }

//        public void setPlayerAsncStartExecutingListener(IPlayerAsncStartExecutingListener mIPlayerAsncStartExecutingListener) {
//            setPlayerAsncStartExecutingListenerInner(mIPlayerAsncStartExecutingListener);
//        }

        public void setPlayerBufferProgressListener(IPlayerBufferProgressListener listener) {
            setPlayerBufferProgressListenerInner(listener);
        }

        public void setInitCompleteListener(IPlayerInitCompleteListener iPlayerInitCompleteListener) {
            setInitCompleteListenerInner(iPlayerInitCompleteListener);
        }

        public void setLoudnessNormalization(int active) {
            setLoudnessNormalizationInner(active);
        }

        public boolean isPlaying() {
            return isPlayingInner();
        }

        public int getPlayStatus() {
            return getPlayStatusInner();
        }

        public boolean requestAudioFocus() {
            return requestAudioFocusInner();
        }

        public boolean abandonAudioFocus() {
            return abandonAudioFocusInner();
        }

        public void setCustomAudioFocus(AAudioFocus audioFocus) {
            setCustomAudioFocusInner(audioFocus);
        }

        public void setAudioFocusListener(OnAudioFocusChangeInter iAudioFocusListener) {
            setAudioFocusListenerInner(iAudioFocusListener);
        }

        public long getCurrentPosition() {
            return getCurrentPositionInner();
        }

        public void setMediaVolume(float leftVolume, float rightVolume) {
            setMediaVolumeInner(leftVolume, rightVolume);
        }
        public void setPlaybackRate(float rate) {
            setPlaybackRateInner(rate);
        }
        public float getPlaybackRate() {
            return getPlaybackRateInner();
        }

        public void setUsageAndContentType(int usage, int contentType) {
            setUsageAndContentTypeInner(usage, contentType);
        }

        public void setLogInValid() {
            setLogInValidInner();
        }

        public void setPlayUrl(String uri, long position, long duration, VideoView videoView) {
            setPlayUrlInner(uri, position, duration, videoView);
        }

        public void disableAudioFade() {
            setAudioFadeEnabledInner(false);
        }

        public void setHttpProxy(String httpProxy) {
            setHttpProxyInner(httpProxy);
        }

        public void clearHttpProxy() {
            setHttpProxyInner(null);
        }

        public void clearDnsCache(boolean clearDnsCache) {
            setClearDnsCacheInner(clearDnsCache);
        }

        public void setAudioFadeConfig(AudioFadeConfig audioFadeConfig) {
            PlayerLogUtil.log(getClass().getSimpleName(), "setAudioFadeConfig, audioFadeConfig = " + audioFadeConfig.toString());
            setAudioFadeConfigInner(audioFadeConfig);
        }

//        public boolean isAsyncStartExecuting() {
//            return mIsAsyncStartExecuting;
//        }

        public void setIsHandleLossFocus(boolean isHandleLossFocus) {
            setHandleLossFocusInner(isHandleLossFocus);
        }
    }

    private void initPlayerInner() {
        mContextMediaPlayer.initPlayer(ContextMediaPlayer.TYPE_IJK_MEDIA_PLAYER, this);
        mAudioFocusManager = new AudioFocusManager(PlayerService.this);
        mAudioFocusManager.setAudioFocusListener(onAudioFocusChangeInner);
    }

    private void startInner(String uri, long duration, long position) {
        // done 此方法调用已检查处理完成
        startInner(uri, duration, position, false, null, null);
    }

    private void startInner(String uri, long duration, long position, boolean isLiving, VideoView videoView) {
        // done 此方法调用已检查处理完成
        startInner(uri, duration, position, isLiving, videoView, null);
    }

    /**
     * 开始第一次播放的核心方法
     * @param uri
     * @param duration
     * @param position
     * @param videoView
     */
    private void startInner(String uri, long duration, long position, boolean isLiving, VideoView videoView, String playUrlId) {
        // done 此方法调用已检查处理完成
        PlayerLogUtil.log(getClass().getSimpleName(), "PlayService.startInner, processId= "+android.os.Process.myTid());
        if (StringUtil.isEmpty(uri)) {
            iPlayerStateCoreListener.onPlayerFailed("", PlayerConstants.ERROR_CODE_PLAY_LIST_URL_NULL, -1, "");
            return;
        }

//        if (mIsAsyncStartExecuting) {
//            Log.i(TAG, "播放器正在异步处理音频开始播放逻辑，跳过uri= " + uri + " 的音频播放请求");
//            return;
//        }
        // 日志检索：tag:player_log_tag message:PlaybackRate
        String newPlayUrlId = generateIdentByUrlAndId(uri, playUrlId);
        boolean isVideo = videoView != null;
        boolean needUseLastPlaybackRate =
                !isLiving
                        && isVideo
                        && !TextUtils.isEmpty(mPlayUrlId)
                        && !TextUtils.isEmpty(newPlayUrlId)
                        && TextUtils.equals(mPlayUrlId, newPlayUrlId);

        mIsLiving = isLiving;
        mPlayingUri = uri;

        boolean shouldResetPlaybackRate = isVideo || !mLastIsVideo;

        PlayerLogUtil.log(getClass().getSimpleName(), "PlayService.startInner, needUseLastPlaybackRate=" + needUseLastPlaybackRate + ", shouldResetPlaybackRate=" + shouldResetPlaybackRate + ", isVideo=" + isVideo);

        if (shouldResetPlaybackRate){
            mPlayUrlId = newPlayUrlId;
        }
        mLastIsVideo = isVideo;

        PlayerCustomizeManager playerCustomizeManager = PlayerCustomizeManager.getInstance();
        if (playerCustomizeManager.disposePlay()) {
            // 阻断之后，获取音频焦点还能从此继续播放
            setPlayUrlInner(uri, position, duration, videoView);
            return;
        }

        if (!checkAudioFocus()) {
            setPlayUrlInner(uri, position, duration, videoView);
            return;
        }

        int streamTypeChannel = playerCustomizeManager.getStreamChannel();
        synchronized (PlayerService.class){
            PlayerLogUtil.log(getClass().getSimpleName(), "PlayService.startInner.MediaPlayer.start, processId= "+android.os.Process.myTid());
            mContextMediaPlayer.getMediaPlayer().start(uri, duration, position, streamTypeChannel, audioFadeEnabled, audioFadeConfig, httpProxy, clearDnsCache, videoView, needUseLastPlaybackRate, shouldResetPlaybackRate);
        }

//        synchronized (PlayerService.class) {
//            PlayerLogUtil.log(getClass().getSimpleName(), "startInner", "async start executing.");
//            mIsAsyncStartExecuting = true;
//            Single.fromCallable(new Callable<Boolean>() {
//                @Override
//                public Boolean call() throws Exception {
//                    // 启用音频淡入淡出效果
////                    mContextMediaPlayer.getMediaPlayer().setAudioFadeEnabled(audioFadeEnabled);
////                    mContextMediaPlayer.getMediaPlayer().setAudioFadeConfig(audioFadeConfig);
//                    PlayerLogUtil.log(getClass().getSimpleName(), "PlayService.startInner.call, processId= "+android.os.Process.myTid());
//                    mContextMediaPlayer.getMediaPlayer().start(uri, duration, position, streamTypeChannel, audioFadeEnabled, audioFadeConfig, httpProxy, clearDnsCache, videoView);
//                    return true;
//                }
//            }).subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread()).subscribe(new Consumer<Boolean>() {
//                @Override
//                public void accept(Boolean aBoolean) throws Exception {
//                    synchronized (PlayerService.class) {
//                        PlayerLogUtil.log(getClass().getSimpleName(), "startInner", "async start executing end.");
//                        mIsAsyncStartExecuting = false;
//                        if (mIPlayerAsncStartExecutingListener != null) {
//                            mIPlayerAsncStartExecutingListener.onAsyncStartExecuteFinished();
//                        }
//                    }
//                }
//            }, new Consumer<Throwable>() {
//                @Override
//                public void accept(Throwable throwable) throws Exception {
//                    PlayerLogUtil.log(getClass().getSimpleName(), "startInner", "mediaPlayer start error.");
//                    mIsAsyncStartExecuting = false;
//                    throwable.printStackTrace();
//                }
//            });
//        }
    }

    private String generateIdentByUrlAndId(String url, String playUrlId){
        if (!TextUtils.isEmpty(playUrlId)){
            return playUrlId;
        }
        if (!TextUtils.isEmpty(url)){
            return url;
        }
        return "";
    }

    private void setPositionInner(long sec) {
        mContextMediaPlayer.getMediaPlayer().seek(sec);
    }

    private void pauseInner() {
        mContextMediaPlayer.getMediaPlayer().pause();
    }

    private void playInner() {
        if (PlayerCustomizeManager.getInstance().disposePlay()) {
            return;
        }
        if (!checkAudioFocus()) {
            return;
        }
        mContextMediaPlayer.getMediaPlayer().play();
    }

    private void stopInner() {
        mContextMediaPlayer.getMediaPlayer().stop();
    }

    private void resetInner() {
        resetInner(true);
    }

    private void resetInner(boolean needResetLastPlaybackRateFlag) {
        mContextMediaPlayer.getMediaPlayer().reset(needResetLastPlaybackRateFlag);
    }
    private void rePlayInner() {
        mContextMediaPlayer.getMediaPlayer().rePlay();
    }

    private void releaseInner() {
        mContextMediaPlayer.getMediaPlayer().release();
    }

    private void seekInner(long mSec) {
        mContextMediaPlayer.getMediaPlayer().seek(mSec);
    }

    private void setPlayerStateListenerInner(IPlayerStateCoreListener iPlayerState) {
        mIPlayerStateCoreListener = iPlayerState;
        mContextMediaPlayer.getMediaPlayer().setPlayerStateListener(iPlayerStateCoreListener);
    }
    private void setPlayerStateVideoListenerInner(IPlayerStateVideoCoreListener iPlayerStateVideoCoreListener) {
        mContextMediaPlayer.getMediaPlayer().setPlayerStateVideoListener(iPlayerStateVideoCoreListener);
    }

//    private void setPlayerAsncStartExecutingListenerInner(IPlayerAsncStartExecutingListener mIPlayerAsncStartExecutingListener) {
//        this.mIPlayerAsncStartExecutingListener = mIPlayerAsncStartExecutingListener;
//    }

    private void setPlayerBufferProgressListenerInner(IPlayerBufferProgressListener listener) {
        mContextMediaPlayer.getMediaPlayer().setBufferProgressListener(listener);
    }

    private void setInitCompleteListenerInner(IPlayerInitCompleteListener iPlayerInitCompleteListener) {
        mContextMediaPlayer.getMediaPlayer().setInitPlayerInitCompleteListener(iPlayerInitCompleteListener);
    }

    private void setLoudnessNormalizationInner(int active) {
        mContextMediaPlayer.getMediaPlayer().setLoudnessNormalization(active);
    }

    private boolean isPlayingInner() {
        return mContextMediaPlayer.getMediaPlayer().isPlaying();
    }

    private void setCustomAudioFocusInner(AAudioFocus audioFocus) {
        if (audioFocus == null) {
            return;
        }
        mAudioFocusManager = audioFocus;
        audioFocus.setAudioFocusListener(onAudioFocusChangeInner);
    }

    private void setAudioFocusListenerInner(OnAudioFocusChangeInter iAudioFocusListener) {
        mIAudioFocusListener = iAudioFocusListener;
    }

    private boolean requestAudioFocusInner() {
        return mAudioFocusManager.requestAudioFocus();
    }

    private boolean abandonAudioFocusInner() {
        return mAudioFocusManager.abandonAudioFocus();
    }

    private void managerAudioFocusChange(int focusChange) {
        PlayerLogUtil.log(getClass().getSimpleName(), "managerAudioFocusChange", "focusChange = " + focusChange);
        if (focusChange == AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK) {
            if (isPlayingInner()) {
                PlayerLogUtil.log(getClass().getSimpleName(), "managerAudioFocusChange", "is playing");
                mPrePlayStatus = PlayerConstants.TYPE_PLAYER_PLAYING;
                if (!PlayerCustomizeManager.getInstance().disposeAudioFocusChangeDuck()) {
                    setMediaVolumeInner(PlayerConstants.LEFT_VOLUME_MIN, PlayerConstants.RIGHT_VOLUME_MIN);
                }
            } else {
                PlayerLogUtil.log(getClass().getSimpleName(), "managerAudioFocusChange", "is not playing");
                setMediaVolumeInner(PlayerConstants.LEFT_VOLUME_MAX, PlayerConstants.RIGHT_VOLUME_MAX);
                if (mPreFocusChange != focusChange) {
                    mPrePlayStatus = getPlayStatusInner();
                }
            }
        } else if (focusChange == AudioManager.AUDIOFOCUS_GAIN) {
            manageGainFocus();
        } else if (focusChange == AUDIOFOCUS_LOSS || focusChange == AUDIOFOCUS_LOSS_TRANSIENT) {
            // todo fix 解决ZMKQ-5313，路演的临时方案，当焦点变化时，不暂停音频，路演之后要把这里改回来
            if (mIsHandleLossFocus) {
                manageLossFocus();
            } else {
                // fix ZMKQ-5471，即便这里不手动置为0，语音引擎唤起时音频也无法出声
                //setMediaVolumeInner(0, 0);
            }
        }

        mPreFocusChange = focusChange;
    }

    // todo fix v3.0路演的临时方案，当焦点丢失时不暂停音频，加一个设置项判断是否要走临时逻辑，默认为false，路演后删除该逻辑或者改成默认为true
    private boolean mIsHandleLossFocus = false;

    public void setHandleLossFocusInner(boolean isHandleLossFocus) {
        Log.d(TAG, "setHandleLossFocusInner " + isHandleLossFocus);
        mIsHandleLossFocus = isHandleLossFocus;
    }

    private void manageGainFocus() {
        boolean isPauseFromUser = PlayerManager.getInstance().isPauseFromUser();
        PlayerLogUtil.log(getClass().getSimpleName(), "manageGainFocus", "mPreFocusChange = " + mPreFocusChange + ", mPrePlayStatus = " + mPrePlayStatus + ", isPauseFromUser = " + isPauseFromUser);
        //当重新获取焦点后, 检测是否是用户暂停
        if (isPauseFromUser) {
            return;
        }
        if (mPrePlayStatus == PlayerConstants.TYPE_PLAYER_PLAYING) {
            if (mPreFocusChange == AUDIOFOCUS_LOSS_TRANSIENT || mPreFocusChange == AUDIOFOCUS_LOSS) {
                setMediaVolumeInner(PlayerConstants.LEFT_VOLUME_MAX, PlayerConstants.RIGHT_VOLUME_MAX);
                PlayerLogUtil.log(getClass().getSimpleName(), "manageGainFocus", "mIsLiving = " + mIsLiving);
                if (mIsLiving) {
                    startInner(mPlayingUri, 0, 0);
                } else {
                    playInner();
                }
                PlayerCustomizeManager.getInstance().notifyPlayerStateChangedByAudioFocus(true);
            } else if (mPreFocusChange == AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK) {
                setMediaVolumeInner(PlayerConstants.LEFT_VOLUME_MAX, PlayerConstants.RIGHT_VOLUME_MAX);
            }
        }
    }

    private void manageLossFocus() {
        boolean isPlaying = isPlayingInner();
        PlayerLogUtil.log(getClass().getSimpleName(), "manageLossFocus", "isPlaying = " + isPlaying);
        if (isPlaying) {
            pauseInner();
            PlayerCustomizeManager.getInstance().notifyPlayerStateChangedByAudioFocus(false);
        }
    }

    /**
     * 检查音频焦点
     *
     * @return
     */
    private boolean checkAudioFocus() {
        PlayerLogUtil.log(getClass().getSimpleName(), "checkAudioFocus", "isLoseAudioFocus = " + isLoseAudioFocus);
        if (!PlayerCustomizeManager.getInstance().isNeedRequestAudioFocus()) {
            PlayerLogUtil.log(getClass().getSimpleName(), "checkAudioFocus", "no need");
            return true;
        }
        if (isLoseAudioFocus && !requestAudioFocusInner()) {
            return false;
        }
        return true;
    }

    private void saveLostAudioFocusBeforePlayState(int focusChange) {
        if (focusChange != AUDIOFOCUS_LOSS && focusChange != AUDIOFOCUS_LOSS_TRANSIENT) {
            return;
        }
        if (isPlayingInner()) {
            mPrePlayStatus = PlayerConstants.TYPE_PLAYER_PLAYING;
        } else {
            mPrePlayStatus = PlayerConstants.TYPE_PLAYER_IDLE;
        }
        PlayerLogUtil.log(getClass().getSimpleName(), "saveLostAudioFocusBeforePlayState", "mPrePlayStatus=" + mPrePlayStatus);
    }

    private OnAudioFocusChangeInner onAudioFocusChangeInner = new OnAudioFocusChangeInner() {
        @Override
        public void onAudioFocusChange(boolean isUseBySystem, int focusChange) {
            PlayerLogUtil.log(getClass().getSimpleName(), "onAudioFocusChangeInner", "focusChange =" + focusChange);
            isLoseAudioFocus = focusChange != AudioManager.AUDIOFOCUS_REQUEST_GRANTED;
            mCurrentAudioFocusState = focusChange;
            saveLostAudioFocusBeforePlayState(focusChange);
            if (mIAudioFocusListener != null) {
                mIAudioFocusListener.onAudioFocusChange(focusChange);
            }

            if (isUseBySystem) {
                managerAudioFocusChange(focusChange);
            }
        }
    };

    /**
     * 获取播放状态
     *
     * @return
     */
    private int getPlayStatusInner() {
        return mContextMediaPlayer.getMediaPlayer().getPlayStatus();
    }

    /**
     * 获取当前进度
     *
     * @return
     */
    private long getCurrentPositionInner() {
        return mContextMediaPlayer.getMediaPlayer().getCurrentPosition();
    }

    /**
     * 设置音量
     *
     * @param leftVolume
     * @param rightVolume
     */
    private void setMediaVolumeInner(float leftVolume, float rightVolume) {
        mContextMediaPlayer.getMediaPlayer().setMediaVolume(leftVolume, rightVolume);
    }

    private void setPlaybackRateInner(float rate) {
        mContextMediaPlayer.getMediaPlayer().setPlaybackRate(rate);
    }

    private float getPlaybackRateInner() {
        return mContextMediaPlayer.getMediaPlayer().getPlaybackRate();
    }

    /**
     * 设置AudioTrack的 Usage和ContentType
     *
     * @param usage
     * @param contentType
     */
    private void setUsageAndContentTypeInner(int usage, int contentType) {
        mContextMediaPlayer.getMediaPlayer().setUsageAndContentType(usage, contentType);
    }

    private void setLogInValidInner() {
        mContextMediaPlayer.getMediaPlayer().setLogInValid();
    }

    private void setPlayUrlInner(String uri, long position, long duration, VideoView videoView) {
        PlayerLogUtil.log(getClass().getSimpleName(), "setPlayUrlInner", "url= " + uri + "; position = " + position + "; duration = " + duration);

        mContextMediaPlayer.getMediaPlayer().reset(true);

        mContextMediaPlayer.getMediaPlayer().setAutoPlayOnPrepared(false);
        // 启用音频淡入淡出效果
//        mContextMediaPlayer.getMediaPlayer().setAudioFadeEnabled(audioFadeEnabled);
//        mContextMediaPlayer.getMediaPlayer().setAudioFadeConfig(audioFadeConfig);
        PlayerLogUtil.log(getClass().getSimpleName(), "setPlayUrlInner", "audioFadeEnabled= " + audioFadeEnabled + "; audioFadeConfig = " + audioFadeConfig.toString());
        if (videoView != null) {
            mContextMediaPlayer.getMediaPlayer().setDisplay(videoView.getSurfaceHolder());
        } else {
            Log.i(getClass().getSimpleName(), "attention videoView is null");
        }
        mContextMediaPlayer.getMediaPlayer().setDataSource(uri);

        if (duration > 0) {
            mContextMediaPlayer.getMediaPlayer().setDuration(duration, duration);
        }

        if (position > 0) {
            mContextMediaPlayer.getMediaPlayer().seekAtStart(position);
        }
        mContextMediaPlayer.getMediaPlayer().prepare(1);
    }

    private IPlayerStateCoreListener iPlayerStateCoreListener = new IPlayerStateCoreListener() {
        @Override
        public void onIdle(String url) {
            if (mIPlayerStateCoreListener != null) {
                mIPlayerStateCoreListener.onIdle(url);
            }
        }

        @Override
        public void onPlayerPreparingComplete(String url) {
            PlayerLogUtil.log(getClass().getSimpleName(), "onPlayerPreparingComplete", "mCurrentAudioFocusState = " + mCurrentAudioFocusState);

            if(!mIsHandleLossFocus){
                if (mIsLiving) {
                    resetInner();
                } else {
                    pauseInner();
                }
                mPrePlayStatus = PlayerConstants.TYPE_PLAYER_PLAYING;

                setMediaVolumeInner(PlayerConstants.LEFT_VOLUME_MAX, PlayerConstants.RIGHT_VOLUME_MAX);
                if (mIsLiving) {
                    startInner(mPlayingUri, 0, 0);
                } else {
                    playInner();
                }
                PlayerCustomizeManager.getInstance().notifyPlayerStateChangedByAudioFocus(true);

                return;
            }

            if (mCurrentAudioFocusState == AUDIOFOCUS_LOSS || mCurrentAudioFocusState == AUDIOFOCUS_LOSS_TRANSIENT) {
                if (mIsLiving) {
                    resetInner();
                } else {
                    pauseInner();
                }
                mPrePlayStatus = PlayerConstants.TYPE_PLAYER_PLAYING;
                return;
            }
            mContextMediaPlayer.getMediaPlayer().play();
        }

        @Override
        public void onPlayerPreparing(String url) {
            if (mIPlayerStateCoreListener != null) {
                mIPlayerStateCoreListener.onPlayerPreparing(url);
            }
        }

        @Override
        public void onPlayerPlaying(String url) {
            if (mIPlayerStateCoreListener != null) {
                mIPlayerStateCoreListener.onPlayerPlaying(url);
            }
        }

        @Override
        public void onPlayerPaused(String url) {
            if (mIPlayerStateCoreListener != null) {
                mIPlayerStateCoreListener.onPlayerPaused(url);
            }
        }

        @Override
        public void onProgress(String url, long progress, long total) {
            // 正在起播过程中，不上报进度，避免进度条跳动
//            if (mIsAsyncStartExecuting) {
//                return;
//            }
            if (mIPlayerStateCoreListener != null) {
                mIPlayerStateCoreListener.onProgress(url, progress, total);
            }
        }

        @Override
        public void onPlayerFailed(String url, int what, int extra, String dnsAddress) {
            stopInner();
            if (mIPlayerStateCoreListener != null) {
                mIPlayerStateCoreListener.onPlayerFailed(url, what, extra, dnsAddress);
            }
        }

        @Override
        public void onPlayerEnd(String url) {
            if (mIPlayerStateCoreListener != null) {
                mIPlayerStateCoreListener.onPlayerEnd(url);
            }
        }

        @Override
        public void onSeekStart(String url) {
            if (mIPlayerStateCoreListener != null) {
                mIPlayerStateCoreListener.onSeekStart(url);
            }
        }

        @Override
        public void onSeekComplete(String url) {
            if (mIPlayerStateCoreListener != null) {
                mIPlayerStateCoreListener.onSeekComplete(url);
            }
        }

        @Override
        public void onBufferingStart(String url) {
            if (mIPlayerStateCoreListener != null) {
                mIPlayerStateCoreListener.onBufferingStart(url);
            }
        }

        @Override
        public void onBufferingEnd(String url) {
            if (mIPlayerStateCoreListener != null) {
                mIPlayerStateCoreListener.onBufferingEnd(url);
            }
        }

        @Override
        public void onInteractionFired(String url, int position, int id) {
            if (mIPlayerStateCoreListener != null) {
                mIPlayerStateCoreListener.onInteractionFired(url, position, id);
            }
        }
    };

//    private IPlayerStateVideoListener iPlayerStateVideoListener = new IPlayerStateVideoListener() {
//        @Override
//        public void onPlayerVideoRenderingStart(PlayItem playItem) {
//            if (mIPlayerStateVideoListener != null) {
//                mIPlayerStateVideoListener.onPlayerVideoRenderingStart(playItem);
//            }
//        }
//
//        @Override
//        public void onPlayerVideoSizeChanged(PlayItem playItem, int width, int height) {
//            if (mIPlayerStateVideoListener != null) {
//                mIPlayerStateVideoListener.onPlayerVideoSizeChanged(playItem, width, height);
//            }
//        }
//    };

    private void setAudioFadeEnabledInner(boolean audioFadeEnabled) {
        this.audioFadeEnabled = audioFadeEnabled;
    }

    private void setHttpProxyInner(String httpProxy) {
        this.httpProxy = httpProxy;
    }

    private void setClearDnsCacheInner(boolean clearDnsCache) {
        this.clearDnsCache = clearDnsCache;
    }

    private void setAudioFadeConfigInner(AudioFadeConfig audioFadeConfig) {
        PlayerLogUtil.log(getClass().getSimpleName(), "setAudioFadeConfigInner, audioFadeConfig = " + audioFadeConfig.toString());
        this.audioFadeConfig = audioFadeConfig;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }
}
