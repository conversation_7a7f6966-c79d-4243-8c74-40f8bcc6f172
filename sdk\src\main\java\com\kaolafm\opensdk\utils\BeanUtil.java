package com.kaolafm.opensdk.utils;

import com.kaolafm.base.utils.DateUtil;
import com.kaolafm.opensdk.api.broadcast.BroadcastDetails;
import com.kaolafm.opensdk.api.broadcast.ProgramDetails;
import com.kaolafm.opensdk.api.media.model.AudioDetails;
import com.kaolafm.opensdk.player.logic.model.item.BroadcastPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.RadioPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

/**
 * Bean类转换
 *
 * <AUTHOR>
 * @date 2019-03-20
 */
@Deprecated
public class BeanUtil {

    /**
     * 专辑/PGC单曲转换为播放item
     *
     * @param audioDetails 专辑/PGC单曲
     * @return PlayItem
     */
    public static PlayItem translateToPlayItem(AudioDetails audioDetails) {
        if (audioDetails == null) {
            return null;
        }
        RadioPlayItem playItem = new RadioPlayItem();
        playItem.setAudioId(audioDetails.getAudioId());
        playItem.getInfoData().setTitle(audioDetails.getAudioName());
        String aacPlayUrl = audioDetails.getAacPlayUrl();
        playItem.setDuration(audioDetails.getOriginalDuration());
        playItem.setPlayUrl(aacPlayUrl);

        // 增加购买方式及购买信息
        playItem.setPayMethod(audioDetails.getPayMethod());

        //增加audioDetails中携带的购买类型（buyType）和购买状态信息(buyState)
        playItem.setBuyStatus(audioDetails.getBuyStatus());
        playItem.setBuyType(audioDetails.getBuyType());

        //增加playUrlId供播放时通过id获取播放地址
        playItem.setPlayUrlId(audioDetails.getPlayUrlId());

        playItem.getInfoData().setAudioDes(audioDetails.getAudioDes());
        playItem.getInfoData().setAlbumId(audioDetails.getAlbumId());
        playItem.getInfoData().setAlbumPic(audioDetails.getAlbumPic());
        playItem.getInfoData().setAlbumName(audioDetails.getAlbumName());
        playItem.getInfoData().setAudioPic(audioDetails.getAudioPic());
        playItem.getInfoData().setOrderNum(audioDetails.getOrderNum());
        playItem.getInfoData().setUpdateTime(String.valueOf(audioDetails.getUpdateTime()));
        playItem.getRadioInfoData().setClockId(audioDetails.getClockId());
        playItem.getRadioInfoData().setCategoryId(audioDetails.getCategoryId());
        playItem.getInfoData().setIcon(audioDetails.getIcon());
        playItem.getInfoData().setSourceLogo(audioDetails.getSourceLogo());
        playItem.getInfoData().setSourceName(audioDetails.getSourceName());
        playItem.getRadioInfoData().setMainTitleName(audioDetails.getMainTitleName());
        playItem.getRadioInfoData().setRadioSubTag(audioDetails.getContentTypeName());
        playItem.getRadioInfoData().setRadioSubTagType(audioDetails.getContentType());
        playItem.getRadioInfoData().setSubheadName(audioDetails.getSubheadName());
        playItem.getRadioInfoData().setCallBack(audioDetails.getCallBack());
        playItem.getRadioInfoData().setSource(audioDetails.getSource());

        playItem.setPlayInfoList(audioDetails.getPlayInfoList());
        return playItem;
    }

    public static PlayItem translateToPlayItem(ProgramDetails programDetails, String channel, long listenCount) {
        return translateToPlayItem(programDetails, channel, 0, listenCount);
    }

    /**
     * 广播节目单曲转换成播放item
     *
     * @param programDetails 广播节目单曲
     * @return PlayItem
     */
    public static PlayItem translateToPlayItem(ProgramDetails programDetails, String channel, int classifyId, long listenCount) {
        BroadcastPlayItem playItem = new BroadcastPlayItem();

        if (programDetails.getStatus() == PlayerConstants.BROADCAST_STATUS_LIVING) {
            playItem.setStatus(PlayerConstants.BROADCAST_STATUS_LIVING);
            playItem.setPlayUrl(programDetails.getPlayUrl());
            playItem.getTimeInfoData().setCurSystemTime(DateUtil.getServerTime());
        } else {
            int status = programDetails.getStatus();
            if (status == PlayerConstants.BROADCAST_STATUS_LIVING ||
                    status == PlayerConstants.BROADCAST_STATUS_DEFAULT) {
                playItem.setStatus(PlayerConstants.BROADCAST_STATUS_LIVING);
                playItem.setPlayUrl(programDetails.getPlayUrl());
                playItem.getTimeInfoData().setCurSystemTime(DateUtil.getServerTime());
            } else if (status == PlayerConstants.BROADCAST_STATUS_PLAYBACK) {
                playItem.setStatus(PlayerConstants.BROADCAST_STATUS_PLAYBACK);
                playItem.setPlayUrl(programDetails.getBackLiveUrl());
            } else {
                playItem.setStatus(status);
                playItem.setPlayUrl(null);
            }
        }
        playItem.setClassifyId(classifyId);
        playItem.getInfoData().setDataSrc(PlayerConstants.RESOURCES_TYPE_BROADCAST);
        playItem.getInfoData().setAlbumId(programDetails.getBroadcastId());
        playItem.setAudioId(programDetails.getProgramId());
        playItem.getInfoData().setTitle(programDetails.getTitle());
        playItem.getInfoData().setAlbumPic(programDetails.getBroadcastImg());
        playItem.getInfoData().setAlbumName(programDetails.getBroadcastName());
        long start = programDetails.getStartTime();
        long end = programDetails.getFinishTime();
        long duration = end - start;
        playItem.getTimeInfoData().setStartTime(start);
        playItem.getTimeInfoData().setFinishTime(end);
        playItem.getTimeInfoData().setBeginTime(programDetails.getBeginTime());
        playItem.getTimeInfoData().setEndTime(programDetails.getEndTime());
        playItem.setDuration((int) duration);
        playItem.getInfoData().setIcon(programDetails.getIcon());
        playItem.setFrequencyChannel(channel);
        playItem.setListenCount(listenCount);
        return playItem;
    }

    public static PlayItem translateToPlayItem(BroadcastDetails details) {
        if (details == null) {
            return null;
        }
        BroadcastPlayItem playItem = new BroadcastPlayItem();
        if (details.getStatus() == PlayerConstants.BROADCAST_STATUS_LIVING) {
            playItem.setStatus(PlayerConstants.BROADCAST_STATUS_LIVING);
            playItem.setPlayUrl(details.getPlayUrl());
            playItem.getTimeInfoData().setCurSystemTime(DateUtil.getServerTime());
        } else {
            int status = details.getStatus();
            if (status == PlayerConstants.BROADCAST_STATUS_LIVING ||
                    status == PlayerConstants.BROADCAST_STATUS_DEFAULT) {
                playItem.setStatus(PlayerConstants.BROADCAST_STATUS_LIVING);
                playItem.setPlayUrl(details.getPlayUrl());
                playItem.getTimeInfoData().setCurSystemTime(DateUtil.getServerTime());
            } else if (status == PlayerConstants.BROADCAST_STATUS_PLAYBACK) {
                playItem.setStatus(PlayerConstants.BROADCAST_STATUS_PLAYBACK);
                playItem.setPlayUrl(details.getPlayUrl());
            } else {
                playItem.setStatus(status);
                playItem.setPlayUrl(null);
            }
        }
        playItem.setListenCount(details.getOnLineNum());
        playItem.setClassifyId(details.getClassifyId());
        playItem.getInfoData().setDataSrc(PlayerConstants.RESOURCES_TYPE_BROADCAST);
        playItem.getInfoData().setAlbumId(details.getBroadcastId());
        playItem.setAudioId(details.getBroadcastId());
        playItem.getInfoData().setTitle(details.getName());
        playItem.getInfoData().setAlbumPic(details.getImg());
        playItem.getInfoData().setAlbumName(details.getName());
//        long start = details.getStartTime();
//        long end = details.getFinishTime();
//        long duration = end - start;
//        playItem.getTimeInfoData().setStartTime(start);
//        playItem.getTimeInfoData().setFinishTime(end);
//        playItem.getTimeInfoData().setBeginTime(details.getBeginTime());
//        playItem.getTimeInfoData().setEndTime(details.getEndTime());
//        playItem.setDuration((int) duration);
        playItem.getInfoData().setIcon(details.getIcon());
        return playItem;
    }
}
