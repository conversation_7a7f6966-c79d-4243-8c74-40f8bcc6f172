apply plugin: 'com.android.library'
apply plugin: 'org.greenrobot.greendao' // apply plugin
//apply plugin: "build-jar"

def and = rootProject.ext.android
def dependent = rootProject.ext.dependencies
def config = rootProject.ext.config

def VERSION_CODE = and.versionCode
def VERSION_NAME = and.versionName

android {
    compileSdkVersion and.compileSdkVersion
    buildToolsVersion and.buildToolsVersion
    defaultConfig {
        minSdkVersion and.minSdkVersion
        targetSdkVersion and.targetSdkVersion
        versionCode VERSION_CODE
        versionName VERSION_NAME
        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'
    }
    buildTypes {
        release {
            minifyEnabled false
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-report-rules.pro'
            buildConfigField "String", "API_VERSION", "\""+config["api_version"]+"\""
        }

        debug {
            minifyEnabled false
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-report-rules.pro'
            buildConfigField "String", "API_VERSION", "\""+config["api_version"]+"\""
        }
    }
    lintOptions {
        abortOnError false
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    //配置数据库相关信息
    greendao {
        schemaVersion 1 //数据库版本号
        daoPackage 'com.kaolafm.report.database.greendao'
        targetGenDir 'src/main/java'//设置DaoMaster、DaoSession、Dao目录
    }
}

dependencies {
    compileOnly fileTree(include: ['*.jar'], dir: 'libs')
//    compileOnly project(':utils')
    compileOnly project(':core')
}
//upload {
//    sdkFlavor {
//        proguardConfigFile = ["proguard-report-rules.pro"]
//        includePackage = ["com/kaolafm/report"]
//        versionName = VERSION_NAME
//        outputFileName = "Report"
//        mavenConfig {
//            artifactId     'report'
//            groupId        'com.kaolafm'
//            libType        'jar'
//            libDescription 'SDK Report'
////                repository      readLocalProperties('local.repo.url')
//        }
//    }
//}
