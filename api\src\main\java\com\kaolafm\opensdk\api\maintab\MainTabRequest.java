package com.kaolafm.opensdk.api.maintab;

import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.maintab.model.MainTabBean;
import com.kaolafm.opensdk.http.core.HttpCallback;

import java.util.List;

public class MainTabRequest extends BaseRequest {
    private MainTabService mService;

    public MainTabRequest() {
        mService = obtainRetrofitService(MainTabService.class);
    }


    /**
     * 获取导航条数据
     *
     * @param callback 回调
     */
    public void getMainTab(HttpCallback<List<MainTabBean>> callback) {
        doHttpDeal(mService.getMainTab(), BaseResult::getResult, callback);
    }
}
