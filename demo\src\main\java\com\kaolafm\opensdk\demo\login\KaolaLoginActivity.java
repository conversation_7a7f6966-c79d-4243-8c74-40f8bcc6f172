package com.kaolafm.opensdk.demo.login;

import android.os.Bundle;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.appcompat.app.AlertDialog;
import android.text.TextUtils;
import android.text.method.ScrollingMovementMethod;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.kaolafm.opensdk.account.token.AccessTokenManager;
import com.kaolafm.opensdk.account.token.KaolaAccessToken;
import com.kaolafm.opensdk.account.token.TingbanTokenObserver;
import com.kaolafm.opensdk.api.login.LoginRequest;
import com.kaolafm.opensdk.api.login.model.QRCodeInfo;
import com.kaolafm.opensdk.api.login.model.UserInfo;
import com.kaolafm.opensdk.demo.BaseActivity;
import com.kaolafm.opensdk.demo.R;
import com.kaolafm.opensdk.demo.live.ui.UserInfoManager;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

import java.util.concurrent.TimeUnit;

import butterknife.BindView;
import butterknife.OnClick;
import io.reactivex.Observable;
import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;

/**
 * <AUTHOR> Yan
 * @date 2018/9/13
 */

public class KaolaLoginActivity extends BaseActivity {

    @BindView(R.id.iv_kaola_avatar)
    public ImageView mIvKaolaAvatar;

    @BindView(R.id.tv_kaola_nick_name)
    TextView mTvKaolaNickName;

    @BindView(R.id.tv_kaola_gender)
    TextView mTvKaolaGender;

    @BindView(R.id.tv_kaola_region)
    TextView mTvKaolaRegion;

    @BindView(R.id.iv_kaola_qr_code)
    ImageView mIvKaolaQrCode;

    @BindView(R.id.tv_qr_status_history)
    TextView mTvQrStatusHistory;

    @BindView(R.id.cl_kaola_root)
    public ConstraintLayout clKaolaRoot;

    private String mCode;

    private Disposable mDisposable;

    private LoginRequest mLoginRequest;

    private String mUuid;

    @Override
    public int getLayoutId() {
        return R.layout.activity_kaola_login;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        mTvQrStatusHistory.setMovementMethod(ScrollingMovementMethod.getInstance());
        setTitle("云听登录");
    }

    @Override
    public void initData() {
        mLoginRequest = new LoginRequest().setTag(this.toString());
        AccessTokenManager.getInstance().registerObserver(new TingbanTokenObserver() {
            @Override
            public void onChange(KaolaAccessToken token) {

            }
        });
        if (AccessTokenManager.getInstance().getKaolaAccessToken().isLogin()) {
            UserInfo userInfo = new UserInfo();
            userInfo.setAvatar(UserInfoManager.getInstance().getAvatar());
            userInfo.setNickName(UserInfoManager.getInstance().getNickName());
            userInfo.setGender(UserInfoManager.getInstance().getGender());
            userInfo.setUserArea(UserInfoManager.getInstance().getUserArea());
            showInfo(userInfo);
        }
        mLoginRequest.getQRCode(new HttpCallback<QRCodeInfo>() {
            @Override
            public void onSuccess(QRCodeInfo qrCodeInfo) {
                mUuid = qrCodeInfo.getUuid();
                Glide.with(KaolaLoginActivity.this)
                        .load(qrCodeInfo.getQRCodePath())
                        .into(mIvKaolaQrCode);
//                checkStatusInterval();
            }

            @Override
            public void onError(ApiException exception) {
                showStatus("获取二维码错误，错误码：" + exception.getCode() + ", 错误信息：" + exception.getMessage());
            }
        });
    }

    @OnClick({R.id.btn_kaola_start_check, R.id.btn_kaola_stop_check, R.id.btn_kaola_fetch_code,
            R.id.btn_kaola_bind_kradio, R.id.btn_kaola_unbind_kradio, R.id.btn_kaola_bind_when_auth,
            R.id.btn_kaola_bind_kradio_uuid, R.id.btn_kaola_login_id, R.id.btn_kaola_login_refresh,
            R.id.btn_kaola_login_check})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.btn_kaola_start_check:
                checkStatusInterval();
                break;
            case R.id.btn_kaola_stop_check:
                showStatus("停止查询状态");
                dispose();
                break;
            case R.id.btn_kaola_fetch_code:
                fetchCode();
                break;
            case R.id.btn_kaola_bind_kradio:
                bind();
                break;
            case R.id.btn_kaola_unbind_kradio:
                unbind();
                break;
            case R.id.btn_kaola_bind_kradio_uuid:
                bindByUuid();
                break;
            case R.id.btn_kaola_bind_when_auth:
                bindWhenAuth();
                break;
            case R.id.btn_kaola_login_id:
                loginById();
                break;
            case R.id.btn_kaola_login_refresh:
                refreshToken();
                break;
            case R.id.btn_kaola_login_check:
                showToast("是否登录:" + AccessTokenManager.getInstance().getKaolaAccessToken().isLogin());
                break;
            default:
                break;
        }
    }

    private void refreshToken() {
        mLoginRequest.refreshToken(new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean aBoolean) {
                showToast(aBoolean ? "token刷新成功" : "token刷新失败");
            }

            @Override
            public void onError(ApiException exception) {
                showError("刷新token错误", exception);
            }
        });
    }

    private void loginById() {
        EditText editText = new EditText(this);
        editText.setLayoutParams(new LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT));
        new AlertDialog.Builder(this)
                .setView(editText)
                .setPositiveButton("确定", (dialog, which) -> {
                    String userId = editText.getText().toString().trim();
                    if (!TextUtils.isEmpty(userId)) {
                        KaolaAccessToken kaolaAccessToken = AccessTokenManager.getInstance()
                                .getKaolaAccessToken();
                        kaolaAccessToken.setUserId(userId);
                        AccessTokenManager.getInstance().setCurrentAccessToken(kaolaAccessToken);
                    }
                    dialog.dismiss();
                })
                .setNegativeButton("取消", (dialog, which) -> dialog.dismiss())
                .create().show();

    }

    private void bindWhenAuth() {
        if (!TextUtils.isEmpty(mUuid)) {
            mLoginRequest.loginWhenAuthorized(mUuid, new HttpCallback<UserInfo>() {
                @Override
                public void onSuccess(UserInfo userInfo) {
                    showInfo(userInfo);
                }

                @Override
                public void onError(ApiException exception) {
                    showError("一键绑定错误", exception);
                }
            });
        } else {
            showToast("绑定设备的uuid为空");
        }
    }

    private void bindByUuid() {
        if (!TextUtils.isEmpty(mUuid)) {
            mLoginRequest.authorizedByUuid(mUuid, new HttpCallback<UserInfo>() {
                @Override
                public void onSuccess(UserInfo userInfo) {
                    showInfo(userInfo);
                }

                @Override
                public void onError(ApiException exception) {
                    showError("根据uuid绑定设备错误", exception);
                }
            });
        } else {
            showToast("绑定设备的uuid为空");
        }
    }

    private void showInfo(UserInfo userInfo) {
        mTvKaolaNickName.setText("昵称：" + userInfo.getNickName());
        mTvKaolaGender.setText("性别：" + userInfo.getGender());
        mTvKaolaRegion.setText("地区：" + userInfo.getUserArea());
        Glide.with(this).load(userInfo.getAvatar()).into(mIvKaolaAvatar);
        //本地保存用户信息
        UserInfoManager.getInstance().setAvatar(userInfo.getAvatar());
        UserInfoManager.getInstance().setNickName(userInfo.getNickName());
        UserInfoManager.getInstance().setGender(userInfo.getGender());
        UserInfoManager.getInstance().setUserArea(userInfo.getUserArea());
    }

    /**
     * 解绑设备，会退出听伴的登录及听伴的授权
     */
    private void unbind() {
        mLoginRequest.logoutYunting(new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean aBoolean) {
                showToast(aBoolean ? "解绑成功" : "解绑失败");
            }

            @Override
            public void onError(ApiException exception) {
                showError("解绑发生错误", exception);
            }
        });
    }

    /**
     * 听伴登录并绑定设备，
     */
    private void bind() {
        if (!TextUtils.isEmpty(mCode)) {
            mLoginRequest.authorizedByCode(mCode, new HttpCallback<UserInfo>() {
                @Override
                public void onSuccess(UserInfo userInfo) {
                    showStatus("KaolaLoginActivity,UserInfo: " + userInfo.toString());
                    Log.e("KaolaLoginActivity", "onSuccess: " + userInfo);
                }

                @Override
                public void onError(ApiException exception) {
                    showToast(exception.getMessage() + "");
                }
            });
        } else {
            showToast("绑定K-radio的code为空");
        }
    }

    /**
     * 获取绑定的code
     */
    private void fetchCode() {
        if (!TextUtils.isEmpty(mUuid)) {
            mLoginRequest.fetchCode(mUuid, new HttpCallback<String>() {
                @Override
                public void onSuccess(String s) {
                    mCode = s;
                    showStatus("获取code：" + s);
                }

                @Override
                public void onError(ApiException exception) {
                    showStatus("获取二维码错误，错误码：" + exception.getCode() + ", 错误信息：" + exception.getMessage());
                }
            });
        } else {
            showToast("uuid为空");
        }
    }

    /**
     * 循环检查二维码状态
     */
    private void checkStatusInterval() {
        if (mDisposable == null || mDisposable.isDisposed()) {
            Observable.interval(1, TimeUnit.SECONDS)
                    .subscribe(new Observer<Long>() {
                        @Override
                        public void onSubscribe(Disposable d) {
                            mDisposable = d;
                        }

                        @Override
                        public void onNext(Long aLong) {
                            checkStatus();
                        }

                        @Override
                        public void onError(Throwable e) {
                            dispose();
                        }

                        @Override
                        public void onComplete() {
                            dispose();
                        }
                    });
        }
    }

    private void checkStatus() {
        if (!TextUtils.isEmpty(mUuid)) {
            mLoginRequest.checkQRCodeStatus(mUuid, new HttpCallback<Integer>() {
                @Override
                public void onSuccess(Integer integer) {
                    switch (integer) {
                        case QRCodeInfo.STATUS_NORMAL:
                            showStatus("等待扫码");
                            break;
                        case QRCodeInfo.STATUS_LOSE_EFFICACY:
                            showStatus("二维码过期. 需要重新请求二维码");
                            dispose();
                            break;
                        case QRCodeInfo.STATUS_AUTHORIZATION:
                            showStatus("已授权");
                            dispose();
                            break;
                        case QRCodeInfo.STATUS_SCANED:
                            showStatus("已被扫描，还未登录");
                            break;
                        default:
                    }
                }

                @Override
                public void onError(ApiException exception) {

                }
            });
        }
    }

    private void showStatus(String msg) {
        String historyText = mTvQrStatusHistory.getText().toString();
        mTvQrStatusHistory.setText(msg + "\n" + historyText);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mLoginRequest != null) {
            mLoginRequest.cancel(this.toString());
        }
        dispose();
    }

    private void dispose() {
        if (mDisposable != null && !mDisposable.isDisposed()) {
            mDisposable.dispose();
            mDisposable = null;
        }
    }
}
