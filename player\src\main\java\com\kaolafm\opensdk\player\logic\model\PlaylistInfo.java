package com.kaolafm.opensdk.player.logic.model;

import com.kaolafm.opensdk.api.broadcast.BroadcastAreaInfo;

import java.util.List;

/**
 * <AUTHOR> on 2019/3/18.
 */

public class PlaylistInfo extends CustomPlayerBuilder {

    /**
     * 专辑名称
     */
    private String mAlbumName;

    private String mAlbumPic;

    private String mBreakPointContinue;

    private String mPageIndex = "1";

    private int mAllSize = 0;

    private boolean hasNextPage = false;

    private boolean hasPrePage = false;
    /**
     * 下一页
     */
    private int mNextPage;

    /**
     * 上一页
     */
    private int mPrePage;

    /**
     * 专辑订阅数
     */
    private long followedNum;
    /**
     * 总期数
     */
    private long countNum;

    /**
     * 收听数
     */
    private long listenNum;
    /**
     * 广播频段
     */
    private String broadcastChannel;
    /**
     * 广播标识：
     * 1、国家台；2、地方台
     */
    private int broadcastClassifyId;

    private int broadcastSort;

    /**
     * 广播回放的状态
     * 0-节目单隐藏
     * 1-节目单显示，回放能播
     * 2-节目单显示，回放不能播
     * 如果为1，节目开播之后没有回放地址，为转码中状态
     */
    private int programEnable;

    private String sourceLogo;

    private String sourceName;

    private String mTempId;

    private String mTempChildId;

    private int radioType = -1;

    private int adZoneChooseType = 0;

    private int adZoneId = -1;
    /**
     * 是否可以订阅 只有为1的时候不能订阅
     */
    private int noSubscribe;

    /**
     * 是否支持倒序
     */
    private boolean enableReverse = false;

    /**
     * 地区编码
     */
    private String areaCode;

    /**
     * 地区名
     */
    private String areaName;


    /**
     * 当前广播包含的所有地区频道
     * 某些广播会覆盖多个省份，在不同的省份会有不同的频率和内容，该字段为各个省份的频率和播放地址列表
     */
    private List<BroadcastAreaInfo> broadcastMultiAreaList;

    public int getNoSubscribe() {
        return noSubscribe;
    }

    public void setNoSubscribe(int noSubscribe) {
        this.noSubscribe = noSubscribe;
    }

    public String getPageIndex() {
        return mPageIndex;
    }

    public void setPageIndex(String pageIndex) {
        this.mPageIndex = pageIndex;
    }

    public boolean isHasNextPage() {
        return hasNextPage;
    }

    public void setHasNextPage(boolean hasNextPage) {
        this.hasNextPage = hasNextPage;
    }

    public boolean isHasPrePage() {
        return hasPrePage;
    }

    public void setHasPrePage(boolean hasPrePage) {
        this.hasPrePage = hasPrePage;
    }

    public int getAllSize() {
        return mAllSize;
    }

    public void setAllSize(int allSize) {
        this.mAllSize = allSize;
    }

    public String getAlbumName() {
        return mAlbumName;
    }

    public void setAlbumName(String albumName) {
        this.mAlbumName = albumName;
    }

    public String getAlbumPic() {
        return mAlbumPic;
    }

    public void setAlbumPic(String albumPic) {
        this.mAlbumPic = albumPic;
    }

    public long getFollowedNum() {
        return followedNum;
    }

    public void setFollowedNum(long followedNum) {
        this.followedNum = followedNum;
    }

    public long getCountNum() {
        return countNum;
    }

    public void setCountNum(long countNum) {
        this.countNum = countNum;
    }

    public long getListenNum() {
        return listenNum;
    }

    public void setListenNum(long listenNum) {
        this.listenNum = listenNum;
    }

    public String getBroadcastChannel() {
        return broadcastChannel;
    }

    public void setBroadcastChannel(String broadcastChannel) {
        this.broadcastChannel = broadcastChannel;
    }

    public int getBroadcastSort() {
        return broadcastSort;
    }

    public void setBroadcastSort(int broadcastSort) {
        this.broadcastSort = broadcastSort;
    }

    public int getProgramEnable() {
        return programEnable;
    }

    public void setProgramEnable(int programEnable) {
        this.programEnable = programEnable;
    }

    public String getSourceLogo() {
        return sourceLogo;
    }

    public void setSourceLogo(String sourceLogo) {
        this.sourceLogo = sourceLogo;
    }

    public String getSourceName() {
        return sourceName;
    }

    public void setSourceName(String sourceName) {
        this.sourceName = sourceName;
    }

    public int getNextPage() {
        return mNextPage;
    }

    public void setNextPage(int nextPage) {
        if (nextPage > mNextPage) {
            this.mNextPage = nextPage;
        }
    }

    public void resetNextPage(int nextPage) {
        this.mNextPage = nextPage;
    }

    public int getPrePage() {
        return mPrePage;
    }

    public void setPrePage(int prePage) {
        if (mPrePage == 0 || prePage < mPrePage) {
            this.mPrePage = prePage;
        }
    }

    public void resetPrePage(int prePage) {
        this.mPrePage = prePage;
    }

    public String getTempId() {
        return mTempId;
    }

    public void setTempId(String tempId) {
        this.mTempId = tempId;
    }

    public String getTempChildId() {
        return mTempChildId;
    }

    public void setTempChildId(String tempChildId) {
        this.mTempChildId = tempChildId;
    }

    public int getRadioType() {
        return radioType;
    }

    public void setRadioType(int radioType) {
        this.radioType = radioType;
    }

    public int getAdZoneChooseType() {
        return adZoneChooseType;
    }

    public void setAdZoneChooseType(int adZoneChooseType) {
        this.adZoneChooseType = adZoneChooseType;
    }

    public int getAdZoneId() {
        return adZoneId;
    }

    public void setAdZoneId(int adZoneId) {
        this.adZoneId = adZoneId;
    }

    public String getBreakPointContinue() {
        return mBreakPointContinue;
    }

    public void setBreakPointContinue(String mBreakPointContinue) {
        this.mBreakPointContinue = mBreakPointContinue;
    }


    public int getBroadcastClassifyId() {
        return broadcastClassifyId;
    }

    public void setBroadcastClassifyId(int broadcastClassifyId) {
        this.broadcastClassifyId = broadcastClassifyId;
    }

    public boolean isEnableReverse() {
        return enableReverse;
    }

    public void setEnableReverse(boolean enableReverse) {
        this.enableReverse = enableReverse;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public List<BroadcastAreaInfo> getBroadcastMultiAreaList() {
        return broadcastMultiAreaList;
    }

    public void setBroadcastMultiAreaList(List<BroadcastAreaInfo> broadcastMultiAreaList) {
        this.broadcastMultiAreaList = broadcastMultiAreaList;
    }
}