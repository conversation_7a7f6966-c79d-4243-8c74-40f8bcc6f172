package com.kaolafm.opensdk.api;

import com.kaolafm.opensdk.HostConstants;
import com.kaolafm.opensdk.http.urlmanager.UrlManager;

/**
 * host地址及对应的domain name和headers(用于区分域名)
 *
 * <AUTHOR> on 2018/5/21.
 */

public final class ApiHostConstants {

    /**
     * 添加到头部信息，表示使用https
     */
    public static final String HTTPS_PROTOCOL_DOMAIN_HEADER = UrlManager.HTTPS_PROTOCOL + ":"+ "https";


    /**
     * open.kaolafm
     */
    public static final String OPEN_KAOLA_HOST = "https://" + HostConstants.OPEN_KAOLA_HOST;
    public static final String OPEN_KAOLA_DOMAIN_NAME = "open_kaola";
    public static final String OPEN_KAOLA_DOMAIN_HEADER = UrlManager.DOMAIN_NAME_HEADER + OPEN_KAOLA_DOMAIN_NAME;

    /**
     * 搜索HTTPS
     */
    public static final String SEARCH_HTTPS_HOST = "https://" + HostConstants.SEARCH_HTTPS_HOST;
    /**
     * 搜索http
     */
    public static final String SEARCH_HTTP_HOST = "http://" + HostConstants.SEARCH_HTTP_HOST;
    public static final String SEARCH_DOMAIN_NAME = "voice_search";
    public static final String SEARCH_DOMAIN_HEADER = UrlManager.DOMAIN_NAME_HEADER
            + SEARCH_DOMAIN_NAME;

    /**
     * QQ音乐的host地址
     */
    public static final String QQMUSIC_HOST = "http://open.music.qq.com";
    public static final String QQMUSIC_DOMAIN_NAME = "QQMusic";
    public static final String QQMUSIC_DOMAIN_HEADER = UrlManager.DOMAIN_NAME_HEADER + QQMUSIC_DOMAIN_NAME;

    /**
     * 品牌信息的Host地址
     */
    public static final String BRAND_HOST = "https://" + HostConstants.OPEN_KAOLA_HOST;

    public static final String BRAND_DOMAIN_NAME = "BRAND";

    public static final String BRAND_DOMAIN_HEADER = UrlManager.DOMAIN_NAME_HEADER + BRAND_DOMAIN_NAME;

    /**
     * 商城信息的Host地址
     */
    public static final String MALL_HOST = "https://" + HostConstants.MALL_HOST;

    public static final String MALL_DOMAIN_NAME = "Mall";

    public static final String MALL_DOMAIN_HEADER = UrlManager.DOMAIN_NAME_HEADER + MALL_DOMAIN_NAME;

    /**
     * 推荐的HOST地址
     */
    public static final String RECOMMEND_DOMAIN_NAME = "MINUS_FEED_BACK";
    public static final String RECOMMEND_DOMAIN_HEADER = UrlManager.DOMAIN_NAME_HEADER + RECOMMEND_DOMAIN_NAME;
    public static final String RECOMMEND_HOST = "https://" + HostConstants.EMERGENCY_HTTPS_HOST;

}
