package com.kaolafm.opensdk.api.brandpage.model;

import com.kaolafm.opensdk.api.operation.model.ImageFile;

import java.util.List;
import java.util.Map;

/**
 * 品牌主页板块列表
 */
public class BrandPageListBean {
    private String brandName;//品牌名称
    /**
     * 图片信息集合
     */
    private Map<String, ImageFile> imageFiles;

    private List<BrandSectionsBean> brandSections;//板块内容

    public Map<String, ImageFile> getImageFiles() {
        return imageFiles;
    }

    public void setImageFiles(Map<String, ImageFile> imageFiles) {
        this.imageFiles = imageFiles;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public List<BrandSectionsBean> getBrandSections() {
        return brandSections;
    }

    public void setBrandSections(List<BrandSectionsBean> brandSections) {
        this.brandSections = brandSections;
    }
}
