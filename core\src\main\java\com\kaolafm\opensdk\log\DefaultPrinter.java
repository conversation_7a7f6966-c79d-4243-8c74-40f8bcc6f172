package com.kaolafm.opensdk.log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.util.Log;

import com.kaolafm.base.utils.StringUtil;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.StringReader;
import java.io.StringWriter;
import java.util.logging.Logger;

import javax.xml.transform.OutputKeys;
import javax.xml.transform.Source;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.stream.StreamResult;
import javax.xml.transform.stream.StreamSource;

/**
 * <AUTHOR>
 * @date 2018/12/21
 */

public class DefaultPrinter implements Printer {


    /**
     * Android's max limit for a log entry is ~4076 bytes,
     * so 4000 bytes is used as chunk size since default charset
     * is UTF-8
     */
    private static final int CHUNK_SIZE = 4000;

    /**
     * The minimum stack trace index, starts at this class after two native calls.
     */
    private static final int MIN_STACK_OFFSET = 5;

    /**
     * Drawing toolbox
     */
    private static final char TOP_LEFT_CORNER = '┌';

    private static final char BOTTOM_LEFT_CORNER = '└';

    private static final char MIDDLE_CORNER = '├';

    private static final char HORIZONTAL_LINE = '│';

    private static final String DOUBLE_DIVIDER = "────────────────────────────────────────────────────────";

    private static final String SINGLE_DIVIDER = "┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄";

    private static final String TOP_BORDER = TOP_LEFT_CORNER + DOUBLE_DIVIDER + DOUBLE_DIVIDER;

    private static final String BOTTOM_BORDER = BOTTOM_LEFT_CORNER + DOUBLE_DIVIDER + DOUBLE_DIVIDER;

    private static final String MIDDLE_BORDER = MIDDLE_CORNER + SINGLE_DIVIDER + SINGLE_DIVIDER;

    private static final int JSON_INDENT = 2;

    private final int methodCount;

    private final int methodOffset;

    private final boolean showThreadInfo;

    @Nullable
    private String tag;

    public DefaultPrinter() {
        methodCount = 5;
        methodOffset = 1;
        showThreadInfo = true;
        tag = "K-radio";
    }

    public DefaultPrinter(String tag) {
        this.tag = tag;
        methodCount = 0;
        methodOffset = 1;
        showThreadInfo = false;
    }

    public DefaultPrinter(@Nullable String tag, int methodCount, int methodOffset, boolean showThreadInfo) {
        this.tag = tag;
        this.methodCount = methodCount;
        this.methodOffset = methodOffset;
        this.showThreadInfo = showThreadInfo;
    }

    @Override
    public Printer tag(@Nullable String tag) {
        this.tag = tag;
        return this;
    }

    @Override
    public void d(@NonNull String message, @Nullable Object... args) {
        log(LogLevel.DEBUG, tag, formatMessage(message, args));
    }

    @Override
    public void d(@Nullable Object object) {
        log(LogLevel.DEBUG, tag, StringUtil.toString(object));
    }

    @Override
    public void e(@NonNull String message, @Nullable Object... args) {
        log(LogLevel.ERROR, tag, formatMessage(message, args));
    }

    @Override
    public void e(@Nullable Throwable throwable, @NonNull String message, @Nullable Object... args) {
        log(LogLevel.ERROR, throwable, message, args);
    }

    @Override
    public void w(@NonNull String message, @Nullable Object... args) {
        log(LogLevel.WARN, null, message, args);
    }

    @Override
    public void i(@NonNull String message, @Nullable Object... args) {
        log(LogLevel.INFO, null, message, args);
    }

    @Override
    public void v(@NonNull String message, @Nullable Object... args) {
        log(LogLevel.VERBOSE, null, message, args);
    }

    @Override
    public void wtf(@NonNull String message, @Nullable Object... args) {
        log(LogLevel.ASSERT, null, message, args);
    }

    @Override
    public void json(@Nullable String json) {
        if (TextUtils.isEmpty(json)) {
            d("Empty/Null json content");
            return;
        }
        try {
            json = json.trim();
            if (json.startsWith("{")) {
                JSONObject jsonObject = new JSONObject(json);
                String message = jsonObject.toString(JSON_INDENT);
                d(message);
                return;
            }
            if (json.startsWith("[")) {
                JSONArray jsonArray = new JSONArray(json);
                String message = jsonArray.toString(JSON_INDENT);
                d(message);
                return;
            }
            e("Invalid Json");
        } catch (JSONException e) {
            e("Invalid Json");
        }
    }

    @Override
    public void xml(@Nullable String xml) {
        if (TextUtils.isEmpty(xml)) {
            d("Empty/Null xml content");
            return;
        }
        try {
            Source xmlInput = new StreamSource(new StringReader(xml));
            StreamResult xmlOutput = new StreamResult(new StringWriter());
            Transformer transformer = TransformerFactory.newInstance().newTransformer();
            transformer.setOutputProperty(OutputKeys.INDENT, "yes");
            transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "2");
            transformer.transform(xmlInput, xmlOutput);
            d(xmlOutput.getWriter().toString().replaceFirst(">", ">\n"));
        } catch (TransformerException e) {
            e("Invalid xml");
        }
    }

    @Override
    public synchronized void println(int logLevel, @Nullable String tag, @Nullable String message, @Nullable Throwable throwable) {
        if (throwable != null) {
            String stackMsg = Log.getStackTraceString(throwable);
            if (message != null) {
                message += " : " + stackMsg;
            } else {
                message = stackMsg;
            }
        }
        log(logLevel, tag, message);
    }

    private String formatMessage(String message, @Nullable Object... args) {
        return args == null || args.length == 0 ? message : String.format(message, args);
    }

    private synchronized void log(int logLevel, @Nullable Throwable throwable, String message,
            @Nullable Object... args) {
        message = formatMessage(message, args);
        println(logLevel, tag, message, throwable);
    }

    private void log(int logLevel, @Nullable String tag, String message) {
        if (TextUtils.isEmpty(message)) {
            return;
        }

        logTopBorder(logLevel, tag);
        if (methodCount > 0) {
            logHeaderContent(logLevel, tag, methodCount);
        }

        //get bytes of message with system's default charset (which is UTF-8 for Android)
        byte[] bytes = message.getBytes();
        int length = bytes.length;
        if (length <= CHUNK_SIZE) {
            if (methodCount > 0) {
                logDivider(logLevel, tag);
            }
            logContent(logLevel, tag, message);
            logBottomBorder(logLevel, tag);
            return;
        }
        if (methodCount > 0) {
            logDivider(logLevel, tag);
        }
        for (int i = 0; i < length; i += CHUNK_SIZE) {
            int count = Math.min(length - i, CHUNK_SIZE);
            //create a new String with system's default charset (which is UTF-8 for Android)
            logContent(logLevel, tag, new String(bytes, i, count));
        }
        logBottomBorder(logLevel, tag);
    }

    private void logTopBorder(int logLevel, @Nullable String tag) {
        logChunk(logLevel, tag, TOP_BORDER);
    }

    @SuppressWarnings("StringBufferReplaceableByString")
    private void logHeaderContent(int logLevel, @Nullable String tag, int methodCount) {
        StackTraceElement[] trace = Thread.currentThread().getStackTrace();
        if (showThreadInfo) {
            logChunk(logLevel, tag, HORIZONTAL_LINE + " Thread: " + Thread.currentThread().getName());
            logDivider(logLevel, tag);
        }
        String level = "";

        int stackOffset = getStackOffset(trace) + methodOffset;

        //corresponding method count with the current stack may exceeds the stack trace. Trims the count
        if (methodCount + stackOffset > trace.length) {
            methodCount = trace.length - stackOffset - 1;
        }

        for (int i = methodCount; i > 0; i--) {
            int stackIndex = i + stackOffset;
            if (stackIndex >= trace.length) {
                continue;
            }
            StringBuilder builder = new StringBuilder();
            builder.append(HORIZONTAL_LINE)
                    .append(' ')
                    .append(level)
                    .append(getSimpleClassName(trace[stackIndex].getClassName()))
                    .append(".")
                    .append(trace[stackIndex].getMethodName())
                    .append(" ")
                    .append(" (")
                    .append(trace[stackIndex].getFileName())
                    .append(":")
                    .append(trace[stackIndex].getLineNumber())
                    .append(")");
            level += "   ";
            logChunk(logLevel, tag, builder.toString());
        }
    }

    private void logBottomBorder(int logLevel, @Nullable String tag) {
        logChunk(logLevel, tag, BOTTOM_BORDER);
    }

    private void logDivider(int logLevel, @Nullable String tag) {
        logChunk(logLevel, tag, MIDDLE_BORDER);
    }

    private void logContent(int logLevel, @Nullable String tag, @NonNull String chunk) {
        if (!TextUtils.isEmpty(chunk)) {
            String[] lines = chunk.split(System.getProperty("line.separator"));
            for (String line : lines) {
                logChunk(logLevel, tag, HORIZONTAL_LINE + " " + line);
            }
        }

    }

    private void logChunk(int logLevel, @Nullable String tag, @NonNull String chunk) {
        if (!TextUtils.isEmpty(chunk)) {
            Log.println(logLevel, tag, chunk);
        }
    }

    private String getSimpleClassName(@NonNull String name) {
        if (!TextUtils.isEmpty(name)) {
            int lastIndex = name.lastIndexOf(".");
            return name.substring(lastIndex + 1);
        }
        return "";
    }

    /**
     * Determines the starting index of the stack trace, after method calls made by this class.
     *
     * @param trace the stack trace
     * @return the stack offset
     */
    private int getStackOffset(@NonNull StackTraceElement[] trace) {
        for (int i = MIN_STACK_OFFSET; i < trace.length; i++) {
            StackTraceElement e = trace[i];
            String name = e.getClassName();
            if (!name.equals(DefaultPrinter.class.getName()) && !name.equals(Logger.class.getName())) {
                return --i;
            }
        }
        return -1;
    }

    @Nullable
    private String formatTag(@Nullable String tag) {
        if (!TextUtils.isEmpty(tag) && !TextUtils.equals(this.tag, tag)) {
            return this.tag + "-" + tag;
        }
        return this.tag;
    }

}
