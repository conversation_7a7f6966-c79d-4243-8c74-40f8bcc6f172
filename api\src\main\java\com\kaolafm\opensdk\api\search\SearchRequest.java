package com.kaolafm.opensdk.api.search;

import android.net.Uri;

import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.search.internal.Word;
import com.kaolafm.opensdk.api.search.model.SearchProgramBean;
import com.kaolafm.opensdk.api.search.model.VoiceSearchResult;
import com.kaolafm.opensdk.http.core.HttpCallback;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 搜索接口
 *
 * <AUTHOR>
 * @date 2019/4/25
 */
public class SearchRequest extends BaseRequest {

    private final SearchService mSearchService;

    public SearchRequest() {
        mSearchService = obtainRetrofitService(SearchService.class);
    }

    /**
     * 语义搜索，包括了所有参数
     *
     * @param voiceSource 必填 语音来源 公司标识,考拉:kaola;同行者:txzing;思必驰:si<PERSON><PERSON>;问问:wenwen;蓦然:moran;科大 讯飞:kedaxunfei;
     * @param qualityType 可填 音频质量要求,0:低;1:高;
     * @param origJson    可填 语音商原始json串
     * @param field       必填 场景类别 1：音乐，2：综合; 6: 在线广播
     * @param tag         可填 参数可信标识 0：不可信，1：可信；为1时，表示场景分类和其他字段等信息可信度高。
     * @param artist      可填 艺术家
     * @param audioName   可填 音频名称
     * @param albumName   可填 专辑名称
     * @param category    可填 分类
     * @param keyword     必填 关键词 多个关键词以英文逗号“,”分隔
     * @param text        必填 用户声控的原始串
     */
    public void searchBySemantics(
            String voiceSource,
            int qualityType,
            String origJson,
            int field,
            int tag,
            String artist,
            String audioName,
            String albumName,
            String category,
            String keyword,
            String text,
            String freq,
            String area,
            HttpCallback<VoiceSearchResult> callback) {
        HashMap<String, Object> params = new HashMap<>();
        putNotNullParams(params, "voicesource", voiceSource);
        putNotNullParams(params, "qualitytype", qualityType);
        putNotNullParams(params, "orig_json", origJson);
        Word word = new Word();
        word.setField(field);
        word.setTag(tag);
        word.setArtist(artist);
        word.setAudioName(audioName);
        word.setAlbumName(albumName);
        word.setCategory(category);
        word.setKeyword(keyword);
        word.setText(text);
        word.setFreq(freq);
        word.setArea(area);
        String wordStr = Uri.encode(mGsonLazy.get().toJson(word));
        putNotNullParams(params, "word", wordStr);
        doHttpDeal(mSearchService.searchBySemantics(params), callback);
    }

    /**
     * 语义搜索，包括了所有参数
     *
     * @param voiceSource 必填 语音来源 公司标识,考拉:kaola;同行者:txzing;思必驰:sibichi;问问:wenwen;蓦然:moran;科大 讯飞:kedaxunfei;
     * @param qualityType 可填 音频质量要求,0:低;1:高;
     * @param origJson    可填 语音商原始json串
     * @param field       必填 场景类别 1：音乐，2：综合; 6: 在线广播
     * @param tag         可填 参数可信标识 0：不可信，1：可信；为1时，表示场景分类和其他字段等信息可信度高。
     * @param artist      可填 艺术家
     * @param audioName   可填 音频名称
     * @param albumName   可填 专辑名称
     * @param category    可填 分类
     * @param keyword     必填 关键词 多个关键词以英文逗号“,”分隔
     * @param text        必填 用户声控的原始串
     * @param language    可填 语言 暂不支持
     * @param freq        可填 电台频率 暂不支持
     * @param area        可填 搜索text中的地点 暂不支持
     */
    public void searchBySemantics(
            String voiceSource,
            int qualityType,
            String origJson,
            int field,
            int tag,
            String artist,
            String audioName,
            String albumName,
            String category,
            String keyword,
            String text,
            String language,
            String freq,
            String area,
            HttpCallback<VoiceSearchResult> callback) {
        searchBySemantics(voiceSource, qualityType, origJson, field, tag, artist, audioName, albumName, category,
                keyword, text, freq, area, callback);
    }

    private void putNotNullParams(Map<String, Object> params, String key, Object value) {
        if (value == null) {
            value = "";
        }
        params.put(key, value);
    }

    /**
     * 根据关键词搜索所有资源
     *
     * @param keyword  必填 关键词
     * @param callback 选填 回调
     */
    public void searchAll(String keyword, HttpCallback<List<SearchProgramBean>> callback) {
        doHttpDeal(mSearchService.searchAll(keyword), BaseResult::getResult, callback);
    }

    /**
     * 根据关键词分页搜索指定类型资源
     *
     * @param keyword  必填 关键词
     * @param resType  必填 资源类型,支持专辑{@link ResType#TYPE_ALBUM}、单曲{@link ResType#TYPE_AUDIO}、广播{@link
     *                 ResType#TYPE_BROADCAST}、PGC{@link ResType#TYPE_RADIO}
     * @param pageNum  选填 页码，默认1
     * @param pageSize 选填 每页个数，默认10
     * @param callback 选填 回调 返回带有分页信息的搜索结果列表
     */
    public void searchByType(String keyword, int resType, int pageNum, int pageSize,
                             HttpCallback<BasePageResult<List<SearchProgramBean>>> callback) {
        doHttpDeal(mSearchService.searchByType(keyword, getType(resType), pageNum, pageSize), BaseResult::getResult,
                callback);
    }

    /**
     * 根据给定的词获取联想词
     *
     * @param word     必填 关键词
     * @param callback 选填 回调，返回联想词的列表
     */
    public void getSuggestedWords(String word, HttpCallback<List<String>> callback) {
        doHttpDeal(mSearchService.getSuggestedWords(word), BaseResult::getResult, callback);
    }
    public void getSuggestedWords(String word,String contentType,HttpCallback<List<String>> callback) {
        doHttpDeal(mSearchService.getSuggestedWords(word,contentType), BaseResult::getResult, callback);
    }

    private String getType(int resType) {
        switch (resType) {
            case ResType.TYPE_RADIO:
                return "10000";
            case ResType.TYPE_ALBUM:
                return "20000";
            case ResType.TYPE_AUDIO:
                return "30000";
            case ResType.TYPE_BROADCAST:
                return "50000";
            case ResType.TYPE_QQ_MUSIC:
                return "70000";
            case ResType.TYPE_TV:
                return "80000";
            case ResType.TYPE_FEATURE:
                return "90000";
            default:
        }
        return "";
    }

    /**
     * 获取热词
     *
     * @param callback 选填 回调，返回热词的列表
     */
    public void getHotWords(HttpCallback<List<String>> callback) {
        doHttpDeal(mSearchService.getHotWords(), baseResult -> baseResult.getResult().getDataList(), callback);
    }
}
