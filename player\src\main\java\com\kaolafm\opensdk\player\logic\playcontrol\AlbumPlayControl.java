package com.kaolafm.opensdk.player.logic.playcontrol;

import android.util.Log;

import com.kaolafm.opensdk.api.media.AudioRequest;
import com.kaolafm.opensdk.api.media.model.AudioFileInfo;
import com.kaolafm.opensdk.api.media.model.AudioPlayInfo;
import com.kaolafm.opensdk.api.media.model.ToneQuality;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.core.ijk.VideoView;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

import java.util.List;

/**
 * 专辑播放控制
 * <AUTHOR> shi qian
 */
public class AlbumPlayControl extends BasePlayControl {

    @Override
    public void start(int type, PlayItem playItem, VideoView videoView, boolean isPlayNow) {
        super.start(type, playItem, videoView, isPlayNow);
    }

    @Override
    ToneQuality getToneQuality(PlayItem playItem, List<AudioFileInfo> playListUrlInfos) {
        return null;
    }

    @Override
    String getPlayItemResourceType(PlayItem playItem) {
        return "mp3";
    }

    //加密音频播放测试
//            String testUrl = "https://ytmedia.radio.cn/CCYT%2F2021%2F08%2F02%2F1627892285de0d974ced6fefe3381f20be9d8cd70alc.mp3";
////            String testUrl = Environment.getExternalStorageDirectory().getAbsolutePath() +"/Download/decode_test.mp3";
////            String testUrl = Environment.getExternalStorageDirectory().getAbsolutePath() +"/Download/encode_test.mp3";
//            Log.i("start play", "testUrl:" + testUrl);
////            String testUrl = "/sdcard/Download/encode_test.mp3";
//            playItem.setPlayUrl(testUrl);
//            playWithPosition(playItem);
    // 由于添加了付费的内容播放，专辑内容需要单独请求播放的url地址
    @Override
    void requestPlayUrl(PlayItem playItem, OnGetPlayUrlData callback) {
        new AudioRequest().getAudioPlayInfo(playItem.getPlayUrlId(), new HttpCallback<AudioPlayInfo>() {
            @Override
            public void onSuccess(AudioPlayInfo audioPlayInfo) {
                List<AudioFileInfo> playList = audioPlayInfo.getPlayInfoList();
                if (playList == null) {
                    onError(new ApiException(PlayerConstants.ERROR_CODE_PLAY_LIST_URL_NULL, "playList is null"));
                    return;
                }
                //设置并回调播放地址
                setPlayUrl(playItem, playList);
                callback.onDataGet(playItem.getPlayUrl());
            }

            @Override
            public void onError(ApiException e) {
                Log.i("BasePlayControl", "getPlayUrl error:" + e.toString());
                stop();
                if (e.getCode() == PlayerConstants.ERROR_CODE_NO_COPYRIGHT){
                    // code - 50816, message - 因版权原因，暂时无法播放
                    if(mBasePlayControlListener != null){
                        mBasePlayControlListener.onPlayerFailed(playItem, PlayerConstants.ERROR_CODE_NO_COPYRIGHT, e.getCode());
                    }
                    return;
                }
                if (e.getCode() == PlayerConstants.ERROR_CODE_PLAY_LIST_URL_NULL) {
                    if(mBasePlayControlListener != null){
                        mBasePlayControlListener.onPlayerFailed(playItem, PlayerConstants.ERROR_CODE_PLAY_LIST_URL_NULL, 404);
                    }
                    return;
                }
                if(mBasePlayControlListener != null){
                    mBasePlayControlListener.onPlayerFailed(playItem, PlayerConstants.ERROR_CODE_PLAY_LIST_URL_DECODE, e.getCode());
                }
            }
        });
    }

//    /**
//     * 根据音质选择不同的url
//     */
//    private void setPlayUrl(PlayItem playItem) {
//        if (playItem == null) {
//            return;
//        }
//        int type = playItem.getType();
//        switch (type) {
//            case PlayerConstants.RESOURCES_TYPE_INVALID: {
//                InvalidPlayItem invalidPlayItem = (InvalidPlayItem) playItem;
//                setPlayUrl(playItem, null);
//            }
//            break;
//            case PlayerConstants.RESOURCES_TYPE_RADIO: {
//                RadioPlayItem radioPlayItem = (RadioPlayItem) playItem;
//                setPlayUrl(playItem, radioPlayItem.getPlayInfoList());
//            }
//            break;
//            case PlayerConstants.RESOURCES_TYPE_LIVING: {
//                LivePlayItem radioPlayItem = (LivePlayItem) playItem;
//                setPlayUrl(playItem, radioPlayItem.getPlayListUrlInfo());
//            }
//            break;
//            case PlayerConstants.RESOURCES_TYPE_BROADCAST: {
//                BroadcastPlayItem broadcastPlayItem = (BroadcastPlayItem) playItem;
//                if (broadcastPlayItem.isLiving()) {
//                    setPlayUrl(playItem, broadcastPlayItem.getPlayInfoList());
//                } else {
//                    setPlayUrl(playItem, broadcastPlayItem.getBackPlayInfoList());
//                }
//            }
//            break;
//            case PlayerConstants.RESOURCES_TYPE_TV: {
//                TVPlayItem tvPlayItem = (TVPlayItem) playItem;
//                if (tvPlayItem.isLiving()) {
//                    setPlayUrl(playItem, tvPlayItem.getPlayInfoList());
//                } else {
//                    setPlayUrl(playItem, tvPlayItem.getBackPlayInfoList());
//                }
//            }
//            break;
//            case PlayerConstants.RESOURCES_TYPE_FEATURE: {
//                FeaturePlayItem featurePlayItem = (FeaturePlayItem) playItem;
//                setPlayUrl(playItem, featurePlayItem.getPlayUrlDataList());
//            }
//            break;
//            default:
//                break;
//        }
//    }
//
//
//
//
//
//
//    private boolean isLiving(PlayItem playItem) {
//        if (playItem == null) {
//            return false;
//        }
//        int type = playItem.getType();
//        switch (type) {
//            case PlayerConstants.RESOURCES_TYPE_TV:
//            case PlayerConstants.RESOURCES_TYPE_BROADCAST:
//            case PlayerConstants.RESOURCES_TYPE_LIVE_STREAM: {
//                return mPlayItem.isLiving();
//            }
//            case PlayerConstants.RESOURCES_TYPE_LIVING: {
//                return true;
//            }
//            default:
//                return false;
//        }
//    }
}
